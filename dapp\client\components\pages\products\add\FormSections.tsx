import {
  Calendar,
  DollarSign,
  FileImage,
  Info,
  Package,
  Search,
  Settings,
  Shield,
  Tag,
  Truck,
} from "lucide-react";

import { FormSection, SectionId } from "@/types/form-section";

export const getFormSections = (
  completedSections: SectionId[] = []
): FormSection[] => {
  const currentSection =
    completedSections.length > 0
      ? completedSections[completedSections.length - 1]
      : "basic-info";

  return [
    {
      id: "basic-info",
      title: "Basic Information",
      icon: <Info size={18} />,
      isCompleted: completedSections.includes("basic-info"),
      isActive: currentSection === "basic-info",
    },
    {
      id: "pricing",
      title: "Pricing",
      icon: <DollarSign size={18} />,
      isCompleted: completedSections.includes("pricing"),
      isActive: currentSection === "pricing",
    },
    {
      id: "inventory",
      title: "Inventory",
      icon: <Package size={18} />,
      isCompleted: completedSections.includes("inventory"),
      isActive: currentSection === "inventory",
    },
    {
      id: "details",
      title: "Product Details",
      icon: <Tag size={18} />,
      isCompleted: completedSections.includes("details"),
      isActive: currentSection === "details",
    },
    {
      id: "media",
      title: "Images & Media",
      icon: <FileImage size={18} />,
      isCompleted: completedSections.includes("media"),
      isActive: currentSection === "media",
    },
    {
      id: "shipping",
      title: "Shipping",
      icon: <Truck size={18} />,
      isCompleted: completedSections.includes("shipping"),
      isActive: currentSection === "shipping",
      isOptional: true,
    },
    {
      id: "availability",
      title: "Availability",
      icon: <Calendar size={18} />,
      isCompleted: completedSections.includes("availability"),
      isActive: currentSection === "availability",
    },
    {
      id: "seo",
      title: "SEO & Marketing",
      icon: <Search size={18} />,
      isCompleted: completedSections.includes("seo"),
      isActive: currentSection === "seo",
      isOptional: true,
    },
    {
      id: "warranty",
      title: "Warranty & Returns",
      icon: <Shield size={18} />,
      isCompleted: completedSections.includes("warranty"),
      isActive: currentSection === "warranty",
      isOptional: true,
    },
    {
      id: "advanced",
      title: "Advanced Settings",
      icon: <Settings size={18} />,
      isCompleted: completedSections.includes("advanced"),
      isActive: currentSection === "advanced",
      isOptional: true,
    },
  ];
};
