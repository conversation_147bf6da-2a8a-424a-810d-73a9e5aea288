#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/Desktop/Learning/Codes/dapp/server/node_modules/.pnpm/prettier@3.5.3/node_modules/prettier/bin/node_modules:/mnt/c/Users/<USER>/Desktop/Learning/Codes/dapp/server/node_modules/.pnpm/prettier@3.5.3/node_modules/prettier/node_modules:/mnt/c/Users/<USER>/Desktop/Learning/Codes/dapp/server/node_modules/.pnpm/prettier@3.5.3/node_modules:/mnt/c/Users/<USER>/Desktop/Learning/Codes/dapp/server/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/Desktop/Learning/Codes/dapp/server/node_modules/.pnpm/prettier@3.5.3/node_modules/prettier/bin/node_modules:/mnt/c/Users/<USER>/Desktop/Learning/Codes/dapp/server/node_modules/.pnpm/prettier@3.5.3/node_modules/prettier/node_modules:/mnt/c/Users/<USER>/Desktop/Learning/Codes/dapp/server/node_modules/.pnpm/prettier@3.5.3/node_modules:/mnt/c/Users/<USER>/Desktop/Learning/Codes/dapp/server/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../prettier/bin/prettier.cjs" "$@"
else
  exec node  "$basedir/../prettier/bin/prettier.cjs" "$@"
fi
