"use client";

import { useEffect, useState } from "react";

import { usePathname } from "next/navigation";

import Sidebar from "@/components/layout/Sidebar";
import TopNavbar from "@/components/layout/TopNavbar";

// Pages that should not use the admin layout
const authPages = ["/login", "/help", "/forgot-password"];

// Event name for sidebar collapse state changes (must match the one in Sidebar.tsx)
const SIDEBAR_COLLAPSE_EVENT = "sidebarCollapseChange";

export default function AdminTemplate({
  children,
}: {
  children: React.ReactNode;
}) {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const pathname = usePathname();
  const isAuthPage = authPages.some((page) => pathname?.includes(page));

  // Listen for sidebar collapse events
  useEffect(() => {
    const handleSidebarCollapseChange = (event: any) => {
      setSidebarCollapsed(event.detail.collapsed);
    };

    window.addEventListener(
      SIDEBAR_COLLAPSE_EVENT,
      handleSidebarCollapseChange
    );

    return () => {
      window.removeEventListener(
        SIDEBAR_COLLAPSE_EVENT,
        handleSidebarCollapseChange
      );
    };
  }, []);

  if (isAuthPage) {
    return <>{children}</>;
  }

  return (
    <main className="flex min-h-screen w-full bg-gray-50">
      <Sidebar />

      {/* Add margin to account for the fixed sidebar - adjust based on collapsed state */}
      <div
        className={`flex flex-1 flex-col transition-all duration-200 ${
          sidebarCollapsed ? "ml-20" : "ml-72"
        }`}
      >
        <TopNavbar />

        <div className="container mx-auto max-w-screen-2xl flex-1 overflow-y-auto p-6 pt-4 dark:bg-gray-800">
          {children}
        </div>
      </div>
    </main>
  );
}
