"use client";

import { useState } from "react";
import { ChevronDown, ChevronRight } from "lucide-react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";

interface CollapsibleSectionProps {
  title: string;
  description?: string;
  icon?: React.ReactNode;
  children: React.ReactNode;
  defaultOpen?: boolean;
  isOptional?: boolean;
  borderColor?: string;
  completed?: boolean;
}

export const CollapsibleSection: React.FC<CollapsibleSectionProps> = ({
  title,
  description,
  icon,
  children,
  defaultOpen = false,
  isOptional = true,
  borderColor = "border-l-gray-500",
  completed = false,
}) => {
  const [isOpen, setIsOpen] = useState(defaultOpen);

  return (
    <Card className={`border-l-4 ${borderColor} transition-all duration-200 ${isOpen ? 'shadow-md' : 'shadow-sm'}`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            {icon}
            <div>
              <CardTitle className="flex items-center gap-2 text-lg">
                {title}
                {isOptional && (
                  <Badge variant="secondary" className="text-xs">
                    Optional
                  </Badge>
                )}
                {completed && (
                  <Badge variant="default" className="bg-green-100 text-green-800 text-xs">
                    ✓ Completed
                  </Badge>
                )}
              </CardTitle>
              {description && (
                <p className="mt-1 text-sm text-gray-600">
                  {description}
                </p>
              )}
            </div>
          </div>
          
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={() => setIsOpen(!isOpen)}
            className="flex items-center gap-1 text-gray-500 hover:text-gray-700"
          >
            {isOpen ? (
              <>
                <ChevronDown className="h-4 w-4" />
                <span className="text-sm">Hide</span>
              </>
            ) : (
              <>
                <ChevronRight className="h-4 w-4" />
                <span className="text-sm">Show</span>
              </>
            )}
          </Button>
        </div>
      </CardHeader>
      
      {isOpen && (
        <CardContent className="pt-0">
          {children}
        </CardContent>
      )}
    </Card>
  );
};
