import { Express } from "express";
import swaggerJsdoc from "swagger-jsdoc";
import swaggerUi from "swagger-ui-express";

/**
 * Swagger configuration options
 */
const swaggerOptions: swaggerJsdoc.Options = {
  definition: {
    openapi: "3.0.0",
    info: {
      title: "E-Commerce API Documentation",
      version: "1.0.0",
      description: "API documentation for the E-Commerce application",
      contact: {
        name: "API Support",
        email: "<EMAIL>",
      },
    },
    servers: [
      {
        url: "http://localhost:3010",
        description: "Development server",
      },
    ],
    components: {
      schemas: {
        // Import schemas from separate file
        ...(await import("./schemas").then((module) => module.default)),
      },
      responses: {
        BadRequest: {
          description: "Bad request",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  success: {
                    type: "boolean",
                    example: false,
                  },
                  message: {
                    type: "string",
                    example: "Invalid request data",
                  },
                },
              },
            },
          },
        },
        NotFound: {
          description: "Resource not found",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  success: {
                    type: "boolean",
                    example: false,
                  },
                  message: {
                    type: "string",
                    example: "Resource not found",
                  },
                },
              },
            },
          },
        },
        InternalServerError: {
          description: "Internal server error",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  success: {
                    type: "boolean",
                    example: false,
                  },
                  message: {
                    type: "string",
                    example: "Internal server error",
                  },
                },
              },
            },
          },
        },
      },
    },
  },
  apis: [
    "./src/routes/*.ts",
    "./src/models/*.ts",
    "./src/config/swagger/routes/*.ts",
  ],
};

/**
 * Configure Swagger for the Express application
 * @param app - Express application
 */
export const configSwagger = async (app: Express): Promise<void> => {
  try {
    // Generate Swagger specification
    const swaggerSpec = swaggerJsdoc(swaggerOptions);

    // Serve Swagger UI
    app.use(
      "/api-docs",
      swaggerUi.serve,
      swaggerUi.setup(swaggerSpec, {
        explorer: true,
        customCss: ".swagger-ui .topbar { display: none }",
        swaggerOptions: {
          persistAuthorization: true,
        },
      })
    );

    // Serve Swagger specification as JSON
    app.get("/api-docs.json", (req, res) => {
      res.setHeader("Content-Type", "application/json");
      res.send(swaggerSpec);
    });

    // Create a simple HTML page for testing the API
    app.get("/api-test", (_, res) => {
      res.send(`
        <!DOCTYPE html>
        <html>
          <head>
            <title>API Test</title>
            <style>
              body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
              h1 { color: #333; }
              .btn { background: #4CAF50; color: white; border: none; padding: 10px 15px; cursor: pointer; margin: 5px; }
              pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
            </style>
          </head>
          <body>
            <h1>API Test Page</h1>
            <p>Use the buttons below to test the API:</p>

            <button class="btn" onclick="testGetProducts()">Get Products</button>
            <div id="result" style="margin-top: 20px;"></div>

            <script>
              async function testGetProducts() {
                const resultDiv = document.getElementById('result');
                resultDiv.innerHTML = 'Loading...';

                try {
                  const response = await fetch('/api/products');
                  const data = await response.json();
                  resultDiv.innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
                } catch (error) {
                  resultDiv.innerHTML = '<pre>Error: ' + error.message + '</pre>';
                }
              }
            </script>
          </body>
        </html>
      `);
    });

    console.info("Swagger documentation available at /api-docs");
    console.info("API test page available at /api-test");
  } catch (error) {
    console.error("Failed to configure Swagger:", error);
    throw error;
  }
};
