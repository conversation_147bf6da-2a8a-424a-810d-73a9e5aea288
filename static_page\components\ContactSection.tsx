"use client";

import { useState } from "react";

import { motion } from "framer-motion";
import { FaFacebook, FaInstagram, FaLinkedin } from "react-icons/fa";

export default function ContactSection() {
  const [email, setEmail] = useState("");
  const [message, setMessage] = useState("");
  const [isSent, setIsSent] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log("Email:", email, "Message:", message);
    setIsSent(true);

    // Reset form after submission (you can remove this if needed)
    setTimeout(() => {
      setIsSent(false);
      setEmail("");
      setMessage("");
    }, 3000);
  };

  return (
    <motion.div
      className="flex h-screen flex-col items-center justify-center px-6"
      initial={{ opacity: 0, y: 50 }}
      animate={{
        opacity: 1,
        y: 0,
        transition: { duration: 0.8, ease: "easeOut" },
      }}
      exit={{
        opacity: 0,
        y: -50,
        transition: { duration: 0.6, ease: "easeIn" },
      }}
    >
      <h2 className="mb-6 text-3xl font-bold">Contact Us</h2>

      {/* Contact Info */}
      <div className="mb-6 text-center">
        <p>
          Email:{" "}
          <a
            href="mailto:<EMAIL>"
            className="text-blue-500 hover:underline"
          >
            <EMAIL>
          </a>
        </p>
        <p>
          Phone:{" "}
          <a href="tel:+1234567890" className="text-blue-500 hover:underline">
            ****** 567 890
          </a>
        </p>
      </div>

      {/* Social Media Links */}
      <div className="mb-6 flex gap-4">
        <a
          href="https://facebook.com"
          target="_blank"
          rel="noopener noreferrer"
        >
          <FaFacebook className="text-2xl text-blue-600 transition-transform hover:scale-110" />
        </a>
        <a
          href="https://instagram.com"
          target="_blank"
          rel="noopener noreferrer"
        >
          <FaInstagram className="text-2xl text-pink-500 transition-transform hover:scale-110" />
        </a>
        <a
          href="https://linkedin.com"
          target="_blank"
          rel="noopener noreferrer"
        >
          <FaLinkedin className="text-2xl text-blue-700 transition-transform hover:scale-110" />
        </a>
      </div>

      {/* Contact Form */}
      <form
        onSubmit={handleSubmit}
        className="flex w-full max-w-md flex-col gap-4"
      >
        <input
          type="email"
          placeholder="Your email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          required
          className="w-full rounded-lg border p-3 focus:ring focus:ring-blue-300"
        />
        <textarea
          placeholder="Tell us about your service request..."
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          required
          className="h-32 w-full rounded-lg border p-3 focus:ring focus:ring-blue-300"
        ></textarea>
        <button
          type="submit"
          className="rounded-lg bg-blue-600 py-2 text-white transition-colors hover:bg-blue-700"
          disabled={isSent}
        >
          {isSent ? "Sent!" : "Send Message"}
        </button>
      </form>
    </motion.div>
  );
}
