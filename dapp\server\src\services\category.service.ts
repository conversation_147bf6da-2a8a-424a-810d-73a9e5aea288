import { FilterQuery } from "mongoose";
import { Category, CategoryDocument } from "../models/category.model";
import {
  CreateCategoryDto,
  UpdateCategoryDto,
  CategoryFilters,
} from "../types/category.types";

/**
 * Service class for handling category-related business logic
 */
export class CategoryService {
  /**
   * Create a new category
   */
  async createCategory(categoryData: CreateCategoryDto): Promise<CategoryDocument> {
    try {
      // Generate slug if not provided
      const slug = this.generateSlug(categoryData.name);
      
      // Check if slug already exists
      const existingCategory = await Category.findOne({ slug });
      if (existingCategory) {
        throw new Error("A category with this name already exists");
      }

      // Get the next sort order
      const maxSortOrder = await Category.findOne({}, {}, { sort: { sortOrder: -1 } });
      const sortOrder = categoryData.sortOrder ?? (maxSortOrder?.sortOrder ?? 0) + 1;

      const category = new Category({
        ...categoryData,
        slug,
        sortOrder,
      });

      return await category.save();
    } catch (error) {
      throw this.handleError(error, "Error creating category");
    }
  }

  /**
   * Get all categories with optional filtering
   */
  async getCategories(filters: CategoryFilters = {}): Promise<CategoryDocument[]> {
    try {
      const query: FilterQuery<CategoryDocument> = {};

      // Apply filters
      if (filters.isActive !== undefined) {
        query.isActive = filters.isActive;
      }

      if (filters.parentId !== undefined) {
        query.parentId = filters.parentId || null;
      }

      if (filters.search) {
        query.$text = { $search: filters.search };
      }

      return await Category.find(query)
        .populate("parentId", "name slug")
        .sort({ sortOrder: 1, createdAt: -1 });
    } catch (error) {
      throw this.handleError(error, "Error fetching categories");
    }
  }

  /**
   * Get a category by ID
   */
  async getCategoryById(id: string): Promise<CategoryDocument | null> {
    try {
      return await Category.findById(id).populate("parentId", "name slug");
    } catch (error) {
      throw this.handleError(error, "Error fetching category");
    }
  }

  /**
   * Get a category by slug
   */
  async getCategoryBySlug(slug: string): Promise<CategoryDocument | null> {
    try {
      return await Category.findOne({ slug }).populate("parentId", "name slug");
    } catch (error) {
      throw this.handleError(error, "Error fetching category");
    }
  }

  /**
   * Update a category
   */
  async updateCategory(
    id: string,
    updateData: UpdateCategoryDto
  ): Promise<CategoryDocument | null> {
    try {
      // If name is being updated, regenerate slug
      if (updateData.name) {
        const newSlug = this.generateSlug(updateData.name);
        
        // Check if new slug conflicts with existing categories (excluding current one)
        const existingCategory = await Category.findOne({ 
          slug: newSlug, 
          _id: { $ne: id } 
        });
        
        if (existingCategory) {
          throw new Error("A category with this name already exists");
        }
        
        updateData = { ...updateData, slug: newSlug };
      }

      return await Category.findByIdAndUpdate(id, updateData, {
        new: true,
        runValidators: true,
      }).populate("parentId", "name slug");
    } catch (error) {
      throw this.handleError(error, "Error updating category");
    }
  }

  /**
   * Delete a category
   */
  async deleteCategory(id: string): Promise<boolean> {
    try {
      // Check if category has child categories
      const childCategories = await Category.find({ parentId: id });
      if (childCategories.length > 0) {
        throw new Error("Cannot delete category with subcategories");
      }

      // Check if category has products (you might want to implement this check)
      const category = await Category.findById(id);
      if (category && category.productCount > 0) {
        throw new Error("Cannot delete category with products");
      }

      const result = await Category.findByIdAndDelete(id);
      return !!result;
    } catch (error) {
      throw this.handleError(error, "Error deleting category");
    }
  }

  /**
   * Update product count for a category
   */
  async updateProductCount(categoryId: string, increment: number = 1): Promise<void> {
    try {
      await Category.findByIdAndUpdate(categoryId, {
        $inc: { productCount: increment },
      });
    } catch (error) {
      console.error("Error updating category product count:", error);
    }
  }

  /**
   * Generate slug from name
   */
  private generateSlug(name: string): string {
    return name
      .toLowerCase()
      .trim()
      .replace(/[^a-z0-9\s-]/g, "")
      .replace(/\s+/g, "-")
      .replace(/-+/g, "-")
      .replace(/^-|-$/g, "");
  }

  /**
   * Handle and format errors
   */
  private handleError(error: unknown, defaultMessage: string): Error {
    if (error instanceof Error) {
      return error;
    }
    return new Error(defaultMessage);
  }
}
