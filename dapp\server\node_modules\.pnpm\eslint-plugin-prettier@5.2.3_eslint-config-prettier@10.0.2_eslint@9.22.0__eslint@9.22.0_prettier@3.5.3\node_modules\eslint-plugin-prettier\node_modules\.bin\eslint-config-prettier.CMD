@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\Users\<USER>\Desktop\Learning\Codes\dapp\server\node_modules\.pnpm\eslint-config-prettier@10.0.2_eslint@9.22.0\node_modules\eslint-config-prettier\build\bin\node_modules;C:\Users\<USER>\Desktop\Learning\Codes\dapp\server\node_modules\.pnpm\eslint-config-prettier@10.0.2_eslint@9.22.0\node_modules\eslint-config-prettier\build\node_modules;C:\Users\<USER>\Desktop\Learning\Codes\dapp\server\node_modules\.pnpm\eslint-config-prettier@10.0.2_eslint@9.22.0\node_modules\eslint-config-prettier\node_modules;C:\Users\<USER>\Desktop\Learning\Codes\dapp\server\node_modules\.pnpm\eslint-config-prettier@10.0.2_eslint@9.22.0\node_modules;C:\Users\<USER>\Desktop\Learning\Codes\dapp\server\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\Users\<USER>\Desktop\Learning\Codes\dapp\server\node_modules\.pnpm\eslint-config-prettier@10.0.2_eslint@9.22.0\node_modules\eslint-config-prettier\build\bin\node_modules;C:\Users\<USER>\Desktop\Learning\Codes\dapp\server\node_modules\.pnpm\eslint-config-prettier@10.0.2_eslint@9.22.0\node_modules\eslint-config-prettier\build\node_modules;C:\Users\<USER>\Desktop\Learning\Codes\dapp\server\node_modules\.pnpm\eslint-config-prettier@10.0.2_eslint@9.22.0\node_modules\eslint-config-prettier\node_modules;C:\Users\<USER>\Desktop\Learning\Codes\dapp\server\node_modules\.pnpm\eslint-config-prettier@10.0.2_eslint@9.22.0\node_modules;C:\Users\<USER>\Desktop\Learning\Codes\dapp\server\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\..\..\..\..\eslint-config-prettier@10.0.2_eslint@9.22.0\node_modules\eslint-config-prettier\build\bin\cli.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\..\..\..\..\eslint-config-prettier@10.0.2_eslint@9.22.0\node_modules\eslint-config-prettier\build\bin\cli.js" %*
)
