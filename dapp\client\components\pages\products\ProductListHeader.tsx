"use client";

import React from "react";

import { FileDown, Plus } from "lucide-react";

import { ActionButtons } from "@/components/common/ActionButtons";
import { PageTitle } from "@/components/common/PageTitle";

export const ProductListHeader = () => {
  const handleExport = () => console.log("Exporting...");
  const handleCreate = () => console.log("Creating...");

  return (
    <div className="mb-6 flex flex-col items-start justify-between gap-3 sm:flex-row sm:items-center">
      <PageTitle title="Product list" />

      <ActionButtons
        buttons={[
          {
            label: "Export",
            onClick: handleExport,
            icon: <FileDown size={16} />,
            variant: "secondary",
          },
          {
            label: "Create new",
            onClick: handleCreate,
            icon: <Plus size={16} />,
            variant: "primary",
          },
        ]}
      />
    </div>
  );
};
