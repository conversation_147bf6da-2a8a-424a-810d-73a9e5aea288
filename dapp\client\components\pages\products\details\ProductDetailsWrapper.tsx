"use client";

import React, { useEffect, useState } from "react";

import { AlertCircle, RefreshCw } from "lucide-react";
import { useSearchParams } from "next/navigation";
import { toast } from "sonner";

import { Alert, AlertDescription } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { useProduct, useProductMutations } from "@/hooks/useProducts";

import { ProductDetailsActions } from "./ProductDetailsActions";
import { ProductDetailsContent } from "./ProductDetailsContent";

type ProductDetailsWrapperProps = {
  productId: string;
};

export const ProductDetailsWrapper = ({
  productId,
}: ProductDetailsWrapperProps) => {
  const searchParams = useSearchParams();
  const [isEditing, setIsEditing] = useState(false);
  const [localProduct, setLocalProduct] = useState(null);

  // Fetch product data from API
  const { product, loading, error, refetch } = useProduct(productId);
  const { updateProduct, loading: updating } = useProductMutations();

  // Check for edit mode in URL parameters
  useEffect(() => {
    const editMode = searchParams.get("edit") === "true";
    setIsEditing(editMode);
  }, [searchParams]);

  // Update local product state when API data changes
  useEffect(() => {
    if (product) {
      setLocalProduct(product);
    }
  }, [product]);

  const handleEditToggle = () => {
    setIsEditing(!isEditing);
  };

  const handleSave = async () => {
    if (!localProduct || !productId) return;

    try {
      await updateProduct(productId, localProduct);
      setIsEditing(false);
      // Refetch to get updated data
      refetch();
    } catch (error) {
      console.error("Failed to save product:", error);
    }
  };

  const handleCancel = () => {
    // Reset to original product data
    setLocalProduct(product);
    setIsEditing(false);
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="rounded-lg border bg-white p-4">
          <Skeleton className="mb-2 h-6 w-1/3" />
          <Skeleton className="h-4 w-1/4" />
        </div>
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
          {Array.from({ length: 4 }).map((_, index) => (
            <div key={index} className="rounded-lg border bg-white p-6">
              <Skeleton className="mb-4 h-5 w-1/4" />
              <div className="space-y-3">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-4 w-1/2" />
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <Alert className="mx-auto max-w-md">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription className="flex items-center justify-between">
          <span>Failed to load product: {error}</span>
          <Button
            variant="outline"
            size="sm"
            onClick={refetch}
            className="ml-2"
          >
            <RefreshCw className="mr-1 h-4 w-4" />
            Retry
          </Button>
        </AlertDescription>
      </Alert>
    );
  }

  if (!product) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <h3 className="text-lg font-medium text-gray-900">
            Product not found
          </h3>
          <p className="text-gray-500">
            The product you're looking for doesn't exist.
          </p>
          <Button variant="outline" onClick={refetch} className="mt-4">
            <RefreshCw className="mr-2 h-4 w-4" />
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Action Buttons */}
      <div className="flex items-center justify-between rounded-lg border bg-white p-4">
        <div>
          <h2 className="text-lg font-semibold text-gray-900">
            {localProduct?.name || product.name}
          </h2>
          <p className="text-sm text-gray-500">
            Product ID: #
            {(product._id || product.id || "")
              .toString()
              .slice(-6)
              .padStart(6, "0")}
          </p>
        </div>
        <ProductDetailsActions
          productId={productId}
          isEditing={isEditing}
          onEditToggle={handleEditToggle}
          onSave={handleSave}
          onCancel={handleCancel}
          loading={updating}
        />
      </div>

      {/* Product Content */}
      <ProductDetailsContent
        product={localProduct || product}
        isEditing={isEditing}
        onProductUpdate={setLocalProduct}
        onEditToggle={handleEditToggle}
        onSave={handleSave}
        onCancel={handleCancel}
      />
    </div>
  );
};
