import express, { type Express } from "express";
import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";

import { config } from "../config/load-env.config.ts";
import { configRoutes } from "../config/routes.config.ts";
import { connectToDatabase } from "../libs/connect-to-db.ts";

// Get the directory name of the current module (ES modules equivalent of __dirname)
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Start the Express server
 * @param server - Express application
 */
export const startServer = async (server: Express): Promise<void> => {
  try {
    // Connect to the database
    await connectToDatabase();
    console.info("Database connection established");

    // Configure API routes
    configRoutes(server);
    console.info("API routes configured");

    // Serve static files from the public directory (for development)
    server.use(express.static(path.join(__dirname, "../../public")));
    console.info("Static files configured");

    // Serve static files for client-side SPA
    // This should be the last route
    const clientDistPath = path.join(__dirname, "../../client/dist");
    const indexHtmlPath = path.join(clientDistPath, "index.html");

    // Check if the client/dist directory exists
    try {
      // Only set up the catch-all route if the client/dist directory exists
      if (fs.existsSync(clientDistPath)) {
        server.get("*", (_, res) => {
          res.sendFile(indexHtmlPath);
        });
        console.info(`Serving client SPA from ${clientDistPath}`);
      } else {
        console.info(
          `Client SPA directory not found at ${clientDistPath}, skipping catch-all route`
        );
      }
    } catch (error) {
      console.warn(
        `Error checking client SPA directory: ${error instanceof Error ? error.message : "Unknown error"}`
      );
    }
    console.info("Catch-all route configured");

    // Start the server
    server.listen(config.server.port, () => {
      console.info(`Server started successfully`);
      console.info(
        `API available at http://localhost:${config.server.port}/api`
      );
      console.info(
        `Swagger UI available at http://localhost:${config.server.port}/api-docs`
      );
      console.info(
        `API test page available at http://localhost:${config.server.port}/api-test`
      );
    });
  } catch (error) {
    console.error("Failed to start server:", error);
    process.exit(1);
  }
};
