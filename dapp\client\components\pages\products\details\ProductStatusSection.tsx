"use client";

import React, { useState } from "react";

import { Archive, Eye, EyeOff, Settings } from "lucide-react";

import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Product } from "@/types/product";

type ProductSectionProps = {
  product: Product;
  isEditing: boolean;
  onProductUpdate: (product: Product) => void;
};

export const ProductStatusSection = ({
  product,
  isEditing,
  onProductUpdate,
}: ProductSectionProps) => {
  const [editedProduct, setEditedProduct] = useState(product);

  const handleStatusChange = (status: string) => {
    setEditedProduct((prev) => ({
      ...prev,
      status: status as "active" | "draft" | "archived",
    }));
  };

  const handleVisibilityChange = (visible: boolean) => {
    // This would control if the product is visible in the store
    console.log("Visibility changed:", visible);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-700";
      case "draft":
        return "bg-yellow-100 text-yellow-700";
      case "archived":
        return "bg-gray-100 text-gray-700";
      default:
        return "bg-gray-100 text-gray-700";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "active":
        return Eye;
      case "draft":
        return EyeOff;
      case "archived":
        return Archive;
      default:
        return Settings;
    }
  };

  const status = product.status || "active";
  const StatusIcon = getStatusIcon(status);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Settings className="h-5 w-5" />
          Status & Visibility
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Product Status */}
        <div>
          <Label htmlFor="status">Product Status</Label>
          {isEditing ? (
            <Select
              value={editedProduct.status || "active"}
              onValueChange={handleStatusChange}
            >
              <SelectTrigger className="mt-1">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="draft">Draft</SelectItem>
                <SelectItem value="archived">Archived</SelectItem>
              </SelectContent>
            </Select>
          ) : (
            <div className="mt-1 flex items-center gap-2">
              <Badge className={getStatusColor(status)}>
                <StatusIcon className="mr-1 h-3 w-3" />
                {status.charAt(0).toUpperCase() + status.slice(1)}
              </Badge>
            </div>
          )}
        </div>

        {/* Visibility Toggle */}
        <div className="flex items-center justify-between">
          <div>
            <Label htmlFor="visibility">Visible in Store</Label>
            <p className="text-sm text-gray-500">
              Controls if customers can see this product
            </p>
          </div>
          <Switch
            id="visibility"
            checked={status === "active"}
            onCheckedChange={handleVisibilityChange}
            disabled={!isEditing}
          />
        </div>

        {/* Status Information */}
        {!isEditing && (
          <div className="space-y-2 rounded-md bg-gray-50 p-3">
            <h4 className="text-sm font-medium">Status Information</h4>
            <div className="space-y-1 text-sm text-gray-600">
              {status === "active" && (
                <>
                  <p>• Product is live and visible to customers</p>
                  <p>• Available for purchase</p>
                  <p>• Included in search results</p>
                </>
              )}
              {status === "draft" && (
                <>
                  <p>• Product is hidden from customers</p>
                  <p>• Not available for purchase</p>
                  <p>• Only visible to administrators</p>
                </>
              )}
              {status === "archived" && (
                <>
                  <p>• Product is archived and hidden</p>
                  <p>• Not available for purchase</p>
                  <p>• Preserved for historical data</p>
                </>
              )}
            </div>
          </div>
        )}

        {/* Created/Updated Info */}
        {!isEditing && (
          <div className="space-y-2 text-sm text-gray-500">
            <div>
              <span className="font-medium">Created:</span> Jan 15, 2024
            </div>
            <div>
              <span className="font-medium">Last Updated:</span> Jan 20, 2024
            </div>
            <div>
              <span className="font-medium">Views:</span> 1,234
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
