"use client";

import { useState } from "react";

import {
  BarChart3,
  Check,
  ChevronDown,
  ChevronUp,
  Edit,
  Eye,
  EyeOff,
  Filter,
  Grid3X3,
  MoreHorizontal,
  Palette,
  Plus,
  Search,
  Sparkles,
  Trash2,
  <PERSON><PERSON>dingUp,
  X,
} from "lucide-react";
import { toast } from "sonner";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";

// Utility function to generate slug from name
const generateSlug = (name: string): string => {
  return name
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, "-")
    .replace(/(^-|-$)/g, "");
};

export type Color = {
  id: string;
  name: string;
  description: string;
  slug: string;
  hexCode: string;
  rgbCode?: string;
  category: "primary" | "secondary" | "neutral" | "accent" | "pastel";
  isActive: boolean;
  isPopular: boolean;
  productCount: number;
  sortOrder: number;
  createdAt: string;
  updatedAt: string;
};

// Mock colors with enhanced data
const mockColors: Color[] = [
  {
    id: "color-1",
    name: "Classic Black",
    description:
      "Timeless black color perfect for elegant and professional products",
    slug: "classic-black",
    hexCode: "#000000",
    rgbCode: "rgb(0, 0, 0)",
    category: "neutral",
    isActive: true,
    isPopular: true,
    productCount: 245,
    sortOrder: 1,
    createdAt: "2024-01-01T00:00:00Z",
    updatedAt: "2024-01-15T10:30:00Z",
  },
  {
    id: "color-2",
    name: "Pure White",
    description: "Clean white color ideal for minimalist and modern designs",
    slug: "pure-white",
    hexCode: "#FFFFFF",
    rgbCode: "rgb(255, 255, 255)",
    category: "neutral",
    isActive: true,
    isPopular: true,
    productCount: 198,
    sortOrder: 2,
    createdAt: "2024-01-02T00:00:00Z",
    updatedAt: "2024-01-14T16:45:00Z",
  },
  {
    id: "color-3",
    name: "Ocean Blue",
    description:
      "Deep blue reminiscent of ocean depths, perfect for tech products",
    slug: "ocean-blue",
    hexCode: "#1E40AF",
    rgbCode: "rgb(30, 64, 175)",
    category: "primary",
    isActive: true,
    isPopular: true,
    productCount: 156,
    sortOrder: 3,
    createdAt: "2024-01-03T00:00:00Z",
    updatedAt: "2024-01-13T12:20:00Z",
  },
  {
    id: "color-4",
    name: "Forest Green",
    description:
      "Natural green color representing sustainability and eco-friendliness",
    slug: "forest-green",
    hexCode: "#059669",
    rgbCode: "rgb(5, 150, 105)",
    category: "secondary",
    isActive: true,
    isPopular: false,
    productCount: 89,
    sortOrder: 4,
    createdAt: "2024-01-04T00:00:00Z",
    updatedAt: "2024-01-12T09:15:00Z",
  },
  {
    id: "color-5",
    name: "Sunset Orange",
    description: "Vibrant orange that captures attention and energy",
    slug: "sunset-orange",
    hexCode: "#EA580C",
    rgbCode: "rgb(234, 88, 12)",
    category: "accent",
    isActive: false,
    isPopular: false,
    productCount: 34,
    sortOrder: 5,
    createdAt: "2024-01-05T00:00:00Z",
    updatedAt: "2024-01-11T11:30:00Z",
  },
  {
    id: "color-6",
    name: "Soft Pink",
    description: "Gentle pink color perfect for beauty and lifestyle products",
    slug: "soft-pink",
    hexCode: "#F472B6",
    rgbCode: "rgb(244, 114, 182)",
    category: "pastel",
    isActive: true,
    isPopular: true,
    productCount: 67,
    sortOrder: 6,
    createdAt: "2024-01-06T00:00:00Z",
    updatedAt: "2024-01-10T08:45:00Z",
  },
];

type ColorManagerProps = {
  initialColors?: Color[];
  onColorsChange?: (colors: Color[]) => void;
};

/**
 * Enhanced component for managing product colors with professional UI
 */
export const ColorManagerEnhanced = ({
  initialColors = mockColors,
  onColorsChange,
}: ColorManagerProps) => {
  const [colors, setColors] = useState<Color[]>(initialColors);
  const [newColor, setNewColor] = useState<Partial<Color>>({
    name: "",
    description: "",
    hexCode: "#000000",
    category: "neutral",
    isActive: true,
    isPopular: false,
  });
  const [editingColorId, setEditingColorId] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [filterCategory, setFilterCategory] = useState<string>("all");
  const [filterStatus, setFilterStatus] = useState<string>("all");
  const [sortBy, setSortBy] = useState<string>("name");
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [showAddForm, setShowAddForm] = useState(false);

  // Notify parent component of changes
  const notifyChange = (updatedColors: Color[]) => {
    setColors(updatedColors);
    onColorsChange?.(updatedColors);
  };

  // Filter and sort colors
  const filteredColors = colors
    .filter((color) => {
      const matchesSearch = color.name
        .toLowerCase()
        .includes(searchQuery.toLowerCase());
      const matchesCategory =
        filterCategory === "all" || color.category === filterCategory;
      const matchesStatus =
        filterStatus === "all" ||
        (filterStatus === "active" && color.isActive) ||
        (filterStatus === "inactive" && !color.isActive);
      return matchesSearch && matchesCategory && matchesStatus;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case "name":
          return a.name.localeCompare(b.name);
        case "products":
          return b.productCount - a.productCount;
        case "created":
          return (
            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
          );
        default:
          return a.sortOrder - b.sortOrder;
      }
    });

  // Statistics
  const stats = {
    total: colors.length,
    active: colors.filter((c) => c.isActive).length,
    popular: colors.filter((c) => c.isPopular).length,
    totalProducts: colors.reduce((sum, c) => sum + c.productCount, 0),
  };

  // Add a new color
  const handleAddColor = () => {
    if (!newColor.name) {
      toast.error("Color name is required");
      return;
    }

    if (!newColor.hexCode || !/^#[0-9A-F]{6}$/i.test(newColor.hexCode)) {
      toast.error("Valid hex color code is required (e.g., #FF0000)");
      return;
    }

    const slug = generateSlug(newColor.name);

    // Check if slug already exists
    if (colors.some((color) => color.slug === slug)) {
      toast.error("A color with this name already exists");
      return;
    }

    // Convert hex to RGB
    const hex = newColor.hexCode.replace("#", "");
    const r = parseInt(hex.substr(0, 2), 16);
    const g = parseInt(hex.substr(2, 2), 16);
    const b = parseInt(hex.substr(4, 2), 16);
    const rgbCode = `rgb(${r}, ${g}, ${b})`;

    const newColorWithId: Color = {
      id: `color-${Date.now()}`,
      name: newColor.name,
      description: newColor.description || "",
      slug,
      hexCode: newColor.hexCode.toUpperCase(),
      rgbCode,
      category: newColor.category || "neutral",
      isActive: newColor.isActive ?? true,
      isPopular: newColor.isPopular ?? false,
      productCount: 0,
      sortOrder: colors.length + 1,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    const updatedColors = [...colors, newColorWithId];
    notifyChange(updatedColors);
    setNewColor({
      name: "",
      description: "",
      hexCode: "#000000",
      category: "neutral",
      isActive: true,
      isPopular: false,
    });
    setShowAddForm(false);
    toast.success("Color added successfully!");
  };

  return (
    <div className="space-y-6">
      {/* Statistics Dashboard */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">
              Total Colors
            </CardTitle>
            <Palette className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">
              {stats.total}
            </div>
            <div className="mt-1 flex items-center text-xs text-gray-500">
              <TrendingUp className="mr-1 h-3 w-3 text-green-500" />
              <span>+2 this month</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">
              Active Colors
            </CardTitle>
            <Eye className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">
              {stats.active}
            </div>
            <div className="mt-1 text-xs text-gray-500">
              {Math.round((stats.active / stats.total) * 100)}% of total
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">
              Popular Colors
            </CardTitle>
            <Sparkles className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">
              {stats.popular}
            </div>
            <div className="mt-1 text-xs text-gray-500">High demand colors</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">
              Total Products
            </CardTitle>
            <BarChart3 className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">
              {stats.totalProducts}
            </div>
            <div className="mt-1 text-xs text-gray-500">
              Using color variants
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Controls */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div className="flex flex-1 gap-2">
          <div className="relative max-w-sm flex-1">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
            <Input
              placeholder="Search colors..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          <Select value={filterCategory} onValueChange={setFilterCategory}>
            <SelectTrigger className="w-[140px]">
              <Filter className="mr-2 h-4 w-4" />
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Categories</SelectItem>
              <SelectItem value="primary">Primary</SelectItem>
              <SelectItem value="secondary">Secondary</SelectItem>
              <SelectItem value="neutral">Neutral</SelectItem>
              <SelectItem value="accent">Accent</SelectItem>
              <SelectItem value="pastel">Pastel</SelectItem>
            </SelectContent>
          </Select>
          <Select value={filterStatus} onValueChange={setFilterStatus}>
            <SelectTrigger className="w-[120px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="inactive">Inactive</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="flex gap-2">
          <Select value={sortBy} onValueChange={setSortBy}>
            <SelectTrigger className="w-[140px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="name">Sort by Name</SelectItem>
              <SelectItem value="products">Sort by Products</SelectItem>
              <SelectItem value="created">Sort by Created</SelectItem>
            </SelectContent>
          </Select>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setViewMode(viewMode === "grid" ? "list" : "grid")}
          >
            <Grid3X3 className="h-4 w-4" />
          </Button>
          <Button
            size="sm"
            onClick={() => setShowAddForm(!showAddForm)}
            className="bg-purple-600 hover:bg-purple-700"
          >
            <Plus className="mr-2 h-4 w-4" />
            Add Color
          </Button>
        </div>
      </div>

      {/* Add Color Form */}
      {showAddForm && (
        <Card className="border-purple-200 bg-purple-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-purple-900">
              <Plus className="h-5 w-5" />
              Add New Color
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <div>
                <Label htmlFor="color-name">Color Name *</Label>
                <Input
                  id="color-name"
                  value={newColor.name}
                  onChange={(e) =>
                    setNewColor({ ...newColor, name: e.target.value })
                  }
                  placeholder="e.g., Ocean Blue"
                />
              </div>
              <div>
                <Label htmlFor="color-hex">Hex Code *</Label>
                <div className="flex gap-2">
                  <Input
                    id="color-hex"
                    value={newColor.hexCode}
                    onChange={(e) =>
                      setNewColor({ ...newColor, hexCode: e.target.value })
                    }
                    placeholder="#000000"
                    className="flex-1"
                  />
                  <div
                    className="h-10 w-16 rounded border-2 border-gray-300"
                    style={{ backgroundColor: newColor.hexCode }}
                  />
                </div>
              </div>
            </div>
            <div>
              <Label htmlFor="color-description">Description</Label>
              <Textarea
                id="color-description"
                value={newColor.description}
                onChange={(e) =>
                  setNewColor({ ...newColor, description: e.target.value })
                }
                placeholder="Describe this color and its best use cases..."
                rows={2}
              />
            </div>
            <div className="grid gap-4 md:grid-cols-3">
              <div>
                <Label htmlFor="color-category">Category</Label>
                <Select
                  value={newColor.category}
                  onValueChange={(value) =>
                    setNewColor({
                      ...newColor,
                      category: value as Color["category"],
                    })
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="primary">Primary</SelectItem>
                    <SelectItem value="secondary">Secondary</SelectItem>
                    <SelectItem value="neutral">Neutral</SelectItem>
                    <SelectItem value="accent">Accent</SelectItem>
                    <SelectItem value="pastel">Pastel</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="color-active"
                  checked={newColor.isActive}
                  onCheckedChange={(checked) =>
                    setNewColor({ ...newColor, isActive: checked })
                  }
                />
                <Label htmlFor="color-active">Active</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="color-popular"
                  checked={newColor.isPopular}
                  onCheckedChange={(checked) =>
                    setNewColor({ ...newColor, isPopular: checked })
                  }
                />
                <Label htmlFor="color-popular">Popular</Label>
              </div>
            </div>
            <div className="flex gap-2 pt-2">
              <Button
                onClick={handleAddColor}
                className="bg-purple-600 hover:bg-purple-700"
              >
                <Check className="mr-2 h-4 w-4" />
                Add Color
              </Button>
              <Button variant="outline" onClick={() => setShowAddForm(false)}>
                <X className="mr-2 h-4 w-4" />
                Cancel
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Colors Grid/List */}
      <div
        className={
          viewMode === "grid"
            ? "grid gap-4 md:grid-cols-2 lg:grid-cols-3"
            : "space-y-3"
        }
      >
        {filteredColors.map((color) => (
          <Card
            key={color.id}
            className={`transition-all hover:shadow-md ${
              !color.isActive ? "opacity-60" : ""
            }`}
          >
            <CardContent className="p-4">
              <div className="flex items-start justify-between">
                <div className="flex items-center gap-3">
                  <div
                    className="h-12 w-12 rounded-lg border-2 border-gray-200 shadow-sm"
                    style={{ backgroundColor: color.hexCode }}
                  />
                  <div>
                    <h3 className="font-semibold text-gray-900">
                      {color.name}
                    </h3>
                    <p className="text-sm text-gray-600">{color.hexCode}</p>
                    <div className="mt-1 flex items-center gap-2">
                      <Badge
                        variant="outline"
                        className={`text-xs ${
                          color.category === "primary"
                            ? "border-blue-200 text-blue-700"
                            : color.category === "secondary"
                              ? "border-green-200 text-green-700"
                              : color.category === "neutral"
                                ? "border-gray-200 text-gray-700"
                                : color.category === "accent"
                                  ? "border-orange-200 text-orange-700"
                                  : "border-pink-200 text-pink-700"
                        }`}
                      >
                        {color.category}
                      </Badge>
                      {color.isPopular && (
                        <Badge variant="secondary" className="text-xs">
                          <Sparkles className="mr-1 h-3 w-3" />
                          Popular
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem>
                      <Edit className="mr-2 h-4 w-4" />
                      Edit
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      {color.isActive ? (
                        <>
                          <EyeOff className="mr-2 h-4 w-4" />
                          Deactivate
                        </>
                      ) : (
                        <>
                          <Eye className="mr-2 h-4 w-4" />
                          Activate
                        </>
                      )}
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem className="text-red-600">
                      <Trash2 className="mr-2 h-4 w-4" />
                      Delete
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
              {color.description && (
                <p className="mt-2 text-sm text-gray-600">
                  {color.description}
                </p>
              )}
              <div className="mt-3 flex items-center justify-between text-sm text-gray-500">
                <span>{color.productCount} products</span>
                <span
                  className={color.isActive ? "text-green-600" : "text-red-600"}
                >
                  {color.isActive ? "Active" : "Inactive"}
                </span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredColors.length === 0 && (
        <Card>
          <CardContent className="py-12 text-center">
            <Palette className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-4 text-lg font-semibold text-gray-900">
              No colors found
            </h3>
            <p className="mt-2 text-gray-600">
              {searchQuery || filterCategory !== "all" || filterStatus !== "all"
                ? "Try adjusting your search or filters"
                : "Get started by adding your first color"}
            </p>
            {!searchQuery &&
              filterCategory === "all" &&
              filterStatus === "all" && (
                <Button
                  className="mt-4 bg-purple-600 hover:bg-purple-700"
                  onClick={() => setShowAddForm(true)}
                >
                  <Plus className="mr-2 h-4 w-4" />
                  Add Your First Color
                </Button>
              )}
          </CardContent>
        </Card>
      )}
    </div>
  );
};
