"use client";

import React, { useState } from "react";

import {
  AlertTriangle,
  Calendar,
  CheckCircle,
  Download,
  Eye,
  Filter,
  Image as ImageIcon,
  MessageSquare,
  MoreHorizontal,
  Package,
  Search,
  Star,
  ThumbsDown,
  ThumbsUp,
  User,
  XCircle,
} from "lucide-react";

import { PageHeaderWrapper } from "@/components/common/PageHeaderWrapper";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { mockCustomers } from "@/constants/products";
import {
  calculateAverageRating,
  getRatingDistribution,
  mockReviews,
} from "@/constants/reviews";

export default function Reviews() {
  const [searchTerm, setSearchTerm] = useState("");
  const [ratingFilter, setRatingFilter] = useState("all");
  const [statusFilter, setStatusFilter] = useState("all");

  // Calculate review statistics
  const totalReviews = mockReviews.length;
  const averageRating = calculateAverageRating(mockReviews);
  const ratingDistribution = getRatingDistribution(mockReviews);
  const pendingReviews = mockReviews.filter((review) => !review.title).length; // Mock pending logic

  // Filter reviews based on search and filters
  const filteredReviews = mockReviews.filter((review) => {
    const customer = mockCustomers.find((c) => c.id === review.userId);
    const matchesSearch =
      review.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      review.comment?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      customer?.name.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesRating =
      ratingFilter === "all" || review.rating.toString() === ratingFilter;
    const matchesStatus =
      statusFilter === "all" ||
      (statusFilter === "pending" && !review.title) ||
      (statusFilter === "published" && review.title);

    return matchesSearch && matchesRating && matchesStatus;
  });

  const getCustomerInfo = (userId: string) => {
    return (
      mockCustomers.find((customer) => customer.id === userId) || {
        id: userId,
        name: "Unknown Customer",
        email: "<EMAIL>",
        avatar: "/avatars/default.jpg",
      }
    );
  };

  const getStatusColor = (review: any) => {
    if (!review.title) return "bg-yellow-100 text-yellow-800"; // Pending
    if (review.rating >= 4) return "bg-green-100 text-green-800"; // Positive
    if (review.rating <= 2) return "bg-red-100 text-red-800"; // Negative
    return "bg-blue-100 text-blue-800"; // Neutral
  };

  const getStatusText = (review: any) => {
    if (!review.title) return "Pending";
    if (review.rating >= 4) return "Positive";
    if (review.rating <= 2) return "Negative";
    return "Neutral";
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${
          i < rating ? "fill-yellow-400 text-yellow-400" : "text-gray-300"
        }`}
      />
    ));
  };

  return (
    <>
      <PageHeaderWrapper
        title="Reviews & Feedback"
        description="Manage customer reviews, ratings, and feedback across all products"
      >
        <div className="flex gap-2">
          <Button variant="outline" size="sm">
            <Filter className="mr-2 h-4 w-4" />
            Advanced Filters
          </Button>

          <Button variant="outline" size="sm">
            <Download className="mr-2 h-4 w-4" />
            Export Reviews
          </Button>
        </div>
      </PageHeaderWrapper>

      <div className="container mx-auto mt-6 space-y-6">
        {/* Reviews Overview Stats */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-3">
                <div className="rounded-full bg-blue-100 p-2">
                  <MessageSquare className="h-5 w-5 text-blue-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600">
                    Total Reviews
                  </p>
                  <p className="text-2xl font-bold text-gray-900">
                    {totalReviews}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-3">
                <div className="rounded-full bg-yellow-100 p-2">
                  <Star className="h-5 w-5 text-yellow-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600">
                    Average Rating
                  </p>
                  <div className="flex items-center gap-2">
                    <p className="text-2xl font-bold text-gray-900">
                      {averageRating}
                    </p>
                    <div className="flex">
                      {renderStars(Math.round(averageRating))}
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-3">
                <div className="rounded-full bg-green-100 p-2">
                  <ThumbsUp className="h-5 w-5 text-green-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600">
                    Positive Reviews
                  </p>
                  <p className="text-2xl font-bold text-gray-900">
                    {ratingDistribution[4] + ratingDistribution[5]}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-3">
                <div className="rounded-full bg-orange-100 p-2">
                  <AlertTriangle className="h-5 w-5 text-orange-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600">
                    Pending Review
                  </p>
                  <p className="text-2xl font-bold text-gray-900">
                    {pendingReviews}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filters */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
              <div className="flex flex-1 gap-4">
                <div className="relative max-w-sm flex-1">
                  <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
                  <Input
                    placeholder="Search reviews, customers, or products..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>

                <Select value={ratingFilter} onValueChange={setRatingFilter}>
                  <SelectTrigger className="w-32">
                    <SelectValue placeholder="Rating" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Ratings</SelectItem>
                    <SelectItem value="5">5 Stars</SelectItem>
                    <SelectItem value="4">4 Stars</SelectItem>
                    <SelectItem value="3">3 Stars</SelectItem>
                    <SelectItem value="2">2 Stars</SelectItem>
                    <SelectItem value="1">1 Star</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-32">
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="published">Published</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="text-sm text-gray-500">
                Showing {filteredReviews.length} of {totalReviews} reviews
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Reviews List */}
        <div className="space-y-4">
          {filteredReviews.map((review) => {
            const customer = getCustomerInfo(review.userId);

            return (
              <Card
                key={review.id}
                className="transition-shadow hover:shadow-md"
              >
                <CardContent className="p-6">
                  <div className="space-y-4">
                    {/* Review Header */}
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-4">
                        <Avatar className="h-12 w-12">
                          <AvatarImage
                            src={customer.avatar}
                            alt={customer.name}
                          />
                          <AvatarFallback>
                            {customer.name
                              .split(" ")
                              .map((n) => n[0])
                              .join("")}
                          </AvatarFallback>
                        </Avatar>

                        <div>
                          <div className="flex items-center gap-2">
                            <h4 className="font-medium text-gray-900">
                              {customer.name}
                            </h4>
                            <Badge
                              className={`text-xs ${getStatusColor(review)}`}
                            >
                              {getStatusText(review)}
                            </Badge>
                          </div>
                          <div className="mt-1 flex items-center gap-2">
                            <div className="flex">
                              {renderStars(review.rating)}
                            </div>
                            <span className="text-sm text-gray-500">
                              {new Date(review.createdAt).toLocaleDateString(
                                "en-US",
                                {
                                  year: "numeric",
                                  month: "short",
                                  day: "numeric",
                                }
                              )}
                            </span>
                          </div>
                        </div>
                      </div>

                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem>
                            <Eye className="mr-2 h-4 w-4" />
                            View Details
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <User className="mr-2 h-4 w-4" />
                            View Customer
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Package className="mr-2 h-4 w-4" />
                            View Product
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem>
                            <CheckCircle className="mr-2 h-4 w-4" />
                            Approve Review
                          </DropdownMenuItem>
                          <DropdownMenuItem className="text-red-600">
                            <XCircle className="mr-2 h-4 w-4" />
                            Hide Review
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>

                    {/* Review Content */}
                    <div className="space-y-3">
                      {review.title && (
                        <h5 className="font-medium text-gray-900">
                          {review.title}
                        </h5>
                      )}

                      {review.comment && (
                        <p className="leading-relaxed text-gray-700">
                          {review.comment}
                        </p>
                      )}

                      {review.images && review.images.length > 0 && (
                        <div className="flex gap-2">
                          <ImageIcon className="mt-1 h-4 w-4 text-gray-400" />
                          <span className="text-sm text-gray-500">
                            {review.images.length} image
                            {review.images.length > 1 ? "s" : ""} attached
                          </span>
                        </div>
                      )}
                    </div>

                    {/* Review Footer */}
                    <div className="flex items-center justify-between border-t border-gray-100 pt-3">
                      <div className="flex items-center gap-4 text-sm text-gray-500">
                        <span>Review ID: {review.id}</span>
                        <span>Product ID: {review.productId}</span>
                      </div>

                      <div className="flex gap-2">
                        <Button variant="outline" size="sm">
                          <MessageSquare className="mr-2 h-4 w-4" />
                          Reply
                        </Button>
                        <Button variant="outline" size="sm">
                          <ThumbsUp className="mr-2 h-4 w-4" />
                          Helpful
                        </Button>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Empty State */}
        {filteredReviews.length === 0 && (
          <Card>
            <CardContent className="p-12 text-center">
              <MessageSquare className="mx-auto mb-4 h-12 w-12 text-gray-400" />
              <h3 className="mb-2 text-lg font-medium text-gray-900">
                No reviews found
              </h3>
              <p className="text-gray-500">
                {searchTerm || ratingFilter !== "all" || statusFilter !== "all"
                  ? "Try adjusting your search criteria or filters."
                  : "No customer reviews have been submitted yet."}
              </p>
            </CardContent>
          </Card>
        )}
      </div>
    </>
  );
}
