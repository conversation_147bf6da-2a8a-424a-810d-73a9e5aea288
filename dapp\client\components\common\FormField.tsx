import React from "react";

import { Label } from "@/components/ui/label";
import { cn } from "@/lib/utils";

type FormFieldProps = {
  id: string;
  label: string;
  error?: string;
  className?: string;
  optional?: boolean;
  children: React.ReactNode;
};

export const FormField: React.FC<FormFieldProps> = ({
  id,
  label,
  error,
  className,
  optional = false,
  children,
}) => {
  return (
    <div className={cn("space-y-2", className)}>
      <div className="flex items-center justify-between">
        <Label htmlFor={id} className="text-sm font-medium">
          {label}

          {optional && (
            <span className="ml-1 text-xs text-gray-500">(Optional)</span>
          )}
        </Label>
        {error && (
          <span className="text-xs font-medium text-destructive">{error}</span>
        )}
      </div>
      {children}
    </div>
  );
};
