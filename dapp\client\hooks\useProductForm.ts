import { zodResolver } from "@hookform/resolvers/zod";

import { useForm } from "react-hook-form";

import { ProductFormData, productSchema } from "@/schemas/productSchema";

export const useProductForm = () => {
  return useForm<ProductFormData>({
    resolver: zodResolver(productSchema),
    defaultValues: {
      currency: "EUR",
      condition: "new",
      stock: 1,
      isPublished: true,
      status: "draft",
      ageRestriction: "none",
      freeShipping: false,
    },
    mode: "onChange",
  });
};
