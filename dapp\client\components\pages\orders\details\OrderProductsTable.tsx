import { Badge } from "@/components/ui/badge";
import { Card } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { orderProducts } from "@/constants/products";

import { OrderProductRow } from "./OrderProductRow";
import { SubTotalRow } from "./SubTotalRow";

export const OrderProductsTable = () => {
  return (
    <div className="space-y-4">
      <Card className="rounded-lg border border-gray-200 bg-white">
        <table className="w-full text-sm">
          <thead>
            <tr className="border-b text-gray-500">
              <th className="p-4 text-left">Product</th>
              <th className="p-4 text-center">Quantity</th>
              <th className="p-4 text-center">Unit Price</th>
              <th className="p-4 text-right">Total</th>
            </tr>
          </thead>

          <tbody>
            {orderProducts.map((product, index) => (
              <OrderProductRow
                key={index}
                imageSrc={product.imageSrc}
                name={product.name}
                quantity={product.quantity}
                unitPrice={product.unitPrice}
                total={product.total}
              />
            ))}
          </tbody>
        </table>

        <div className="flex justify-end border-t p-4">
          <div className="w-60 space-y-2">
            <SubTotalRow label="Subtotal" value="$973.35" />

            <SubTotalRow label="Shipping cost" value="$10.00" />

            <Separator />

            <div className="flex flex-col gap-2">
              <div className="flex justify-between">
                <span className="font-medium">Total:</span>

                <span className="font-bold">$983.00</span>
              </div>

              <Badge className="cursor-pointer rounded bg-green-100 px-2 font-normal text-green-700 hover:bg-green-200">
                <p className="w-full text-center">Payment made</p>
              </Badge>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
};
