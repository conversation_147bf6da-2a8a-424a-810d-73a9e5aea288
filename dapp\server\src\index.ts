import "dotenv/config";
import express from "express";

import { configMiddleware } from "./config/middleware.config.ts";
import { configSwagger } from "./config/swagger/swagger.config.ts";
import { startServer } from "./libs/start-server.ts";

/**
 * Main async function to start the application
 */
async function startApplication(): Promise<void> {
  try {
    console.info("Starting application...");

    // Create Express application
    const server = express();
    console.info("Express application created");

    // Configure middleware (CORS, Helmet, etc.)
    configMiddleware(server);
    console.info("Middleware configured");

    // Configure Swagger documentation
    await configSwagger(server);
    console.info("Swagger documentation configured");

    // Start the server and connect to the database
    await startServer(server);
  } catch (error) {
    console.error("Failed to start application:", error);
    if (error instanceof Error) {
      console.error(`Error details: ${error.message}`);
      console.error(`Stack trace: ${error.stack}`);
    }
    process.exit(1);
  }
}

// Handle uncaught exceptions
process.on("uncaughtException", (error) => {
  console.error("Uncaught Exception:", error);
  process.exit(1);
});

// Handle unhandled promise rejections
process.on("unhandledRejection", (reason, promise) => {
  console.error("Unhandled Rejection at:", promise, "reason:", reason);
  process.exit(1);
});

// Start the application
console.info("Initializing application...");
startApplication();
