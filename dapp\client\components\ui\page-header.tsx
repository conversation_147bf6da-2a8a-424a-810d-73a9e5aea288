import React from "react";

type PageHeaderProps = {
  heading: string;
  subheading?: string;
  children?: React.ReactNode;
};

export function PageHeader({ heading, subheading, children }: PageHeaderProps) {
  return (
    <div className="flex flex-col items-start justify-between gap-4 border-b pb-5 sm:flex-row sm:items-center sm:gap-0">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">{heading}</h1>
        {subheading && (
          <p className="text-muted-foreground">{subheading}</p>
        )}
      </div>
      {children && <div>{children}</div>}
    </div>
  );
}
