export type UserRole = "guest" | "buyer" | "seller" | "moderator" | "admin";

export type UserProfile = {
  name: string;
  avatar?: string;
  bio?: string;
  wishlistIds?: string[];
};

export type UserContact = {
  email: string;
  phone?: string;
  address?: string;
  location?: string;
};

export type UserAccount = {
  id: string;
  role: UserRole;
  joinedAt: string;
  isVerified: boolean;
};

export type User = UserAccount & UserProfile & UserContact;
