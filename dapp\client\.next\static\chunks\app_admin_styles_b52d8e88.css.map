{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/app/admin/styles.css"], "sourcesContent": ["/* Fix for HTML and body height to ensure background color extends fully */\r\nhtml,\r\nbody {\r\n  height: 100%;\r\n  min-height: 100%;\r\n  margin: 0;\r\n  padding: 0;\r\n  overflow-x: hidden;\r\n  background-color: hsl(var(--background));\r\n}\r\n\r\n/* Ensure the main container takes full height */\r\n#__next,\r\nmain {\r\n  min-height: 100%;\r\n}\r\n\r\n/* Ensure sidebar stays on top */\r\naside {\r\n  z-index: 40;\r\n}\r\n\r\n/* Ensure content area scrolls properly */\r\n.overflow-y-auto {\r\n  -webkit-overflow-scrolling: touch;\r\n}\r\n"], "names": [], "mappings": "AACA;;;;;;;;;AAWA;;;;AAMA;;;;AAKA"}}]}