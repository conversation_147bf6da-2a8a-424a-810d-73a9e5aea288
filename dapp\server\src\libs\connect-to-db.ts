import mongoose from "mongoose";

import { config } from "../config/load-env.config.ts";

/**
 * Database connection manager
 * Handles connecting to MongoDB and managing the connection
 */
class DatabaseManager {
  private static instance: DatabaseManager;
  private isConnected = false;
  private connectionOptions = {
    dbName: config.db.name,
    retryWrites: true,
    w: "majority",
    serverSelectionTimeoutMS: 5000,
    socketTimeoutMS: 45000,
  };

  /**
   * Get the singleton instance of DatabaseManager
   * @returns DatabaseManager instance
   */
  public static getInstance(): DatabaseManager {
    if (!DatabaseManager.instance) {
      DatabaseManager.instance = new DatabaseManager();
    }
    return DatabaseManager.instance;
  }

  /**
   * Private constructor to enforce singleton pattern
   */
  private constructor() {
    // Set up event listeners for the mongoose connection
    mongoose.connection.on("connected", () => {
      this.isConnected = true;
      console.info("Successfully connected to MongoDB");
    });

    mongoose.connection.on("error", (err) => {
      console.error("Mongoose connection error:", err);
      this.isConnected = false;
    });

    mongoose.connection.on("disconnected", () => {
      console.warn("Lost MongoDB connection");
      this.isConnected = false;
    });

    // Handle application termination
    process.on("SIGINT", this.gracefulShutdown.bind(this, "SIGINT"));
    process.on("SIGTERM", this.gracefulShutdown.bind(this, "SIGTERM"));
  }

  /**
   * Connect to the MongoDB database
   * @returns Promise that resolves when connected
   */
  public async connect(): Promise<void> {
    if (this.isConnected) {
      console.info("Using existing database connection");
      return;
    }

    try {
      // Enable debug mode in development
      mongoose.set("debug", process.env.NODE_ENV === "development");

      // Log connection details in development mode
      if (process.env.NODE_ENV === "development") {
        console.info(`Connecting to MongoDB: ${config.db.name}`);
      }

      // Connect to MongoDB
      await mongoose.connect(config.db.connection, this.connectionOptions);
      console.info(
        `Successfully connected to MongoDB database: ${config.db.name}`
      );
    } catch (err) {
      console.error("Failed to connect to database:", err);
      if (err instanceof Error) {
        console.error(`Error details: ${err.message}`);
      }
      process.exit(1);
    }
  }

  /**
   * Disconnect from the MongoDB database
   * @returns Promise that resolves when disconnected
   */
  public async disconnect(): Promise<void> {
    if (!this.isConnected) {
      return;
    }

    try {
      await mongoose.disconnect();
      this.isConnected = false;
      console.info("Disconnected from MongoDB");
    } catch (err) {
      console.error("Failed to disconnect from database:", err);
    }
  }

  /**
   * Gracefully shut down the database connection
   * @param signal - The signal that triggered the shutdown
   */
  private async gracefulShutdown(signal: string): Promise<void> {
    console.info(`${signal} signal received: closing MongoDB connection`);

    try {
      await this.disconnect();
      process.exit(0);
    } catch (err) {
      console.error("Error during graceful shutdown:", err);
      process.exit(1);
    }
  }
}

/**
 * Connect to the MongoDB database
 * @returns Promise that resolves when connected
 */
export const connectToDatabase = async (): Promise<void> => {
  const dbManager = DatabaseManager.getInstance();
  await dbManager.connect();
};
