import type { Express, NextFunction, Request, Response } from "express";

import { registerRoutes } from "../routes";

/**
 * Configure API routes for the Express application
 * @param app - Express application
 */
export const configRoutes = (app: Express): void => {
  try {
    // Register API routes
    registerRoutes(app);
    console.info("API routes registered successfully");

    // Note: 404 handler will be added later

    console.info("Route configuration completed");
  } catch (error) {
    console.error("Failed to configure routes:", error);
    throw error;
  }
};
