# Frontend-Backend API Integration

This document explains how the frontend has been connected to the backend API for product management.

## Overview

The frontend now fetches product data from the backend API instead of using static mock data. The integration includes:

- ✅ API utility functions for all CRUD operations
- ✅ React hooks for data fetching and state management
- ✅ Updated components to use real API data
- ✅ Loading states and error handling
- ✅ Filtering and pagination support

## Setup Instructions

### 1. Backend Server

Make sure the backend server is running:

```bash
cd dapp/server
pnpm install
pnpm dev
```

The server should start on `http://localhost:3001` (or the port specified in your `.env` file).

### 2. Frontend Client

Start the frontend development server:

```bash
cd dapp/client
pnpm install
pnpm dev
```

The client should start on `http://localhost:3000`.

### 3. Environment Configuration

The frontend is configured to connect to the backend API. The API URL is set in:

- `dapp/client/.env.local` - Contains `NEXT_PUBLIC_API_URL=http://localhost:3001`

## API Integration Details

### API Functions (`lib/api/products.ts`)

- `getProducts(filters)` - Fetch products with optional filtering
- `getProductById(id)` - Fetch a single product
- `createProduct(data)` - Create a new product
- `updateProduct(id, data)` - Update an existing product
- `deleteProduct(id)` - Delete a product
- `checkApiHealth()` - Test API connection

### React Hooks (`hooks/useProducts.ts`)

- `useProducts(filters)` - Hook for fetching and managing multiple products
- `useProduct(id)` - Hook for fetching a single product
- `useProductMutations()` - Hook for CRUD operations (create, update, delete)
- `useProductForEdit(id)` - Hook for fetching product data for editing

### Updated Components

1. **ProductGrid** - Now fetches products from API with loading/error states
2. **ProductCard** - Updated to handle backend data structure
3. **ProductDetailsWrapper** - Uses API for fetching and updating products
4. **ProductFilter** - Connected to API filtering
5. **AddProductForm** - Creates products via API
6. **ProductCard** - Includes delete functionality with confirmation dialog
7. **ProductBulkActions** - Bulk operations for multiple products

## Delete Functionality

The delete functionality has been fully implemented with the following features:

- ✅ **Individual product deletion** from ProductCard dropdown menu
- ✅ **Confirmation dialog** with product details before deletion
- ✅ **Loading states** during deletion process
- ✅ **Error handling** with toast notifications
- ✅ **Automatic refresh** of product list after deletion
- ✅ **Bulk delete** for multiple products (coming soon)

### How to Delete Products

1. **Single Product Deletion:**

   - Navigate to the products list page
   - Click the three-dot menu on any product card
   - Select "Delete Product"
   - Confirm in the dialog that appears
   - The product will be deleted and the list will refresh

2. **Testing Delete Functionality:**
   - Open browser console
   - Run: `testDeleteFunctionality()`
   - This will create a test product and then delete it

## Testing the Integration

### Method 1: Browser Console Test

1. Open the browser console on any page
2. Run: `testApiConnection()`
3. Check the console output for API health and sample data

### Method 2: Manual Testing

1. Navigate to `/admin/products/list`
2. Verify products load from the API
3. Test filtering and search functionality
4. Try creating a new product at `/admin/products/add`
5. Test editing an existing product

### Method 3: Check Network Tab

1. Open browser DevTools → Network tab
2. Navigate to the products page
3. Look for API calls to `http://localhost:3001/api/products`

## Data Flow

```
Frontend Component → React Hook → API Function → Backend Endpoint → Database
```

Example:

```
ProductGrid → useProducts → getProducts → GET /api/products → MongoDB
```

## Error Handling

The integration includes comprehensive error handling:

- **Network errors**: Displayed with retry buttons
- **API errors**: Toast notifications with error messages
- **Loading states**: Skeleton loaders and loading indicators
- **Empty states**: Friendly messages when no data is found

## Backend Data Structure

The backend returns products with this structure:

```typescript
{
  _id: string;
  name: string;
  price: number;
  currency: string;
  mainImage: string;
  category: string;
  brand: string;
  stock: number;
  status: string;
  // ... other fields
}
```

## Troubleshooting

### Common Issues

1. **API not responding**

   - Check if backend server is running
   - Verify the API URL in `.env.local`
   - Check for CORS issues in browser console

2. **Products not loading**

   - Check browser console for errors
   - Verify API endpoints are working
   - Test with the `testApiConnection()` function

3. **Type errors**
   - The frontend handles both backend (`_id`) and frontend (`id`) identifiers
   - Price formatting supports both string and number types

### Debug Steps

1. Check backend server logs
2. Check browser console for errors
3. Use Network tab to inspect API calls
4. Run the API test function
5. Verify environment variables

## Next Steps

The integration is now complete and ready for use. You can:

1. Add more products through the admin interface
2. Test all CRUD operations
3. Customize the filtering and sorting options
4. Add more advanced features like bulk operations
5. Implement real-time updates with WebSockets (future enhancement)
