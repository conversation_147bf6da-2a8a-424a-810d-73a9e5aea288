import {
  BarChart3,
  ExternalLink,
  Eye,
  Globe,
  Hash,
  MousePointer,
  Search,
  Star,
  Target,
  TrendingUp,
  Users,
  Zap,
} from "lucide-react";
import {
  FieldErrors,
  UseFormRegister,
  UseFormSetValue,
  UseFormWatch,
} from "react-hook-form";

import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import { ProductFormData } from "@/schemas/productSchema";

import { FormField } from "../../../common/FormField";
import { CollapsibleSection } from "../../../ui/collapsible-section";

type SEOSectionProps = {
  register: UseFormRegister<ProductFormData>;
  errors: FieldErrors<ProductFormData>;
  setValue: UseFormSetValue<ProductFormData>;
  watch: UseFormWatch<ProductFormData>;
};

export const SEOSection: React.FC<SEOSectionProps> = ({
  register,
  errors,
  setValue,
  watch,
}) => {
  const metaTitle = watch("metaTitle");
  const metaDescription = watch("metaDescription");
  const focusKeyword = watch("focusKeyword");

  return (
    <div className="space-y-8">
      {/* Section Header */}
      <div className="flex items-center gap-3 border-b border-gray-200 pb-4">
        <div className="rounded-full bg-green-100 p-2">
          <Search className="h-5 w-5 text-green-600" />
        </div>
        <div>
          <h2 className="text-2xl font-bold text-gray-900">SEO & Marketing</h2>
          <p className="text-gray-600">
            Optimize your product for search engines and marketing
          </p>
        </div>
      </div>

      {/* SEO Meta Information - Collapsible */}
      <CollapsibleSection
        title="SEO Meta Information"
        description="Optimize how your product appears in search engine results"
        icon={<Search className="h-5 w-5 text-green-600" />}
        borderColor="border-l-green-500"
        defaultOpen={false}
        isOptional={true}
      >
        <div className="space-y-4">
          {/* Meta Title */}
          <FormField id="metaTitle" label="Meta Title" optional={true}>
            <Input
              id="metaTitle"
              {...register("metaTitle")}
              placeholder="SEO-optimized title for search engines"
              className="border-2 focus:border-green-500"
              maxLength={60}
            />
            <div className="mt-1 flex justify-between text-xs text-gray-500">
              <span>Recommended: 50-60 characters</span>
              <span>{metaTitle?.length || 0}/60</span>
            </div>
          </FormField>

          {/* Meta Description */}
          <FormField
            id="metaDescription"
            label="Meta Description"
            optional={true}
          >
            <Textarea
              id="metaDescription"
              {...register("metaDescription")}
              placeholder="Brief description that appears in search results..."
              rows={3}
              className="resize-none border-2 focus:border-green-500"
              maxLength={160}
            />
            <div className="mt-1 flex justify-between text-xs text-gray-500">
              <span>Recommended: 150-160 characters</span>
              <span>{metaDescription?.length || 0}/160</span>
            </div>
          </FormField>

          {/* Focus Keyword */}
          <FormField id="focusKeyword" label="Focus Keyword" optional={true}>
            <Input
              id="focusKeyword"
              {...register("focusKeyword")}
              placeholder="Main keyword to target for SEO"
              className="border-2 focus:border-green-500"
            />
          </FormField>

          {/* SEO Preview */}
          {(metaTitle || metaDescription) && (
            <div className="mt-4 rounded-lg border border-green-200 bg-green-50 p-4">
              <h4 className="mb-2 font-medium text-green-900">
                Search Engine Preview
              </h4>
              <div className="rounded border bg-white p-3">
                <div className="cursor-pointer text-lg font-medium text-blue-600 hover:underline">
                  {metaTitle || "Your Product Title"}
                </div>
                <div className="text-sm text-green-700">
                  yourstore.com/products/product-name
                </div>
                <div className="mt-1 text-sm text-gray-600">
                  {metaDescription ||
                    "Your product description will appear here..."}
                </div>
              </div>
            </div>
          )}
        </div>
      </CollapsibleSection>

      {/* Keywords & Tags - Collapsible */}
      <CollapsibleSection
        title="Keywords & Tags"
        description="Add relevant keywords and tags to improve discoverability"
        icon={<Hash className="h-5 w-5 text-blue-600" />}
        borderColor="border-l-blue-500"
        defaultOpen={false}
        isOptional={true}
      >
        <div className="space-y-4">
          {/* SEO Keywords */}
          <FormField id="seoKeywords" label="SEO Keywords" optional={true}>
            <Input
              id="seoKeywords"
              {...register("seoKeywords")}
              placeholder="keyword1, keyword2, keyword3"
              className="border-2 focus:border-blue-500"
            />
            <p className="mt-1 text-xs text-gray-500">
              Separate keywords with commas. Focus on relevant, specific terms.
            </p>
          </FormField>

          {/* Canonical URL */}
          <FormField id="canonicalUrl" label="Canonical URL" optional={true}>
            <Input
              id="canonicalUrl"
              {...register("canonicalUrl")}
              placeholder="https://yourstore.com/products/product-name"
              className="border-2 focus:border-blue-500"
            />
            <p className="mt-1 text-xs text-gray-500">
              Specify the preferred URL for this product to avoid duplicate
              content issues.
            </p>
          </FormField>
        </div>
      </CollapsibleSection>

      {/* Marketing Features Card */}
      <Card className="border-l-4 border-l-purple-500">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Target className="h-5 w-5 text-purple-600" />
            Marketing Features
            <Badge variant="secondary" className="text-xs">
              Optional
            </Badge>
          </CardTitle>
          <p className="text-sm text-gray-600">
            Enable special marketing features and promotions
          </p>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Featured Product */}
            <div className="flex items-center space-x-3">
              <Switch
                id="featured"
                checked={watch("featured")}
                onCheckedChange={(checked) => setValue("featured", checked)}
              />
              <Label htmlFor="featured" className="text-base font-medium">
                Featured Product
              </Label>
            </div>

            {/* Sticky Product */}
            <div className="flex items-center space-x-3">
              <Switch
                id="sticky"
                checked={watch("sticky")}
                onCheckedChange={(checked) => setValue("sticky", checked)}
              />
              <Label htmlFor="sticky" className="text-base font-medium">
                Sticky (Always show at top)
              </Label>
            </div>

            {/* Marketing Description */}
            {(watch("featured") || watch("sticky")) && (
              <div className="rounded-lg border border-purple-200 bg-purple-50 p-3">
                <div className="flex items-start gap-2">
                  <Star className="mt-0.5 h-4 w-4 text-purple-600" />
                  <div className="text-sm text-purple-800">
                    <strong>Marketing Benefits:</strong>
                    <ul className="mt-1 list-inside list-disc space-y-1">
                      {watch("featured") && (
                        <li>
                          Featured products get priority placement in listings
                        </li>
                      )}
                      {watch("sticky") && (
                        <li>
                          Sticky products always appear at the top of search
                          results
                        </li>
                      )}
                    </ul>
                  </div>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* SEO Tips Card */}
      <Card className="border-green-200 bg-gradient-to-r from-green-50 to-blue-50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-green-800">
            <TrendingUp className="h-5 w-5" />
            SEO Best Practices
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2">
            <div className="flex items-start gap-3">
              <div className="rounded-full bg-green-100 p-2">
                <Search className="h-4 w-4 text-green-600" />
              </div>
              <div>
                <h4 className="font-medium text-green-900">Keyword Research</h4>
                <p className="mt-1 text-sm text-green-700">
                  Use tools like Google Keyword Planner to find relevant,
                  high-traffic keywords
                </p>
              </div>
            </div>

            <div className="flex items-start gap-3">
              <div className="rounded-full bg-blue-100 p-2">
                <Eye className="h-4 w-4 text-blue-600" />
              </div>
              <div>
                <h4 className="font-medium text-blue-900">User Intent</h4>
                <p className="mt-1 text-sm text-blue-700">
                  Write titles and descriptions that match what customers are
                  searching for
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
