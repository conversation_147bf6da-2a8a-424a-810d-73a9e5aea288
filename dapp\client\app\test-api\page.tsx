"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { testApiConnection, testAddProductFunctionality } from "@/lib/test-api";

export default function TestApiPage() {
  const [results, setResults] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);

  const addResult = (message: string) => {
    setResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const testConnection = async () => {
    setLoading(true);
    addResult("Testing API connection...");
    try {
      const result = await testApiConnection();
      addResult(result ? "✅ API connection successful" : "❌ API connection failed");
    } catch (error) {
      addResult(`❌ API connection error: ${error}`);
    }
    setLoading(false);
  };

  const testAddProduct = async () => {
    setLoading(true);
    addResult("Testing add product functionality...");
    try {
      const result = await testAddProductFunctionality();
      addResult(result ? "✅ Add product successful" : "❌ Add product failed");
    } catch (error) {
      addResult(`❌ Add product error: ${error}`);
    }
    setLoading(false);
  };

  const testSimpleCreate = async () => {
    setLoading(true);
    addResult("Testing simple product creation...");
    try {
      const response = await fetch('http://localhost:3001/api/products', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: 'Simple Test Product',
          price: 99.99,
          currency: 'USD',
          description: 'A simple test product',
          category: 'Test',
          brand: 'Test Brand',
          stock: 10,
          status: 'draft'
        })
      });

      if (response.ok) {
        const data = await response.json();
        addResult(`✅ Simple create successful: ${JSON.stringify(data)}`);
      } else {
        const errorText = await response.text();
        addResult(`❌ Simple create failed: ${response.status} - ${errorText}`);
      }
    } catch (error) {
      addResult(`❌ Simple create error: ${error}`);
    }
    setLoading(false);
  };

  const clearResults = () => {
    setResults([]);
  };

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <Card>
        <CardHeader>
          <CardTitle>API Testing Page</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2 flex-wrap">
            <Button onClick={testConnection} disabled={loading}>
              Test API Connection
            </Button>
            <Button onClick={testAddProduct} disabled={loading}>
              Test Add Product
            </Button>
            <Button onClick={testSimpleCreate} disabled={loading}>
              Test Simple Create
            </Button>
            <Button onClick={clearResults} variant="outline">
              Clear Results
            </Button>
          </div>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Test Results</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="bg-gray-100 p-4 rounded-lg max-h-96 overflow-y-auto">
                {results.length === 0 ? (
                  <p className="text-gray-500">No test results yet. Click a test button above.</p>
                ) : (
                  <div className="space-y-1">
                    {results.map((result, index) => (
                      <div key={index} className="text-sm font-mono">
                        {result}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Instructions</CardTitle>
            </CardHeader>
            <CardContent>
              <ol className="list-decimal list-inside space-y-2 text-sm">
                <li>Make sure your backend server is running on port 3001</li>
                <li>Click "Test API Connection" to verify the backend is accessible</li>
                <li>Click "Test Add Product" to test the full product creation flow</li>
                <li>Click "Test Simple Create" to test a basic HTTP POST request</li>
                <li>Check the browser console for detailed logs</li>
                <li>Check the Network tab in DevTools to see actual HTTP requests</li>
              </ol>
            </CardContent>
          </Card>
        </CardContent>
      </Card>
    </div>
  );
}
