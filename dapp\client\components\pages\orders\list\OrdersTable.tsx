"use client";

import React from "react";
import { More<PERSON><PERSON><PERSON><PERSON>, <PERSON>, Edit, Trash2 } from "lucide-react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { mockOrders } from "@/constants/products";

import { OrderRow } from "./OrderRow";

export const OrdersTable = () => {
  return (
    <div className="rounded-lg border border-gray-200 bg-white">
      <div className="overflow-x-auto">
        <table className="w-full text-sm">
          <thead>
            <tr className="border-b bg-gray-50/50 text-gray-500">
              <th className="p-4 text-left font-medium">Order</th>
              <th className="p-4 text-left font-medium">Customer</th>
              <th className="p-4 text-left font-medium">Products</th>
              <th className="p-4 text-center font-medium">Status</th>
              <th className="p-4 text-center font-medium">Shipping</th>
              <th className="p-4 text-right font-medium">Total</th>
              <th className="p-4 text-center font-medium">Date</th>
              <th className="p-4 text-center font-medium">Actions</th>
            </tr>
          </thead>

          <tbody>
            {mockOrders.map((order) => (
              <OrderRow key={order.id} order={order} />
            ))}
          </tbody>
        </table>
      </div>

      {/* Empty state - show when no orders */}
      {mockOrders.length === 0 && (
        <div className="flex flex-col items-center justify-center py-12 text-center">
          <div className="mb-4 rounded-full bg-gray-100 p-3">
            <Eye className="h-6 w-6 text-gray-400" />
          </div>
          <h3 className="mb-2 text-lg font-medium text-gray-900">No orders found</h3>
          <p className="text-gray-500">
            No orders match your current filters. Try adjusting your search criteria.
          </p>
        </div>
      )}
    </div>
  );
};
