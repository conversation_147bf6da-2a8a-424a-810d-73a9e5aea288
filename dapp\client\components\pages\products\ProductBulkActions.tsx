"use client";

import React, { useState } from "react";

import { Trash2, Archive, Eye, EyeOff } from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import { ConfirmationDialog } from "@/components/ui/confirmation-dialog";
import { useProductMutations } from "@/hooks/useProducts";
import { toast } from "sonner";

interface ProductBulkActionsProps {
  selectedProducts: string[];
  onClearSelection: () => void;
  onRefresh: () => void;
}

export const ProductBulkActions = ({
  selectedProducts,
  onClearSelection,
  onRefresh,
}: ProductBulkActionsProps) => {
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const { deleteProduct } = useProductMutations();

  const handleBulkDelete = async () => {
    setIsDeleting(true);
    try {
      // Delete products one by one
      const deletePromises = selectedProducts.map(id => deleteProduct(id));
      await Promise.all(deletePromises);
      
      toast.success(`Successfully deleted ${selectedProducts.length} product(s)`);
      onClearSelection();
      onRefresh();
      setShowDeleteDialog(false);
    } catch (error) {
      console.error("Failed to delete products:", error);
      toast.error("Failed to delete some products");
    } finally {
      setIsDeleting(false);
    }
  };

  const handleArchive = () => {
    toast.info("Archive functionality coming soon");
  };

  const handleHide = () => {
    toast.info("Hide functionality coming soon");
  };

  const handleShow = () => {
    toast.info("Show functionality coming soon");
  };

  if (selectedProducts.length === 0) {
    return null;
  }

  return (
    <>
      <div className="flex items-center gap-2 rounded-lg border bg-blue-50 p-3">
        <span className="text-sm font-medium text-blue-900">
          {selectedProducts.length} product(s) selected
        </span>
        
        <div className="ml-auto flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleShow}
          >
            <Eye className="mr-1 h-4 w-4" />
            Show
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={handleHide}
          >
            <EyeOff className="mr-1 h-4 w-4" />
            Hide
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={handleArchive}
          >
            <Archive className="mr-1 h-4 w-4" />
            Archive
          </Button>
          
          <Button
            variant="destructive"
            size="sm"
            onClick={() => setShowDeleteDialog(true)}
          >
            <Trash2 className="mr-1 h-4 w-4" />
            Delete
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={onClearSelection}
          >
            Cancel
          </Button>
        </div>
      </div>

      <ConfirmationDialog
        open={showDeleteDialog}
        onOpenChange={setShowDeleteDialog}
        title="Delete Products"
        description={`Are you sure you want to delete ${selectedProducts.length} product(s)? This action cannot be undone and will permanently remove the products from your inventory.`}
        confirmText={isDeleting ? "Deleting..." : "Delete Products"}
        cancelText="Cancel"
        onConfirm={handleBulkDelete}
        loading={isDeleting}
        variant="destructive"
      />
    </>
  );
};
