"use client";

import { useState } from "react";

import {
  Archive,
  Copy,
  Edit,
  Eye,
  Filter,
  FolderOpen,
  Grid3X3,
  LayoutGrid,
  List,
  MoreHorizontal,
  Package,
  Pencil,
  Plus,
  Save,
  Search,
  SortAsc,
  SortDesc,
  Sparkles,
  Tag,
  Trash2,
  TrendingUp,
  X,
} from "lucide-react";
import { toast } from "sonner";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";

export type Category = {
  id: string;
  name: string;
  description: string;
  slug: string;
  icon?: string;
  color?: string;
  isActive: boolean;
  productCount: number;
  parentId?: string;
  sortOrder: number;
  createdAt: string;
  updatedAt: string;
};

// Mock categories with enhanced data
const mockCategories: Category[] = [
  {
    id: "cat-1",
    name: "Electronics",
    description:
      "Electronic devices, gadgets, and accessories for modern living",
    slug: "electronics",
    icon: "📱",
    color: "#3B82F6",
    isActive: true,
    productCount: 324,
    sortOrder: 1,
    createdAt: "2024-01-01T00:00:00Z",
    updatedAt: "2024-01-15T10:30:00Z",
  },
  {
    id: "cat-2",
    name: "Clothing & Fashion",
    description:
      "Trendy clothing, shoes, and fashion accessories for all occasions",
    slug: "clothing-fashion",
    icon: "👕",
    color: "#EF4444",
    isActive: true,
    productCount: 289,
    sortOrder: 2,
    createdAt: "2024-01-02T00:00:00Z",
    updatedAt: "2024-01-14T16:45:00Z",
  },
  {
    id: "cat-3",
    name: "Home & Garden",
    description: "Furniture, decor, and garden supplies to beautify your space",
    slug: "home-garden",
    icon: "🏠",
    color: "#10B981",
    isActive: true,
    productCount: 156,
    sortOrder: 3,
    createdAt: "2024-01-03T00:00:00Z",
    updatedAt: "2024-01-13T09:20:00Z",
  },
  {
    id: "cat-4",
    name: "Sports & Outdoors",
    description:
      "Equipment and gear for sports, fitness, and outdoor adventures",
    slug: "sports-outdoors",
    icon: "⚽",
    color: "#F59E0B",
    isActive: true,
    productCount: 198,
    sortOrder: 4,
    createdAt: "2024-01-04T00:00:00Z",
    updatedAt: "2024-01-12T14:15:00Z",
  },
  {
    id: "cat-5",
    name: "Books & Media",
    description: "Books, magazines, movies, and digital media content",
    slug: "books-media",
    icon: "📚",
    color: "#8B5CF6",
    isActive: false,
    productCount: 67,
    sortOrder: 5,
    createdAt: "2024-01-05T00:00:00Z",
    updatedAt: "2024-01-11T11:30:00Z",
  },
  {
    id: "cat-6",
    name: "Health & Beauty",
    description: "Personal care, cosmetics, and wellness products",
    slug: "health-beauty",
    icon: "💄",
    color: "#EC4899",
    isActive: true,
    productCount: 143,
    sortOrder: 6,
    createdAt: "2024-01-06T00:00:00Z",
    updatedAt: "2024-01-10T08:45:00Z",
  },
];

type CategoryManagerProps = {
  initialCategories?: Category[];
  onCategoriesChange?: (categories: Category[]) => void;
};

/**
 * Enhanced component for managing product categories with professional UI
 */
export const CategoryManagerEnhanced = ({
  initialCategories = mockCategories,
  onCategoriesChange,
}: CategoryManagerProps) => {
  const [categories, setCategories] = useState<Category[]>(initialCategories);
  const [newCategory, setNewCategory] = useState<Partial<Category>>({
    name: "",
    description: "",
    icon: "",
    color: "#3B82F6",
    isActive: true,
  });
  const [editingCategoryId, setEditingCategoryId] = useState<string | null>(
    null
  );
  const [editForm, setEditForm] = useState<Partial<Category>>({});
  const [searchQuery, setSearchQuery] = useState("");
  const [filterStatus, setFilterStatus] = useState("all");
  const [sortBy, setSortBy] = useState("name");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("asc");
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [showAddForm, setShowAddForm] = useState(false);

  // Generate a slug from the category name
  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, "-")
      .replace(/^-|-$/g, "");
  };

  // Filter and sort categories
  const filteredAndSortedCategories = categories
    .filter((category) => {
      const matchesSearch =
        category.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        category.description.toLowerCase().includes(searchQuery.toLowerCase());
      const matchesFilter =
        filterStatus === "all" ||
        (filterStatus === "active" && category.isActive) ||
        (filterStatus === "inactive" && !category.isActive);
      return matchesSearch && matchesFilter;
    })
    .sort((a, b) => {
      let comparison = 0;
      switch (sortBy) {
        case "name":
          comparison = a.name.localeCompare(b.name);
          break;
        case "products":
          comparison = a.productCount - b.productCount;
          break;
        case "created":
          comparison =
            new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
          break;
        default:
          comparison = a.sortOrder - b.sortOrder;
      }
      return sortOrder === "asc" ? comparison : -comparison;
    });

  // Add a new category
  const handleAddCategory = () => {
    if (!newCategory.name) {
      toast.error("Category name is required");
      return;
    }

    const slug = generateSlug(newCategory.name);

    // Check if slug already exists
    if (categories.some((cat) => cat.slug === slug)) {
      toast.error("A category with this name already exists");
      return;
    }

    const newCategoryWithId: Category = {
      id: `category-${Date.now()}`,
      name: newCategory.name,
      description: newCategory.description || "",
      slug,
      icon: newCategory.icon || "📦",
      color: newCategory.color || "#3B82F6",
      isActive: newCategory.isActive ?? true,
      productCount: 0,
      sortOrder: categories.length + 1,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    const updatedCategories = [...categories, newCategoryWithId];
    setCategories(updatedCategories);
    setNewCategory({
      name: "",
      description: "",
      icon: "",
      color: "#3B82F6",
      isActive: true,
    });
    setShowAddForm(false);

    // Notify parent component
    if (onCategoriesChange) {
      onCategoriesChange(updatedCategories);
    }

    toast.success("Category added successfully");
  };

  // Start editing a category
  const handleEditStart = (category: Category) => {
    setEditingCategoryId(category.id);
    setEditForm({ ...category });
  };

  // Cancel editing
  const handleEditCancel = () => {
    setEditingCategoryId(null);
    setEditForm({});
  };

  // Save edited category
  const handleEditSave = () => {
    if (!editForm.name) {
      toast.error("Category name is required");
      return;
    }

    const updatedCategories = categories.map((cat) =>
      cat.id === editingCategoryId
        ? {
            ...cat,
            name: editForm.name || cat.name,
            description: editForm.description || cat.description,
            icon: editForm.icon || cat.icon,
            color: editForm.color || cat.color,
            isActive: editForm.isActive ?? cat.isActive,
            // Only update slug if name changed
            slug:
              cat.name !== editForm.name
                ? generateSlug(editForm.name)
                : cat.slug,
            updatedAt: new Date().toISOString(),
          }
        : cat
    );

    setCategories(updatedCategories);
    setEditingCategoryId(null);
    setEditForm({});

    // Notify parent component
    if (onCategoriesChange) {
      onCategoriesChange(updatedCategories);
    }

    toast.success("Category updated successfully");
  };

  // Delete a category
  const handleDeleteCategory = (categoryId: string) => {
    const updatedCategories = categories.filter((cat) => cat.id !== categoryId);
    setCategories(updatedCategories);

    // Notify parent component
    if (onCategoriesChange) {
      onCategoriesChange(updatedCategories);
    }

    toast.success("Category deleted successfully");
  };

  return (
    <div className="space-y-6">
      {/* Header with Statistics */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="rounded-full bg-blue-100 p-2">
                <LayoutGrid className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Total Categories</p>
                <p className="text-2xl font-bold">{categories.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="rounded-full bg-green-100 p-2">
                <TrendingUp className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Active Categories</p>
                <p className="text-2xl font-bold">
                  {categories.filter((c) => c.isActive).length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="rounded-full bg-orange-100 p-2">
                <Package className="h-5 w-5 text-orange-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Total Products</p>
                <p className="text-2xl font-bold">
                  {categories.reduce((sum, c) => sum + c.productCount, 0)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="rounded-full bg-purple-100 p-2">
                <Sparkles className="h-5 w-5 text-purple-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Avg Products</p>
                <p className="text-2xl font-bold">
                  {categories.length > 0
                    ? Math.round(
                        categories.reduce((sum, c) => sum + c.productCount, 0) /
                          categories.length
                      )
                    : 0}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Controls */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
            {/* Search and Filters */}
            <div className="flex flex-1 gap-4">
              <div className="relative max-w-md flex-1">
                <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
                <Input
                  placeholder="Search categories..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>

              <Select value={filterStatus} onValueChange={setFilterStatus}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                </SelectContent>
              </Select>

              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="name">Name</SelectItem>
                  <SelectItem value="products">Products</SelectItem>
                  <SelectItem value="created">Created</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* View Controls */}
            <div className="flex items-center gap-2">
              <Button
                variant={sortOrder === "asc" ? "default" : "outline"}
                size="sm"
                onClick={() =>
                  setSortOrder(sortOrder === "asc" ? "desc" : "asc")
                }
              >
                {sortOrder === "asc" ? (
                  <SortAsc className="h-4 w-4" />
                ) : (
                  <SortDesc className="h-4 w-4" />
                )}
              </Button>

              <div className="flex rounded-md border">
                <Button
                  variant={viewMode === "grid" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setViewMode("grid")}
                  className="rounded-r-none"
                >
                  <Grid3X3 className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === "list" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setViewMode("list")}
                  className="rounded-l-none"
                >
                  <List className="h-4 w-4" />
                </Button>
              </div>

              <Button onClick={() => setShowAddForm(true)}>
                <Plus className="mr-2 h-4 w-4" />
                Add Category
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Add Category Modal */}
      {showAddForm && (
        <Card className="border-2 border-blue-200 bg-blue-50/50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Plus className="h-5 w-5 text-blue-600" />
              Add New Category
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2">
              <div>
                <Label htmlFor="new-category-name">Category Name *</Label>
                <Input
                  id="new-category-name"
                  value={newCategory.name}
                  onChange={(e) =>
                    setNewCategory({ ...newCategory, name: e.target.value })
                  }
                  placeholder="e.g. Electronics"
                  className="mt-1"
                />
              </div>

              <div>
                <Label htmlFor="new-category-icon">Icon (Emoji)</Label>
                <Input
                  id="new-category-icon"
                  value={newCategory.icon}
                  onChange={(e) =>
                    setNewCategory({ ...newCategory, icon: e.target.value })
                  }
                  placeholder="📱"
                  className="mt-1"
                />
              </div>

              <div className="md:col-span-2">
                <Label htmlFor="new-category-description">Description</Label>
                <Textarea
                  id="new-category-description"
                  value={newCategory.description}
                  onChange={(e) =>
                    setNewCategory({
                      ...newCategory,
                      description: e.target.value,
                    })
                  }
                  placeholder="Describe what products belong in this category..."
                  className="mt-1"
                  rows={3}
                />
              </div>

              <div>
                <Label htmlFor="new-category-color">Color</Label>
                <Input
                  id="new-category-color"
                  type="color"
                  value={newCategory.color}
                  onChange={(e) =>
                    setNewCategory({ ...newCategory, color: e.target.value })
                  }
                  className="mt-1 h-10"
                />
              </div>

              <div className="flex items-center space-x-2 pt-6">
                <Switch
                  id="new-category-active"
                  checked={newCategory.isActive}
                  onCheckedChange={(checked) =>
                    setNewCategory({ ...newCategory, isActive: checked })
                  }
                />
                <Label htmlFor="new-category-active">Active category</Label>
              </div>
            </div>

            <div className="mt-6 flex gap-2">
              <Button onClick={handleAddCategory}>
                <Save className="mr-2 h-4 w-4" />
                Add Category
              </Button>
              <Button variant="outline" onClick={() => setShowAddForm(false)}>
                <X className="mr-2 h-4 w-4" />
                Cancel
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Categories Display */}
      {viewMode === "grid" ? (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {filteredAndSortedCategories.map((category) => (
            <Card
              key={category.id}
              className="transition-shadow hover:shadow-md"
            >
              <CardContent className="p-4">
                {editingCategoryId === category.id ? (
                  // Edit form
                  <div className="space-y-3">
                    <Input
                      value={editForm.name || ""}
                      onChange={(e) =>
                        setEditForm({ ...editForm, name: e.target.value })
                      }
                      placeholder="Category name"
                    />
                    <Textarea
                      value={editForm.description || ""}
                      onChange={(e) =>
                        setEditForm({
                          ...editForm,
                          description: e.target.value,
                        })
                      }
                      placeholder="Description"
                      rows={2}
                    />
                    <div className="flex gap-2">
                      <Button size="sm" onClick={handleEditSave}>
                        <Save className="mr-1 h-3 w-3" />
                        Save
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={handleEditCancel}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                ) : (
                  // Display mode
                  <div className="space-y-3">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-2">
                        <span className="text-2xl">{category.icon}</span>
                        <div>
                          <h3 className="font-semibold">{category.name}</h3>
                          <Badge
                            variant={
                              category.isActive ? "default" : "secondary"
                            }
                            className="text-xs"
                          >
                            {category.isActive ? "Active" : "Inactive"}
                          </Badge>
                        </div>
                      </div>

                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem
                            onClick={() => handleEditStart(category)}
                          >
                            <Edit className="mr-2 h-4 w-4" />
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Eye className="mr-2 h-4 w-4" />
                            View Products
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Copy className="mr-2 h-4 w-4" />
                            Duplicate
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem>
                            <Archive className="mr-2 h-4 w-4" />
                            Archive
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => handleDeleteCategory(category.id)}
                            className="text-red-600"
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>

                    <p className="line-clamp-2 text-sm text-gray-600">
                      {category.description || "No description provided"}
                    </p>

                    <div className="flex items-center justify-between text-xs text-gray-500">
                      <span>{category.productCount} products</span>
                      <span
                        className="h-3 w-3 rounded-full"
                        style={{ backgroundColor: category.color }}
                      ></span>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        // List view
        <Card>
          <CardContent className="p-0">
            <div className="divide-y">
              {filteredAndSortedCategories.map((category) => (
                <div key={category.id} className="p-4 hover:bg-gray-50">
                  {editingCategoryId === category.id ? (
                    // Edit form
                    <div className="flex items-center gap-4">
                      <Input
                        value={editForm.name || ""}
                        onChange={(e) =>
                          setEditForm({ ...editForm, name: e.target.value })
                        }
                        placeholder="Category name"
                        className="flex-1"
                      />
                      <Input
                        value={editForm.description || ""}
                        onChange={(e) =>
                          setEditForm({
                            ...editForm,
                            description: e.target.value,
                          })
                        }
                        placeholder="Description"
                        className="flex-1"
                      />
                      <div className="flex gap-2">
                        <Button size="sm" onClick={handleEditSave}>
                          <Save className="h-4 w-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={handleEditCancel}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ) : (
                    // Display mode
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        <span className="text-2xl">{category.icon}</span>
                        <div>
                          <div className="flex items-center gap-2">
                            <h3 className="font-semibold">{category.name}</h3>
                            <Badge
                              variant={
                                category.isActive ? "default" : "secondary"
                              }
                              className="text-xs"
                            >
                              {category.isActive ? "Active" : "Inactive"}
                            </Badge>
                          </div>
                          <p className="text-sm text-gray-600">
                            {category.description || "No description"}
                          </p>
                          <div className="mt-1 flex items-center gap-4 text-xs text-gray-500">
                            <span>{category.productCount} products</span>
                            <span>Slug: {category.slug}</span>
                            <div className="flex items-center gap-1">
                              <span>Color:</span>
                              <span
                                className="h-3 w-3 rounded-full"
                                style={{ backgroundColor: category.color }}
                              ></span>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleEditStart(category)}
                        >
                          <Pencil className="h-4 w-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleDeleteCategory(category.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Empty State */}
      {filteredAndSortedCategories.length === 0 && (
        <Card>
          <CardContent className="p-12 text-center">
            <FolderOpen className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-lg font-medium text-gray-900">
              No categories found
            </h3>
            <p className="mt-1 text-sm text-gray-500">
              {searchQuery || filterStatus !== "all"
                ? "Try adjusting your search or filter criteria."
                : "Get started by creating your first category."}
            </p>
            {!searchQuery && filterStatus === "all" && (
              <Button className="mt-4" onClick={() => setShowAddForm(true)}>
                <Plus className="mr-2 h-4 w-4" />
                Add Your First Category
              </Button>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
};
