import { UploadDropzone, generateReactHel<PERSON> } from "@uploadthing/react";

import { useState } from "react";

import { X } from "lucide-react";
import Image from "next/image";
import { toast } from "sonner";

import type { OurFileRouter } from "@/app/api/uploadthing/core";
import { Input } from "@/components/ui/input";
import { Skeleton } from "@/components/ui/skeleton";
import { deleteImageFromServer } from "@/lib/uploadthing/utils";

const { useUploadThing } = generateReactHelpers<OurFileRouter>();

type ImageUploadSectionProps = {
  images: { key: string; url: string }[];
  setImages: React.Dispatch<
    React.SetStateAction<{ key: string; url: string }[]>
  >;
  imageAltTexts: string[];
  setImageAltTexts: React.Dispatch<React.SetStateAction<string[]>>;
  maxImages?: number;
};

export const ImageUploadSection: React.FC<ImageUploadSectionProps> = ({
  images,
  setImages,
  imageAltTexts,
  setImageAltTexts,
  maxImages = 4,
}) => {
  const [dropzoneKey, setDropzoneKey] = useState(0);
  const [uploadingCount, setUploadingCount] = useState(0);

  const [loadedImages, setLoadedImages] = useState<Record<string, boolean>>({});
  const [deletingImages, setDeletingImages] = useState<string[]>([]);

  // Handle alt text changes
  const handleAltTextChange = (index: number, value: string) => {
    const newAltTexts = [...imageAltTexts];
    newAltTexts[index] = value;
    setImageAltTexts(newAltTexts);
  };

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { startUpload } = useUploadThing("imageUploader");
  type UploadResponse = Awaited<ReturnType<typeof startUpload>>;

  const handleBeforeUploadBegin = (files: File[]) => {
    const total = images.length + files.length;

    if (total > maxImages) {
      toast.error(
        `You can only upload up to ${maxImages} images. You already have ${images.length}.`
      );

      setDropzoneKey((prev) => prev + 1);

      throw new Error("Too many images");
    }

    setUploadingCount(files.length);

    return files;
  };

  const handleClientUploadComplete = (res: UploadResponse) => {
    setUploadingCount(0);

    if (!res || res.length === 0) {
      toast.error("Image upload failed. Received no file data.");

      return;
    }

    const newImages = res.map((file) => ({
      key: file.key,
      url: file.ufsUrl ?? "",
    }));

    const updatedImages = [...images, ...newImages];

    if (updatedImages.length > maxImages) {
      toast.error(`You can only upload up to ${maxImages} images.`);

      setDropzoneKey((prev) => prev + 1);

      return;
    }

    setImages(updatedImages);

    // Add empty alt texts for new images
    const newAltTexts = [...imageAltTexts];
    for (let i = 0; i < newImages.length; i++) {
      newAltTexts.push("");
    }
    setImageAltTexts(newAltTexts);

    toast.success("Image uploaded successfully!");
  };

  const handleUploadError = (error: Error) => {
    console.error("Upload failed:", error);

    setDropzoneKey((prev) => prev + 1);

    setUploadingCount(0);

    if (error.message.includes("FileSizeMismatch")) {
      toast.error("File is too large. Maximum allowed size exceeded.");
    } else if (error.message === "Too many images") {
      toast.error(`You can only upload up to ${maxImages} images.`);
    } else {
      toast.error("Image upload failed. Please try a different file.");
    }
  };

  const removeImage = async (index: number) => {
    const imageToRemove = images[index];

    setDeletingImages((prev) => [...prev, imageToRemove.key]);

    setTimeout(async () => {
      setLoadedImages((prev) => {
        const updated = { ...prev };
        delete updated[imageToRemove.key];

        return updated;
      });

      setImages((prev) => prev.filter((_, i) => i !== index));

      // Also remove the corresponding alt text
      setImageAltTexts((prev) => prev.filter((_, i) => i !== index));

      try {
        const deleted = await deleteImageFromServer(imageToRemove.key);
        if (!deleted) {
          toast.error("Image removed from form but server deletion failed.");
        } else {
          toast.success("Image removed.");
        }
      } catch (e) {
        console.error("Failed to delete image from server:", e);
        toast.error("An error occurred while deleting the image.");
      }

      setDeletingImages((prev) =>
        prev.filter((key) => key !== imageToRemove.key)
      );
    }, 300);
  };

  return (
    <div className="w-full space-y-4">
      {/* Images with Alt Text Inputs */}
      {images.length > 0 && (
        <div className="space-y-4">
          <h4 className="text-sm font-medium text-gray-700">
            Uploaded Images & Alt Text
          </h4>
          <div className="grid gap-4 md:grid-cols-2">
            {images.map((image, i) => (
              <div
                key={image.key}
                className={`rounded-lg border p-4 transition-all duration-300 ${
                  deletingImages.includes(image.key)
                    ? "scale-95 opacity-50"
                    : "scale-100 opacity-100"
                }`}
              >
                {/* Image Preview */}
                <div className="group relative mb-3 aspect-square overflow-hidden rounded border">
                  {!loadedImages[image.key] && (
                    <Skeleton className="absolute inset-0 size-full" />
                  )}

                  <Image
                    src={image.url}
                    alt={imageAltTexts[i] || `Product image ${i + 1}`}
                    fill
                    sizes="(max-width: 768px) 100vw, 200px"
                    className="object-cover transition-opacity duration-500"
                    style={{
                      opacity: loadedImages[image.key] ? 1 : 0,
                    }}
                    onLoad={() =>
                      setLoadedImages((prev) => ({
                        ...prev,
                        [image.key]: true,
                      }))
                    }
                    quality={85}
                  />

                  {/* Image Badge */}
                  <div className="absolute left-2 top-2 rounded bg-black/70 px-2 py-1 text-xs text-white">
                    {i === 0 ? "Main" : `#${i + 1}`}
                  </div>

                  {/* Remove Button */}
                  <button
                    type="button"
                    onClick={() => removeImage(i)}
                    className="absolute right-2 top-2 rounded-full bg-red-500 p-1 text-white opacity-0 shadow-sm transition-opacity hover:bg-red-600 group-hover:opacity-100"
                  >
                    <X size={14} />
                  </button>
                </div>

                {/* Alt Text Input */}
                <div className="space-y-2">
                  <label className="text-xs font-medium text-gray-600">
                    Alt Text {i === 0 && "(Main Image)"}
                    <span className="text-red-500">*</span>
                  </label>
                  <Input
                    value={imageAltTexts[i] || ""}
                    onChange={(e) => handleAltTextChange(i, e.target.value)}
                    placeholder={`Describe image ${i + 1}... (e.g., "Red cotton t-shirt front view")`}
                    className={`text-sm ${
                      !imageAltTexts[i] || imageAltTexts[i].trim() === ""
                        ? "border-red-300 focus:border-red-500"
                        : "border-green-300 focus:border-green-500"
                    }`}
                    required
                  />
                  {!imageAltTexts[i] || imageAltTexts[i].trim() === "" ? (
                    <p className="text-xs text-red-600">
                      ⚠️ Alt text is required for accessibility and SEO
                    </p>
                  ) : (
                    <p className="text-xs text-green-600">
                      ✅ Good for SEO and accessibility
                    </p>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Uploading Skeletons */}
      {uploadingCount > 0 && (
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-gray-700">Uploading...</h4>
          <div className="grid gap-4 md:grid-cols-2">
            {Array.from({ length: uploadingCount }).map((_, idx) => (
              <div key={`uploading-${idx}`} className="rounded-lg border p-4">
                <div className="aspect-square overflow-hidden rounded border">
                  <Skeleton className="size-full" />
                </div>
                <div className="mt-3 space-y-2">
                  <Skeleton className="h-4 w-20" />
                  <Skeleton className="h-8 w-full" />
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Upload Dropzone */}
      {images.length < maxImages && (
        <div className="flex">
          <UploadDropzone<OurFileRouter, "imageUploader">
            key={dropzoneKey}
            endpoint="imageUploader"
            onBeforeUploadBegin={handleBeforeUploadBegin}
            onClientUploadComplete={handleClientUploadComplete}
            onUploadError={handleUploadError}
            appearance={{
              button: "w-28 h-10 text-sm font-semibold ut-ready:bg-green-500",
              container: "w-full",
            }}
          />
        </div>
      )}

      {/* Alt Text Completion Status */}
      {images.length > 0 && (
        <div className="rounded-lg border border-blue-200 bg-blue-50 p-3">
          <div className="flex items-center gap-2">
            <div className="text-sm font-medium text-blue-900">
              Alt Text Completion:{" "}
              {imageAltTexts.filter((alt) => alt && alt.trim() !== "").length} /{" "}
              {images.length}
            </div>
            {imageAltTexts.filter((alt) => alt && alt.trim() !== "").length ===
            images.length ? (
              <span className="text-green-600">✅ All Complete</span>
            ) : (
              <span className="text-red-600">
                ⚠️{" "}
                {images.length -
                  imageAltTexts.filter((alt) => alt && alt.trim() !== "")
                    .length}{" "}
                Missing
              </span>
            )}
          </div>
          <p className="mt-1 text-xs text-blue-700">
            All images must have alt text before you can continue to the next
            step.
          </p>
        </div>
      )}

      {/* Bottom Note */}
      <div className="flex flex-col text-xs text-gray-500">
        {images.length >= maxImages && (
          <p className="font-semibold text-green-500">
            Maximum image limit reached.
          </p>
        )}
        <p>
          First image will be the main product image. Additional images will be
          shown separately.
        </p>
      </div>
    </div>
  );
};
