import { ArrowLef<PERSON>, ArrowR<PERSON>, Save } from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import { SectionId } from "@/types/form-section";

type FormNavigationProps = {
  currentSection: SectionId;
  isLastSection: boolean;
  handlePrevious: () => void;
  handleNext: () => void;
};

/**
 * Navigation buttons for the multi-step form
 */
export const FormNavigation = ({
  currentSection,
  isLastSection,
  handlePrevious,
  handleNext,
}: FormNavigationProps) => {
  return (
    <div className="flex items-center justify-between pt-4">
      {/* Previous button */}
      <Button
        type="button"
        variant="outline"
        onClick={handlePrevious}
        disabled={currentSection === "basic-info"} // Disable on first section
      >
        <ArrowLeft className="mr-2 size-4" />
        Previous
      </Button>

      {/* Next/Submit button */}
      <div>
        {isLastSection ? (
          <Button
            type="submit"
            className="bg-green-600 hover:bg-green-700"
            // Always enable the save button to allow saving in any state
            // Original condition was: disabled={!isValid}
            disabled={false}
          >
            <Save className="mr-2 size-4" />
            Save Product
          </Button>
        ) : (
          <Button
            type="button"
            onClick={handleNext}
            className="bg-blue-600 hover:bg-blue-700"
          >
            Next
            <ArrowRight className="ml-2 size-4" />
          </Button>
        )}
      </div>
    </div>
  );
};
