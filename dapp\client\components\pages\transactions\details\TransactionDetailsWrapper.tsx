"use client";

import { format } from "date-fns";
import { 
  ArrowLeft, 
  CreditCard, 
  ExternalLink, 
  Receipt, 
  RefreshCw,
  CheckCircle,
  AlertCircle,
  Clock,
  XCircle
} from "lucide-react";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { mockTransactions } from "@/constants/transactions";
import { mockCustomers } from "@/constants/products";

type Props = {
  transactionId: string;
};

export const TransactionDetailsWrapper = ({ transactionId }: Props) => {
  // Find the transaction
  const transaction = mockTransactions.find(t => t.id === transactionId);
  const customer = mockCustomers.find(c => c.id === transaction?.userId);

  if (!transaction) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900">Transaction Not Found</h2>
          <p className="mt-2 text-gray-600">The transaction you're looking for doesn't exist.</p>
          <Button className="mt-4" onClick={() => window.history.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Go Back
          </Button>
        </div>
      </div>
    );
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "paid":
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case "pending":
        return <Clock className="h-5 w-5 text-yellow-600" />;
      case "failed":
        return <XCircle className="h-5 w-5 text-red-600" />;
      case "cancelled":
        return <XCircle className="h-5 w-5 text-gray-600" />;
      case "refunded":
        return <RefreshCw className="h-5 w-5 text-blue-600" />;
      default:
        return <AlertCircle className="h-5 w-5 text-gray-600" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      paid: { variant: "default" as const, className: "bg-green-100 text-green-800" },
      pending: { variant: "secondary" as const, className: "bg-yellow-100 text-yellow-800" },
      failed: { variant: "destructive" as const, className: "bg-red-100 text-red-800" },
      cancelled: { variant: "outline" as const, className: "bg-gray-100 text-gray-800" },
      refunded: { variant: "secondary" as const, className: "bg-blue-100 text-blue-800" },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    
    return (
      <Badge variant={config.variant} className={config.className}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" onClick={() => window.history.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Transactions
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Transaction Details</h1>
            <p className="text-gray-600">{transaction.id}</p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          {getStatusIcon(transaction.status)}
          {getStatusBadge(transaction.status)}
        </div>
      </div>

      <div className="grid gap-6 lg:grid-cols-3">
        {/* Main Transaction Info */}
        <div className="lg:col-span-2 space-y-6">
          {/* Transaction Overview */}
          <Card>
            <CardHeader>
              <CardTitle>Transaction Overview</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2">
                <div>
                  <label className="text-sm font-medium text-gray-500">Transaction ID</label>
                  <p className="mt-1 font-mono text-sm">{transaction.id}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Order ID</label>
                  <p className="mt-1">
                    <Button variant="link" className="h-auto p-0 text-blue-600">
                      {transaction.orderId}
                    </Button>
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Amount</label>
                  <p className="mt-1 text-lg font-semibold">€{transaction.amount.toFixed(2)}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Currency</label>
                  <p className="mt-1">{transaction.currency}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Payment Method</label>
                  <p className="mt-1 flex items-center gap-2">
                    <CreditCard className="h-4 w-4" />
                    {transaction.method.charAt(0).toUpperCase() + transaction.method.slice(1).replace('-', ' ')}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Status</label>
                  <div className="mt-1">
                    {getStatusBadge(transaction.status)}
                  </div>
                </div>
              </div>

              {transaction.refundedAmount && (
                <>
                  <Separator className="my-4" />
                  <div className="rounded-lg bg-blue-50 p-4">
                    <h4 className="font-medium text-blue-900">Refund Information</h4>
                    <p className="mt-1 text-sm text-blue-700">
                      Refunded Amount: €{transaction.refundedAmount.toFixed(2)}
                    </p>
                  </div>
                </>
              )}
            </CardContent>
          </Card>

          {/* Provider Information */}
          {transaction.providerTransactionId && (
            <Card>
              <CardHeader>
                <CardTitle>Payment Provider Details</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div>
                    <label className="text-sm font-medium text-gray-500">Provider Transaction ID</label>
                    <p className="mt-1 font-mono text-sm break-all">{transaction.providerTransactionId}</p>
                  </div>
                  {transaction.receiptUrl && (
                    <div>
                      <label className="text-sm font-medium text-gray-500">Receipt</label>
                      <div className="mt-1">
                        <Button variant="outline" size="sm" asChild>
                          <a href={transaction.receiptUrl} target="_blank" rel="noopener noreferrer">
                            <Receipt className="mr-2 h-4 w-4" />
                            View Receipt
                            <ExternalLink className="ml-2 h-4 w-4" />
                          </a>
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Customer Information */}
          <Card>
            <CardHeader>
              <CardTitle>Customer Information</CardTitle>
            </CardHeader>
            <CardContent>
              {customer ? (
                <div className="space-y-3">
                  <div>
                    <label className="text-sm font-medium text-gray-500">Name</label>
                    <p className="mt-1">{customer.name}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Email</label>
                    <p className="mt-1">{customer.email}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Customer ID</label>
                    <p className="mt-1 font-mono text-sm">{customer.id}</p>
                  </div>
                  <Button variant="outline" size="sm" className="w-full">
                    View Customer Profile
                  </Button>
                </div>
              ) : (
                <p className="text-gray-500">Customer information not available</p>
              )}
            </CardContent>
          </Card>

          {/* Timeline */}
          <Card>
            <CardHeader>
              <CardTitle>Transaction Timeline</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <div className="mt-1 h-2 w-2 rounded-full bg-blue-600"></div>
                  <div>
                    <p className="text-sm font-medium">Transaction Created</p>
                    <p className="text-xs text-gray-500">
                      {format(new Date(transaction.createdAt), "MMM dd, yyyy 'at' HH:mm")}
                    </p>
                  </div>
                </div>
                
                {transaction.updatedAt !== transaction.createdAt && (
                  <div className="flex items-start gap-3">
                    <div className="mt-1 h-2 w-2 rounded-full bg-green-600"></div>
                    <div>
                      <p className="text-sm font-medium">Status Updated</p>
                      <p className="text-xs text-gray-500">
                        {format(new Date(transaction.updatedAt), "MMM dd, yyyy 'at' HH:mm")}
                      </p>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Actions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Button variant="outline" size="sm" className="w-full">
                  <ExternalLink className="mr-2 h-4 w-4" />
                  View Related Order
                </Button>
                {transaction.receiptUrl && (
                  <Button variant="outline" size="sm" className="w-full" asChild>
                    <a href={transaction.receiptUrl} target="_blank" rel="noopener noreferrer">
                      <Receipt className="mr-2 h-4 w-4" />
                      Download Receipt
                    </a>
                  </Button>
                )}
                {transaction.status === "paid" && (
                  <Button variant="outline" size="sm" className="w-full text-red-600 hover:text-red-700">
                    <RefreshCw className="mr-2 h-4 w-4" />
                    Process Refund
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};
