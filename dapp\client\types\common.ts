export type ISODateString = string;
export type UUID = string;

export type CurrencyUnit = "EUR";

export type WeightUnit = "g" | "kg";
export type DimensionUnit = "mm" | "cm";

export type AgeRestriction = "none" | "18+" | "21+";

export type Condition =
  | "new"
  | "like-new"
  | "excellent"
  | "good"
  | "fair"
  | "used"
  | "refurbished"
  | "vintage"
  | "antique"
  | "damaged";

export type ShippingStatus =
  | "not-shipped"
  | "shipped"
  | "in-transit"
  | "delivered"
  | "delayed";
