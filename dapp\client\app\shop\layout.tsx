// Future e-shop layout - different from admin
export default function ShopLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="min-h-screen bg-white">
      {/* Future: Shop header with navigation, cart, user menu */}
      <header className="border-b bg-white">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="text-xl font-bold">Your Store</div>
            <div className="text-sm text-gray-500">
              E-shop coming soon...
            </div>
          </div>
        </div>
      </header>
      
      {/* Shop content */}
      <main className="container mx-auto px-4 py-8">
        {children}
      </main>
      
      {/* Future: Shop footer */}
      <footer className="border-t bg-gray-50 mt-auto">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center text-gray-500">
            © 2024 Your Store. All rights reserved.
          </div>
        </div>
      </footer>
    </div>
  );
}
