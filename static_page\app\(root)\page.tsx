"use client";

import { useEffect, useState } from "react";

import { motion } from "framer-motion";

import AboutSection from "@/components/AboutSection";
import ContactSection from "@/components/ContactSection";
import HeroSection from "@/components/HeroSection";
import ServicesSection from "@/components/ServicesSection";

const sections = [
  <HeroSection key="hero" />,
  <ServicesSection key="services" />,
  <AboutSection key="about" />,
  <ContactSection key="contact" />,
];

export default function Home() {
  const [activeIndex, setActiveIndex] = useState(0);

  useEffect(() => {
    const handleScroll = (event: WheelEvent) => {
      if (event.deltaY > 0) {
        setActiveIndex((prev) => Math.min(prev + 1, sections.length - 1));
      } else {
        setActiveIndex((prev) => Math.max(prev - 1, 0));
      }
    };

    window.addEventListener("wheel", handleScroll);
    return () => window.removeEventListener("wheel", handleScroll);
  }, []);

  return (
    <>
      <motion.div
        animate={{ y: `-${activeIndex * 100}vh` }}
        transition={{ duration: 0.8, ease: "easeInOut" }}
        className="h-screen w-full"
      >
        {sections.map((section, index) => (
          <div key={index} className="h-screen w-full">
            {section}
          </div>
        ))}
      </motion.div>
    </>
  );
}
