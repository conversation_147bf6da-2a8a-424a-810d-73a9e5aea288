@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\Users\<USER>\Desktop\Learning\Codes\dapp\server\node_modules\.pnpm\swagger-jsdoc@6.2.8_openapi-types@12.1.3\node_modules\swagger-jsdoc\bin\node_modules;C:\Users\<USER>\Desktop\Learning\Codes\dapp\server\node_modules\.pnpm\swagger-jsdoc@6.2.8_openapi-types@12.1.3\node_modules\swagger-jsdoc\node_modules;C:\Users\<USER>\Desktop\Learning\Codes\dapp\server\node_modules\.pnpm\swagger-jsdoc@6.2.8_openapi-types@12.1.3\node_modules;C:\Users\<USER>\Desktop\Learning\Codes\dapp\server\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\Users\<USER>\Desktop\Learning\Codes\dapp\server\node_modules\.pnpm\swagger-jsdoc@6.2.8_openapi-types@12.1.3\node_modules\swagger-jsdoc\bin\node_modules;C:\Users\<USER>\Desktop\Learning\Codes\dapp\server\node_modules\.pnpm\swagger-jsdoc@6.2.8_openapi-types@12.1.3\node_modules\swagger-jsdoc\node_modules;C:\Users\<USER>\Desktop\Learning\Codes\dapp\server\node_modules\.pnpm\swagger-jsdoc@6.2.8_openapi-types@12.1.3\node_modules;C:\Users\<USER>\Desktop\Learning\Codes\dapp\server\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\swagger-jsdoc\bin\swagger-jsdoc.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\swagger-jsdoc\bin\swagger-jsdoc.js" %*
)
