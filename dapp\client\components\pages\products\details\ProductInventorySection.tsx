"use client";

import React, { useState } from "react";

import { AlertTriangle, CheckCircle, Package, XCircle } from "lucide-react";

import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Product } from "@/types/product";

type ProductSectionProps = {
  product: Product;
  isEditing: boolean;
  onProductUpdate: (product: Product) => void;
};

export const ProductInventorySection = ({
  product,
  isEditing,
  onProductUpdate,
}: ProductSectionProps) => {
  const [editedProduct, setEditedProduct] = useState(product);

  const handleInputChange = (field: string, value: string) => {
    setEditedProduct((prev) => ({
      ...prev,
      [field]: field === "stock" ? parseInt(value) || 0 : value,
    }));
  };

  const stock = product.stock || 0;

  const getStockStatus = (stock: number) => {
    if (stock === 0)
      return {
        text: "Out of Stock",
        color: "text-red-600",
        bgColor: "bg-red-100",
        icon: XCircle,
      };
    if (stock < 10)
      return {
        text: "Low Stock",
        color: "text-orange-600",
        bgColor: "bg-orange-100",
        icon: AlertTriangle,
      };
    return {
      text: "In Stock",
      color: "text-green-600",
      bgColor: "bg-green-100",
      icon: CheckCircle,
    };
  };

  const stockStatus = getStockStatus(stock);
  const StockIcon = stockStatus.icon;

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Package className="h-5 w-5" />
          Inventory
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Stock Quantity */}
        <div>
          <Label htmlFor="stock">Stock Quantity</Label>
          {isEditing ? (
            <Input
              id="stock"
              type="number"
              value={editedProduct.stock || 0}
              onChange={(e) => handleInputChange("stock", e.target.value)}
              className="mt-1"
              min="0"
            />
          ) : (
            <div className="mt-1 flex items-center gap-2">
              <span className="text-2xl font-bold">{stock}</span>
              <span className="text-gray-500">units</span>
            </div>
          )}
        </div>

        {/* Stock Status */}
        {!isEditing && (
          <div>
            <Label>Stock Status</Label>
            <div className="mt-1 flex items-center gap-2">
              <Badge
                className={`${stockStatus.bgColor} ${stockStatus.color} hover:${stockStatus.bgColor}`}
              >
                <StockIcon className="mr-1 h-3 w-3" />
                {stockStatus.text}
              </Badge>
            </div>
          </div>
        )}

        {/* SKU */}
        <div>
          <Label htmlFor="sku">SKU</Label>
          {isEditing ? (
            <Input id="sku" placeholder="Enter SKU" className="mt-1" />
          ) : (
            <p className="mt-1 font-mono text-sm text-gray-600">
              {product.sku ||
                `SKU-${(product.id || product._id || "UNKNOWN").toString().padStart(6, "0")}`}
            </p>
          )}
        </div>

        {/* Inventory Alerts */}
        {!isEditing && (
          <div className="space-y-2 rounded-md bg-gray-50 p-3">
            <h4 className="text-sm font-medium">Inventory Alerts</h4>
            <div className="space-y-1 text-sm">
              {stock === 0 ? (
                <div className="flex items-center gap-1 text-red-600">
                  <XCircle className="h-3 w-3" />
                  <span>Product is out of stock</span>
                </div>
              ) : stock < 10 ? (
                <div className="flex items-center gap-1 text-orange-600">
                  <AlertTriangle className="h-3 w-3" />
                  <span>Low stock warning</span>
                </div>
              ) : (
                <div className="flex items-center gap-1 text-green-600">
                  <CheckCircle className="h-3 w-3" />
                  <span>Stock levels are healthy</span>
                </div>
              )}
              {product.lowStockThreshold && (
                <div className="text-gray-500">
                  Reorder point: {product.lowStockThreshold} units
                </div>
              )}
              {product.stockManagement && (
                <div className="text-gray-500">
                  Management: {product.stockManagement}
                </div>
              )}
            </div>
          </div>
        )}

        {/* Quick Actions */}
        {isEditing && (
          <div className="space-y-2">
            <Label>Quick Stock Actions</Label>
            <div className="flex gap-2">
              <button
                type="button"
                onClick={() => handleInputChange("stock", "0")}
                className="rounded bg-red-100 px-2 py-1 text-xs text-red-700 hover:bg-red-200"
              >
                Mark Out of Stock
              </button>
              <button
                type="button"
                onClick={() => handleInputChange("stock", "50")}
                className="rounded bg-green-100 px-2 py-1 text-xs text-green-700 hover:bg-green-200"
              >
                Restock (50)
              </button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
