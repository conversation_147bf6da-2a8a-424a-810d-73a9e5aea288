const CHUNK_PUBLIC_PATH = "server/app/admin/products/catalog-settings/page.js";
const runtime = require("../../../../chunks/ssr/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/ssr/3cfe9_next_dist_5c91d8c1._.js");
runtime.loadChunk("server/chunks/ssr/[root of the server]__3c00be2f._.js");
runtime.loadChunk("server/chunks/ssr/node_modules__pnpm_8fd9a0b0._.js");
runtime.loadChunk("server/chunks/ssr/3cfe9_next_dist_client_components_forbidden-error_506d3fd9.js");
runtime.loadChunk("server/chunks/ssr/3cfe9_next_dist_client_components_unauthorized-error_0a4c1808.js");
runtime.loadChunk("server/chunks/ssr/[root of the server]__c68f026a._.js");
runtime.loadChunk("server/chunks/ssr/_812bd65d._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/admin/products/catalog-settings/page/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/.pnpm/next@15.2.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/esm/build/templates/app-page.js?page=/admin/products/catalog-settings/page { MODULE_0 => \"[project]/node_modules/.pnpm/next@15.2.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/not-found-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_1 => \"[project]/node_modules/.pnpm/next@15.2.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/forbidden-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_2 => \"[project]/node_modules/.pnpm/next@15.2.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/unauthorized-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_3 => \"[project]/app/admin/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_4 => \"[project]/app/admin/products/catalog-settings/page.tsx [app-rsc] (ecmascript, Next.js server component)\" } [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/.pnpm/next@15.2.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/esm/build/templates/app-page.js?page=/admin/products/catalog-settings/page { MODULE_0 => \"[project]/node_modules/.pnpm/next@15.2.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/not-found-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_1 => \"[project]/node_modules/.pnpm/next@15.2.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/forbidden-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_2 => \"[project]/node_modules/.pnpm/next@15.2.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/unauthorized-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_3 => \"[project]/app/admin/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_4 => \"[project]/app/admin/products/catalog-settings/page.tsx [app-rsc] (ecmascript, Next.js server component)\" } [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
