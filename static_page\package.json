{"name": "espusla", "version": "0.0.1", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "eslint-plugin-tailwindcss": "^3.18.0", "framer-motion": "^12.5.0", "lucide-react": "^0.482.0", "next": "15.2.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@eslint/eslintrc": "^3.3.0", "@trivago/prettier-plugin-sort-imports": "^5.2.2", "@types/node": "^20.17.24", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "eslint": "^9.22.0", "eslint-config-next": "15.2.2", "eslint-config-prettier": "^9.1.0", "postcss": "^8.5.3", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^3.4.17", "typescript": "^5.8.2"}, "packageManager": "pnpm@9.15.4+sha512.b2dc20e2fc72b3e18848459b37359a32064663e5627a51e4c74b2c29dd8e8e0491483c3abb40789cfd578bf362fb6ba8261b05f0387d76792ed6e23ea3b1b6a0"}