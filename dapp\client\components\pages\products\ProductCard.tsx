"use client";

import { useState } from "react";

import clsx from "clsx";
import { Edit, Eye, MoreHorizontal, Package, Star, Trash2 } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { toast } from "sonner";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Skeleton } from "@/components/ui/skeleton";
import { ConfirmationDialog } from "@/components/ui/confirmation-dialog";
import { useProductMutations } from "@/hooks/useProducts";

type Product = {
  _id?: string;
  id?: number | string;
  name: string;
  price: number | string;
  originalPrice?: number | string;
  mainImage?: string;
  image?: string;
  category?: string;
  brand?: string;
  stock?: number;
  status?:
    | "in-stock"
    | "out-of-stock"
    | "coming-soon"
    | "archived"
    | "draft"
    | "suspended"
    | "active";
  averageRating?: number;
  rating?: number;
  isOnSale?: boolean;
  currency?: string;
  saleEndsAt?: string | Date;
};

export const ProductCard = ({
  product,
  index,
  onDelete,
}: {
  product: Product;
  index: number;
  onDelete?: () => void;
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [isDeleting, setIsDeleting] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const router = useRouter();
  const { deleteProduct } = useProductMutations();

  // Handle both backend (_id) and frontend (id) identifiers
  const productId = product._id || product.id;

  // Default values for optional properties
  const stock = product.stock ?? 0;
  const status = product.status ?? "active";
  const rating = product.averageRating || product.rating ?? 0;
  const isOnSale = product.isOnSale ?? (product.saleEndsAt ? new Date(product.saleEndsAt) > new Date() : false);

  // Handle image - backend uses mainImage, frontend uses image
  const productImage = product.mainImage || product.image || '/images/placeholder.jpg';

  // Format price with currency
  const formatPrice = (price: number | string, currency?: string) => {
    const numPrice = typeof price === 'string' ? parseFloat(price) : price;
    const currencySymbol = currency === 'EUR' ? '€' : '$';
    return `${currencySymbol}${numPrice.toFixed(2)}`;
  };

  const handleViewProduct = () => {
    router.push(`/admin/products/${productId}`);
  };

  const handleEditProduct = () => {
    // Navigate to product details page - edit mode will be handled there
    router.push(`/admin/products/${productId}?edit=true`);
  };

  const handleDeleteClick = () => {
    setShowDeleteDialog(true);
  };

  const handleConfirmDelete = async () => {
    if (!productId) {
      toast.error("Product ID not found");
      setShowDeleteDialog(false);
      return;
    }

    setIsDeleting(true);
    try {
      await deleteProduct(productId.toString());
      // Call the onDelete callback to refresh the product list
      onDelete?.();
      setShowDeleteDialog(false);
    } catch (error) {
      console.error("Failed to delete product:", error);
      // Error toast is already handled in the mutation hook
    } finally {
      setIsDeleting(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
      case "in-stock":
        return "bg-green-100 text-green-700";
      case "draft":
      case "coming-soon":
        return "bg-yellow-100 text-yellow-700";
      case "archived":
      case "suspended":
        return "bg-gray-100 text-gray-700";
      case "out-of-stock":
        return "bg-red-100 text-red-700";
      default:
        return "bg-gray-100 text-gray-700";
    }
  };

  const getStockStatus = (stock: number) => {
    if (stock === 0) return { text: "Out of Stock", color: "text-red-600" };
    if (stock < 10) return { text: "Low Stock", color: "text-orange-600" };
    return { text: "In Stock", color: "text-green-600" };
  };

  const stockStatus = getStockStatus(stock);

  return (
    <Card className="group relative w-full overflow-hidden p-3 shadow-sm transition-all hover:shadow-lg">
      {/* Status Badge */}
      {status !== "active" && (
        <Badge
          className={`absolute left-2 top-2 z-10 text-xs ${getStatusColor(status)}`}
        >
          {status.charAt(0).toUpperCase() + status.slice(1)}
        </Badge>
      )}

      {/* Sale Badge */}
      {isOnSale && (
        <Badge className="absolute right-2 top-2 z-10 bg-red-500 text-white">
          Sale
        </Badge>
      )}

      {/* Product Image */}
      <div
        className="relative mb-3 h-52 w-full cursor-pointer overflow-hidden rounded-md"
        onClick={handleViewProduct}
      >
        {isLoading && (
          <Skeleton className="absolute inset-0 size-full rounded-md" />
        )}

        <Image
          src={productImage}
          alt={product.name}
          fill
          priority={index === 0}
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 20vw"
          onLoad={() => setIsLoading(false)}
          onError={() => setIsLoading(false)}
          className={clsx(
            "rounded object-cover object-center transition-all duration-300 group-hover:scale-105",
            isLoading ? "opacity-0" : "opacity-100"
          )}
        />

        {/* Quick Actions Overlay */}
        <div className="absolute inset-0 flex items-center justify-center bg-black/50 opacity-0 transition-opacity group-hover:opacity-100">
          <Button
            size="sm"
            variant="secondary"
            onClick={(e) => {
              e.stopPropagation();
              handleViewProduct();
            }}
          >
            <Eye className="mr-2 h-4 w-4" />
            View
          </Button>
        </div>
      </div>

      <CardContent className="p-2">
        {/* Product Info */}
        <div className="mb-2">
          <Link
            href={`/admin/products/${productId}`}
            className="text-sm font-medium hover:text-blue-600 hover:underline"
          >
            {product.name}
          </Link>
          {product.category && (
            <div className="text-xs text-gray-500">{product.category}</div>
          )}
          {product.brand && (
            <div className="text-xs text-gray-400">{product.brand}</div>
          )}
        </div>

        {/* Rating */}
        {rating > 0 && (
          <div className="mb-2 flex items-center gap-1">
            <div className="flex">
              {[...Array(5)].map((_, i) => (
                <Star
                  key={i}
                  className={clsx(
                    "h-3 w-3",
                    i < rating
                      ? "fill-yellow-400 text-yellow-400"
                      : "text-gray-300"
                  )}
                />
              ))}
            </div>
            <span className="text-xs text-gray-500">({rating})</span>
          </div>
        )}

        {/* Price */}
        <div className="mb-2 flex items-center gap-2">
          <span className="font-semibold text-gray-900">
            {formatPrice(product.price, product.currency)}
          </span>
          {product.originalPrice && isOnSale && (
            <span className="text-sm text-gray-500 line-through">
              {formatPrice(product.originalPrice, product.currency)}
            </span>
          )}
        </div>

        {/* Stock Status */}
        <div className="mb-3 flex items-center gap-1">
          <Package className="h-3 w-3 text-gray-400" />
          <span className={`text-xs ${stockStatus.color}`}>
            {stockStatus.text} {stock > 0 && `(${stock})`}
          </span>
        </div>

        {/* Actions */}
        <div className="flex items-center justify-between">
          <div className="flex gap-1">
            <Button
              variant="outline"
              size="sm"
              className="h-8 px-2 text-xs"
              onClick={handleEditProduct}
            >
              <Edit className="mr-1 h-3 w-3" />
              Edit
            </Button>
          </div>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={handleViewProduct}>
                <Eye className="mr-2 h-4 w-4" />
                View Details
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleEditProduct}>
                <Edit className="mr-2 h-4 w-4" />
                Edit Product
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                className="text-red-600"
                onClick={handleDeleteClick}
                disabled={isDeleting}
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete Product
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardContent>

      {/* Delete Confirmation Dialog */}
      <ConfirmationDialog
        open={showDeleteDialog}
        onOpenChange={setShowDeleteDialog}
        title="Delete Product"
        description={`Are you sure you want to delete "${product.name}"? This action cannot be undone and will permanently remove the product from your inventory.`}
        confirmText={isDeleting ? "Deleting..." : "Delete Product"}
        cancelText="Cancel"
        onConfirm={handleConfirmDelete}
        loading={isDeleting}
        variant="destructive"
      />
    </Card>
  );
};
