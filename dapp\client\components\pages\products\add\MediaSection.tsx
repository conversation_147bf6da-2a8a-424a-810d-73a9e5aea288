import {
  Camera,
  Eye,
  Image,
  Info,
  Monitor,
  Palette,
  Smartphone,
  Star,
  Upload,
  Video,
} from "lucide-react";
import {
  FieldErrors,
  UseFormRegister,
  UseFormSetValue,
  UseFormWatch,
} from "react-hook-form";

import { FormField } from "@/components/common/FormField";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { CollapsibleSection } from "@/components/ui/collapsible-section";
import { ImportantNotice } from "@/components/ui/important-notice";
import { Input } from "@/components/ui/input";
import { ProductFormData } from "@/schemas/productSchema";

import { ImageUploadSection } from "./ImageUploadSection";

type MediaSectionProps = {
  register: UseFormRegister<ProductFormData>;
  errors: FieldErrors<ProductFormData>;
  setValue: UseFormSetValue<ProductFormData>;
  watch: UseFormWatch<ProductFormData>;
  images: { key: string; url: string }[];
  setImages: React.Dispatch<
    React.SetStateAction<{ key: string; url: string }[]>
  >;
  imageAltTexts: string[];
  setImageAltTexts: React.Dispatch<React.SetStateAction<string[]>>;
};

export const MediaSection = ({
  register,
  errors,
  setValue,
  watch,
  images,
  setImages,
  imageAltTexts,
  setImageAltTexts,
}: MediaSectionProps) => {
  return (
    <div className="space-y-8">
      {/* Section Header */}
      <div className="flex items-center gap-3 border-b border-gray-200 pb-4">
        <div className="rounded-full bg-pink-100 p-2">
          <Camera className="h-5 w-5 text-pink-600" />
        </div>
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Images & Media</h2>
          <p className="text-gray-600">
            Upload product images and media content
          </p>
        </div>
      </div>

      {/* Important Notice */}
      <ImportantNotice
        description="You must upload at least one product image and provide alt text for all images before proceeding to the next step."
        requiredFields={["Product Images", "Alt Text for All Images"]}
        tip="Product images are essential for customer engagement and sales conversion. Alt text improves accessibility and SEO rankings."
        variant="amber"
      />

      {/* Product Images Card */}
      <Card className="border-l-4 border-l-pink-500">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Image className="h-5 w-5 text-pink-600" />
            Product Images
            <Badge variant="destructive" className="text-xs">
              Required
            </Badge>
          </CardTitle>
          <p className="text-sm text-gray-600">
            Upload high-quality images to showcase your product
          </p>
        </CardHeader>
        <CardContent>
          <FormField
            id="media"
            label=""
            error={errors.mainImage?.message}
            optional={false}
          >
            <div className="space-y-4">
              <ImageUploadSection
                images={images}
                setImages={setImages}
                imageAltTexts={imageAltTexts}
                setImageAltTexts={setImageAltTexts}
                maxImages={4}
              />

              {/* Image Guidelines */}
              <div className="rounded-lg border border-pink-200 bg-pink-50 p-4">
                <div className="mb-3 flex items-start gap-2">
                  <Camera className="mt-0.5 h-4 w-4 text-pink-600" />
                  <span className="font-medium text-pink-900">
                    Image Guidelines
                  </span>
                </div>
                <div className="grid gap-3 md:grid-cols-2">
                  <div className="flex items-center gap-2">
                    <Monitor className="h-4 w-4 text-pink-600" />
                    <span className="text-sm text-pink-800">
                      Minimum 800 x 800 px
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Palette className="h-4 w-4 text-pink-600" />
                    <span className="text-sm text-pink-800">
                      JPG, PNG, WebP formats
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Star className="h-4 w-4 text-pink-600" />
                    <span className="text-sm text-pink-800">
                      Square aspect ratio preferred
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Eye className="h-4 w-4 text-pink-600" />
                    <span className="text-sm text-pink-800">
                      First image is main display
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </FormField>
        </CardContent>
      </Card>

      {/* Video Content - Collapsible */}
      <CollapsibleSection
        title="Video Content"
        description="Add video URLs to showcase your product in action"
        icon={<Video className="h-5 w-5 text-purple-600" />}
        borderColor="border-l-purple-500"
        defaultOpen={false}
        isOptional={true}
      >
        <div className="space-y-4">
          {/* Video URL */}
          <FormField id="videoUrl" label="Video URL" optional={true}>
            <Input
              id="videoUrl"
              {...register("videoUrl")}
              placeholder="https://youtube.com/watch?v=... or https://vimeo.com/..."
              className="border-2 focus:border-purple-500"
            />
          </FormField>

          {/* Video Tips */}
          <div className="rounded-lg border border-purple-200 bg-purple-50 p-3">
            <div className="flex items-start gap-2">
              <Video className="mt-0.5 h-4 w-4 text-purple-600" />
              <div className="text-sm text-purple-800">
                <strong>Video Tips:</strong> Product demos, unboxing videos, and
                360° views increase conversion rates by up to 80%
              </div>
            </div>
          </div>
        </div>
      </CollapsibleSection>

      {/* 3D Models & AR - Collapsible */}
      <CollapsibleSection
        title="3D Models & AR"
        description="Add 3D models for immersive product experiences"
        icon={<Monitor className="h-5 w-5 text-green-600" />}
        borderColor="border-l-green-500"
        defaultOpen={false}
        isOptional={true}
      >
        <div className="space-y-4">
          {/* 3D Model URL */}
          <FormField id="threeDModelUrl" label="3D Model URL" optional={true}>
            <Input
              id="threeDModelUrl"
              {...register("threeDModelUrl")}
              placeholder="https://example.com/model.glb or .usdz file URL"
              className="border-2 focus:border-green-500"
            />
          </FormField>

          {/* AR Instructions */}
          <div className="rounded-lg border border-green-200 bg-green-50 p-3">
            <div className="flex items-start gap-2">
              <Smartphone className="mt-0.5 h-4 w-4 text-green-600" />
              <div className="text-sm text-green-800">
                <strong>AR Ready:</strong> Upload .glb or .usdz files to enable
                View in AR on mobile devices
              </div>
            </div>
          </div>
        </div>
      </CollapsibleSection>

      {/* Media Summary Card */}
      <Card className="border-pink-200 bg-gradient-to-r from-pink-50 to-purple-50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-pink-800">
            <Upload className="h-5 w-5" />
            Media Best Practices
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2">
            <div className="flex items-start gap-3">
              <div className="rounded-full bg-pink-100 p-2">
                <Image className="h-4 w-4 text-pink-600" />
              </div>
              <div>
                <h4 className="font-medium text-pink-900">
                  High Quality Images
                </h4>
                <p className="mt-1 text-sm text-pink-700">
                  Use professional lighting and multiple angles to showcase
                  product details
                </p>
              </div>
            </div>

            <div className="flex items-start gap-3">
              <div className="rounded-full bg-purple-100 p-2">
                <Video className="h-4 w-4 text-purple-600" />
              </div>
              <div>
                <h4 className="font-medium text-purple-900">Video Content</h4>
                <p className="mt-1 text-sm text-purple-700">
                  Show products in use, demonstrate features, and provide 360°
                  views
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
