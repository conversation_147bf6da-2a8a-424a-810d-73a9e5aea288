import { CurrencyUnit, ShippingStatus } from "./common";
import { Transaction } from "./transaction";
import { User } from "./user";

export type OrderStatus =
  | "pending"
  | "paid"
  | "shipped"
  | "delivered"
  | "cancelled"
  | "refunded"
  | "returned"
  | "failed";

export type OrderItem = {
  productId: string;
  name: string;
  quantity: number;
  price: number;
  currency: CurrencyUnit;
  image: string;
};

export type OrderShipping = {
  address: string;
  city: string;
  postalCode: string;
  country: string;
  trackingNumber?: string;
  shippedAt?: string;
  deliveredAt?: string;
  shippingCost: number;
};

export type Order = {
  id: string;
  user: User;

  items: OrderItem[];
  totalAmount: number;
  currency: CurrencyUnit;

  status: OrderStatus;
  shippingStatus: ShippingStatus;
  shipping: OrderShipping;
  payment: Transaction;

  notes?: string;
  placedAt: string;
  updatedAt: string;
};
