// schemas/productSchema.ts
import { z } from "zod";

// Enhanced constants for enum values
export const conditionValues = [
  "new",
  "like-new",
  "excellent",
  "good",
  "fair",
  "used",
  "refurbished",
  "vintage",
  "antique",
  "damaged",
] as const;

export const productStatusValues = [
  "in-stock",
  "out-of-stock",
  "coming-soon",
  "archived",
  "draft",
  "suspended",
] as const;

export const productTypeValues = [
  "physical",
  "digital",
  "service",
  "subscription",
  "bundle",
] as const;

export const visibilityValues = [
  "public",
  "private",
  "hidden",
  "password-protected",
] as const;

export const taxStatusValues = [
  "taxable",
  "tax-exempt",
  "shipping-only",
] as const;

export const stockManagementValues = [
  "track",
  "no-track",
  "backorder",
] as const;

export const shippingClassValues = [
  "standard",
  "express",
  "overnight",
  "international",
  "heavy",
  "fragile",
  "digital-only",
] as const;

export const shippingTimeValues = [
  "same-day",
  "1-2-business-days",
  "2-3-business-days",
  "3-5-business-days",
  "5-7-business-days",
  "7-10-business-days",
  "10-14-business-days",
  "2-3-weeks",
  "3-4-weeks",
  "4-6-weeks",
] as const;

export const currencyValues = ["EUR", "USD"] as const;
export const weightUnitValues = ["g", "kg"] as const;
export const dimensionUnitValues = ["mm", "cm"] as const;
export const ageRestrictionValues = ["none", "18+", "21+"] as const;

// Schema definitions
const dimensionsSchema = z.object({
  width: z.number().positive("Width must be positive"),
  height: z.number().positive("Height must be positive"),
  depth: z.number().positive("Depth must be positive"),
  unit: z.enum(dimensionUnitValues),
});

const weightSchema = z.object({
  value: z.number().positive("Weight must be positive"),
  unit: z.enum(weightUnitValues),
});

// Enhanced address schema
const addressSchema = z
  .object({
    street: z.string().optional(),
    city: z.string().optional(),
    state: z.string().optional(),
    postalCode: z.string().optional(),
    country: z.string().optional(),
    coordinates: z
      .object({
        latitude: z.number(),
        longitude: z.number(),
      })
      .optional(),
  })
  .optional();

// Enhanced sections of the product schema
export const basicInfoSchema = z.object({
  name: z.string().min(1, "Name is required"),
  brand: z.string().min(1, "Brand is required"),
  model: z.string().optional(),
  description: z
    .string()
    .min(10, "Description should be at least 10 characters"),
  shortDescription: z.string().optional(),
  productType: z.enum(productTypeValues).default("physical"),
  visibility: z.enum(visibilityValues).default("public"),
});

// Base pricing schema without refinements (for use in productSchema.shape)
export const basePricingSchema = z.object({
  price: z.number().positive("Price must be a positive number"),
  originalPrice: z.preprocess((val) => {
    if (val === "" || val === null || val === undefined || Number.isNaN(val)) {
      return undefined;
    }
    const numVal = Number(val);
    return Number.isNaN(numVal) ? undefined : numVal;
  }, z.number().positive("Original price must be a positive number").optional()),
  salePrice: z.preprocess((val) => {
    if (val === "" || val === null || val === undefined || Number.isNaN(val)) {
      return undefined;
    }
    const numVal = Number(val);
    return Number.isNaN(numVal) ? undefined : numVal;
  }, z.number().positive("Sale price must be a positive number").optional()),
  currency: z.enum(currencyValues),
  isOnSale: z.boolean().default(false),
  saleEndsAt: z.string().optional(),
  costPrice: z
    .number()
    .positive("Cost price is required for business intelligence"),
  taxStatus: z.enum(taxStatusValues).default("taxable"),
  taxClass: z.string().optional(),
  minimumOrderQuantity: z.preprocess((val) => {
    if (val === "" || val === null || val === undefined || Number.isNaN(val)) {
      return undefined;
    }
    const numVal = Number(val);
    return Number.isNaN(numVal) ? undefined : numVal;
  }, z.number().int().positive().optional()),
  maximumOrderQuantity: z.preprocess((val) => {
    if (val === "" || val === null || val === undefined || Number.isNaN(val)) {
      return undefined;
    }
    const numVal = Number(val);
    return Number.isNaN(numVal) ? undefined : numVal;
  }, z.number().int().positive().optional()),
});

// Pricing schema with refinements (for standalone use)
export const pricingSchema = basePricingSchema
  .refine(
    (data) => {
      // If on sale, sale price must be lower than regular price
      if (data.isOnSale && data.salePrice) {
        return data.salePrice < data.price;
      }
      return true;
    },
    {
      message: "Sale price must be lower than regular price",
      path: ["salePrice"],
    }
  )
  .refine(
    (data) => {
      // If on sale, original price should equal regular price (for backwards compatibility)
      if (data.isOnSale && data.originalPrice) {
        return data.originalPrice >= data.price;
      }
      return true;
    },
    {
      message: "Original price should be at least the regular price",
      path: ["originalPrice"],
    }
  );

export const inventorySchema = z.object({
  stock: z
    .number()
    .int()
    .nonnegative("Stock must be zero or a positive number"),
  condition: z.enum(conditionValues),
  isPublished: z.boolean().default(true),
  stockManagement: z.enum(stockManagementValues).default("track"),
  lowStockThreshold: z.preprocess((val) => {
    if (val === "" || val === null || val === undefined || Number.isNaN(val)) {
      return undefined;
    }
    const numVal = Number(val);
    return Number.isNaN(numVal) ? undefined : numVal;
  }, z.number().int().nonnegative().optional()),
  backorderAllowed: z.boolean().default(false),
  trackQuantity: z.boolean().default(true),
  soldIndividually: z.boolean().default(false),
});

export const detailsSchema = z.object({
  category: z.string().min(1, "Category is required"),
  subcategory: z.string().optional(),
  material: z.string().min(1, "Material is required"),
  dimensions: dimensionsSchema.optional(),
  weight: weightSchema.optional(),
  yearMade: z.preprocess((val) => {
    if (val === "" || val === null || val === undefined || Number.isNaN(val)) {
      return undefined;
    }
    const numVal = Number(val);
    return Number.isNaN(numVal) ? undefined : numVal;
  }, z.number().int("Year must be a whole number").min(1800).max(new Date().getFullYear()).optional()),
  tags: z.array(z.string()).min(1, "At least one tag is required"),
  color: z.string().min(1, "Color is required"),
  size: z.string().optional(),
});

export const imagesSchema = z.object({
  mainImage: z.string().url("Main image must be a valid URL"),
  images: z.array(z.string().url("Image must be a valid URL")).optional(),
  imageAltTexts: z.array(z.string()).optional(),
  videoUrl: z.preprocess(
    (val) =>
      val === "" || val === null || val === undefined ? undefined : val,
    z.string().url().optional()
  ),
  threeDModelUrl: z.preprocess(
    (val) =>
      val === "" || val === null || val === undefined ? undefined : val,
    z.string().url().optional()
  ),
});

export const shippingSchema = z.object({
  location: z.string().optional(),
  address: addressSchema,
  shippingCost: z.preprocess((val) => {
    if (val === "" || val === null || val === undefined || Number.isNaN(val)) {
      return undefined;
    }
    const numVal = Number(val);
    return Number.isNaN(numVal) ? undefined : numVal;
  }, z.number().nonnegative("Shipping cost must be a non-negative number")),
  shippingTime: z.enum(shippingTimeValues, {
    required_error: "Estimated shipping time is required",
  }),
  freeShipping: z.boolean().default(false),
  shippingClass: z.enum(shippingClassValues, {
    required_error: "Shipping class is required",
  }),
  requiresShipping: z.boolean().default(true),
  separateShipping: z.boolean().default(false),
  shippingDimensions: dimensionsSchema.optional(),
  shippingWeight: weightSchema.optional(),
});

export const availabilitySchema = z.object({
  status: z.enum(productStatusValues).default("draft"),
  ageRestriction: z.enum(ageRestrictionValues).default("none"),
  availableFrom: z.string().optional(),
  availableUntil: z.string().optional(),
  featured: z.boolean().default(false),
  sticky: z.boolean().default(false),
  downloadable: z.boolean().default(false),
  virtual: z.boolean().default(false),
});

// New SEO schema
export const seoSchema = z.object({
  metaTitle: z.string().optional(),
  metaDescription: z.string().optional(),
  focusKeyword: z.string().optional(),
  slug: z.string().optional(),
  canonicalUrl: z.preprocess(
    (val) =>
      val === "" || val === null || val === undefined ? undefined : val,
    z.string().url().optional()
  ),
});

// New warranty schema
export const warrantySchema = z.object({
  duration: z.preprocess((val) => {
    if (val === "" || val === null || val === undefined || Number.isNaN(val)) {
      return undefined;
    }
    const numVal = Number(val);
    return Number.isNaN(numVal) ? undefined : numVal;
  }, z.number().int().positive().optional()),
  type: z.enum(["manufacturer", "seller", "extended"]).optional(),
  terms: z.string().optional(),
  coverage: z.array(z.string()).optional(),
});

// Return policy schema
export const returnPolicySchema = z.object({
  allowed: z.boolean().default(true),
  period: z.number().int().positive().optional(),
  conditions: z.array(z.string()).optional(),
  restockingFee: z.number().nonnegative().optional(),
});

// Enhanced product schema with conditional fields based on product type
export const productSchema = z
  .object({
    ...basicInfoSchema.shape,
    ...basePricingSchema.shape,
    ...inventorySchema.shape,
    ...detailsSchema.shape,
    ...imagesSchema.shape,
    ...shippingSchema.shape,
    ...availabilitySchema.shape,
    ...seoSchema.shape,
    ...warrantySchema.shape,
    ...returnPolicySchema.shape,
  })
  .refine(
    (data) => {
      // If on sale, sale price must be lower than regular price
      if (data.isOnSale && data.salePrice) {
        return data.salePrice < data.price;
      }
      return true;
    },
    {
      message: "Sale price must be lower than regular price",
      path: ["salePrice"],
    }
  )
  .refine(
    (data) => {
      // If on sale, original price should equal regular price (for backwards compatibility)
      if (data.isOnSale && data.originalPrice) {
        return data.originalPrice >= data.price;
      }
      return true;
    },
    {
      message: "Original price should be at least the regular price",
      path: ["originalPrice"],
    }
  )
  .refine(
    (data) => {
      // Conditional validation based on product type
      if (data.productType === "digital") {
        return data.requiresShipping === false;
      }
      if (data.productType === "service") {
        return data.virtual === true;
      }
      return true;
    },
    {
      message: "Product configuration doesn't match the selected product type",
    }
  )
  .transform((data) => {
    // Clean up fields that don't apply to certain product types
    if (data.productType === "digital") {
      // For digital products, remove physical-only fields
      return {
        ...data,
        yearMade: undefined,
        shippingCost: undefined,
        dimensions: undefined,
        weight: undefined,
        requiresShipping: false,
        virtual: true,
      };
    }
    if (data.productType === "service") {
      // For services, remove physical-only fields
      return {
        ...data,
        yearMade: undefined,
        shippingCost: undefined,
        dimensions: undefined,
        weight: undefined,
        requiresShipping: false,
        virtual: true,
        stock: 0, // Services don't have stock
      };
    }
    return data;
  });

export type ProductFormData = z.infer<typeof productSchema>;

// Individual section types for component props
export type BasicInfoFormData = z.infer<typeof basicInfoSchema>;
export type PricingFormData = z.infer<typeof basePricingSchema>;
export type InventoryFormData = z.infer<typeof inventorySchema>;
export type DetailsFormData = z.infer<typeof detailsSchema>;
export type ImagesFormData = z.infer<typeof imagesSchema>;
export type ShippingFormData = z.infer<typeof shippingSchema>;
export type AvailabilityFormData = z.infer<typeof availabilitySchema>;
export type SEOFormData = z.infer<typeof seoSchema>;
export type WarrantyFormData = z.infer<typeof warrantySchema>;
export type ReturnPolicyFormData = z.infer<typeof returnPolicySchema>;
