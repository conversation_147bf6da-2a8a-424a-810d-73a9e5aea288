Admin Panel:

Project structure

<pre lang="markdown"> 
dapp/
├── next/
├── app/
│   ├── (admin)/
│   │   ├── (auth)/
│   │   │   ├── forgot-password/
│   │   │   ├── help/
│   │   │   └── login/
│   │   └── (root)/
│   │       └── page.tsx
│   ├── appearance/
│   ├── customers/
│   ├── dashboard/
│   ├── hot-offers/
│   ├── orders/
│   ├── products/
│   │   ├── add/
│   │   ├── brands/
│   │   ├── categories/
│   │   └── list/
│   ├── reviews/
│   ├── sellers/
│   ├── settings/
│   ├── statistics/
│   ├── transactions/
│   ├── globals.css
│   ├── layout.tsx
│   ├── (eshop)/
│   ├── api/
│   │   └── uploadthing/
│   │       ├── delete/
│   │       │   ├── route.ts
│   │       ├──  route.ts
│   │       ├──  core.ts
├── components/
│   ├── common/
│   ├── layout/
│   ├── pages/
│   │   └── products/
│   └── ui/
├── constants/
│   └── products.ts
├── lib/
│   ├── uploadthing.ts
│   └── utils.ts
├── node_modules/
├── public/
├── schemas/
├── types/
├── .env
├── .gitignore
├── .prettierignore
├── .prettierrc
└── components.json
 </pre>
