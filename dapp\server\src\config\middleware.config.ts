import cors from "cors";
import express from "express";
import type { Express } from "express";
import helmet from "helmet";
import path from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

export const configMiddleware = (app: Express) => {
  // Configure Helmet with settings that allow Swagger UI to work
  app.use(
    helmet({
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          scriptSrc: ["'self'", "'unsafe-inline'", "'unsafe-eval'"],
          styleSrc: ["'self'", "'unsafe-inline'"],
          imgSrc: ["'self'", "data:", "https:"],
          connectSrc: ["'self'", "http://localhost:*"],
        },
      },
    })
  );

  // Configure CORS for development
  app.use(
    cors({
      origin: "*", // Allow all origins in development
      methods: ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"],
      allowedHeaders: ["Content-Type", "Authorization", "X-Requested-With"],
      credentials: true,
      maxAge: 86400, // 24 hours
    })
  );

  app.use(express.json({ limit: "50mb" }));
  app.use(express.urlencoded({ extended: true, limit: "50mb" }));

  // Serve static files from the client/dist directory (for production)
  app.use(express.static(path.join(__dirname, "../../client/dist")));

  // Serve static files from the public directory (for development)
  app.use(express.static(path.join(__dirname, "../../public")));

  // Log all requests
  app.use((req, _, next) => {
    console.info(`${req.method} ${req.url}`);
    next();
  });
};
