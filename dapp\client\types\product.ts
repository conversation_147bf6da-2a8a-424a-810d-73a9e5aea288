import {
  AgeRestriction,
  Condition,
  CurrencyUnit,
  DimensionUnit,
  WeightUnit,
} from "./common";
import { Review } from "./review";
import { User } from "./user";

// Enhanced types for better product management
export type ProductType =
  | "physical"
  | "digital"
  | "service"
  | "subscription"
  | "bundle";

export type ProductVisibility =
  | "public"
  | "private"
  | "hidden"
  | "password-protected";

export type TaxStatus = "taxable" | "tax-exempt" | "shipping-only";

export type StockManagement = "track" | "no-track" | "backorder";

export type ShippingClass =
  | "standard"
  | "express"
  | "overnight"
  | "international"
  | "heavy"
  | "fragile"
  | "digital-only";

export type ProductOrigin = {
  country: string;
  region?: string;
  city?: string;
  manufacturer?: string;
  madeIn?: string;
};

export type ProductAddress = {
  street?: string;
  city?: string;
  state?: string;
  postalCode?: string;
  country?: string;
  coordinates?: {
    latitude: number;
    longitude: number;
  };
};

export type ProductVariant = {
  id: string;
  name: string;
  sku?: string;
  price?: number;
  stock?: number;
  image?: string;
  attributes: Record<string, string>; // e.g., { color: "red", size: "M" }
};

export type ProductAttribute = {
  name: string;
  values: string[];
  visible: boolean;
  variation: boolean; // Used for variations
};

export type ProductSEO = {
  metaTitle?: string;
  metaDescription?: string;
  focusKeyword?: string;
  slug?: string;
  canonicalUrl?: string;
};

export type ProductCompliance = {
  certifications?: string[];
  warnings?: string[];
  restrictions?: string[];
  requiresLicense?: boolean;
  hazardousMaterial?: boolean;
};

export type ProductWarranty = {
  duration?: number; // in months
  type?: "manufacturer" | "seller" | "extended";
  terms?: string;
  coverage?: string[];
};

export type ProductReturn = {
  returnable: boolean;
  returnWindow?: number; // in days
  returnPolicy?: string;
  restockingFee?: number;
  returnShippingPaid?: "buyer" | "seller" | "shared";
};

export type ProductStatus =
  | "in-stock"
  | "out-of-stock"
  | "coming-soon"
  | "archived"
  | "draft"
  | "suspended";

export type Dimensions = {
  width: number;
  height: number;
  depth: number;
  unit: DimensionUnit;
};

export type Weight = {
  value: number;
  unit: WeightUnit;
};

export type ProductBasicInfo = {
  id: string;
  name: string;
  slug?: string;
  brand: string;
  model?: string;
  description: string;
  shortDescription?: string;
  sku?: string;
  barcode?: string;
  productType: ProductType;
  visibility: ProductVisibility;
};

export type ProductPricing = {
  price: number;
  originalPrice?: number;
  currency: CurrencyUnit;
  saleEndsAt?: string;
  costPrice?: number; // For profit calculations
  taxStatus: TaxStatus;
  taxClass?: string;
  minimumOrderQuantity?: number;
  maximumOrderQuantity?: number;
};

export type ProductInventory = {
  stock: number;
  condition: Condition;
  isPublished: boolean;
  stockManagement: StockManagement;
  lowStockThreshold?: number;
  backorderAllowed?: boolean;
  trackQuantity?: boolean;
  soldIndividually?: boolean; // Prevent multiple quantities
};

export type ProductDetails = {
  category: string;
  subcategory?: string;
  material?: string;
  dimensions?: Dimensions;
  weight?: Weight;
  yearMade?: number;
  tags?: string[];
  color?: string;
  size?: string;
  attributes?: ProductAttribute[];
  variants?: ProductVariant[];
  origin?: ProductOrigin;
  compliance?: ProductCompliance;
};

export type ProductImages = {
  mainImage: string;
  images?: string[];
  imageAltTexts?: string[];
  videoUrl?: string;
  threeDModelUrl?: string;
};

export type ProductShipping = {
  location?: string;
  address?: ProductAddress; // Enhanced address structure
  shippingCost?: number;
  shippingTime?: string;
  freeShipping?: boolean;
  shippingClass?: ShippingClass;
  requiresShipping?: boolean;
  separateShipping?: boolean;
  shippingDimensions?: Dimensions;
  shippingWeight?: Weight;
};

export type ProductRelations = {
  seller: User;
  shopId?: string;
  reviews?: Review[];
  relatedProducts?: string[]; // Product IDs
  crossSellProducts?: string[]; // Product IDs
  upSellProducts?: string[]; // Product IDs
  bundledProducts?: string[]; // For bundle products
};

export type ProductMetrics = {
  averageRating?: number;
  reviewCount?: number;
  views?: number;
  purchases?: number;
  wishlistCount?: number;
  conversionRate?: number;
  returnRate?: number;
  profitMargin?: number;
};

export type ProductAvailability = {
  status: ProductStatus;
  ageRestriction?: AgeRestriction;
  availableFrom?: string;
  availableUntil?: string;
  featured?: boolean;
  sticky?: boolean; // Pin to top of category
  downloadable?: boolean;
  virtual?: boolean;
};

export type ProductSEOSection = {
  seo?: ProductSEO;
};

export type ProductWarrantySection = {
  warranty?: ProductWarranty;
};

export type ProductReturnSection = {
  returnPolicy?: ProductReturn;
};

export type ProductTimestamps = {
  createdAt: string;
  updatedAt: string;
  publishedAt?: string;
  lastModifiedBy?: string;
};

// Enhanced Product type with all new properties
export type Product = ProductBasicInfo &
  ProductPricing &
  ProductInventory &
  ProductDetails &
  ProductImages &
  ProductShipping &
  ProductRelations &
  ProductMetrics &
  ProductAvailability &
  ProductSEOSection &
  ProductWarrantySection &
  ProductReturnSection &
  ProductTimestamps;
