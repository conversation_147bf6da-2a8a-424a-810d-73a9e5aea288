import { zodResolver } from "@hookform/resolvers/zod";

import { useState } from "react";

import { useRouter } from "next/navigation";
import { Resolver, SubmitHandler, useForm } from "react-hook-form";
import { toast } from "sonner";

import { useProductMutations } from "@/hooks/useProducts";
import { ProductFormData, productSchema } from "@/schemas/productSchema";
import { SectionId } from "@/types/form-section";

/**
 * Custom hook for managing a multi-section product form
 * Handles form state, validation, and section navigation
 */
export function useProductFormWithSections() {
  const router = useRouter();
  const { createProduct, loading: creating } = useProductMutations();

  // Section navigation state
  const [currentSection, setCurrentSection] = useState<SectionId>("basic-info");
  const [completedSections, setCompletedSections] = useState<SectionId[]>([]);

  // Form data state
  const [tags, setTags] = useState<string[]>([]);
  const [images, setImages] = useState<{ key: string; url: string }[]>([]);
  const [imageAltTexts, setImageAltTexts] = useState<string[]>([]);

  // Initialize react-hook-form
  const {
    register,
    handleSubmit,
    setValue,
    watch,
    getValues,
    formState: { errors, isValid },
    trigger,
  } = useForm<ProductFormData>({
    resolver: zodResolver(productSchema) as Resolver<ProductFormData>,
    defaultValues: {
      currency: "EUR",
      condition: "new",
      stock: 1,
      isPublished: true,
      status: "draft",
      ageRestriction: "none",
      freeShipping: false,
      productType: "physical",
      visibility: "public",
      taxStatus: "taxable",
      stockManagement: "track",
      trackQuantity: true,
      backorderAllowed: false,
      soldIndividually: false,
      requiresShipping: true,
      separateShipping: false,
      shippingClass: "standard",
      shippingTime: "3-5-business-days",
      featured: false,
      sticky: false,
      downloadable: false,
      virtual: false,
      costPrice: 0,
    },
    mode: "onChange",
  });

  // Define the order of form sections
  const SECTIONS_ORDER: SectionId[] = [
    "basic-info",
    "pricing",
    "inventory",
    "details",
    "media",
    "shipping",
    "availability",
    "seo",
    "warranty",
    "advanced",
  ];

  // Calculate progress percentage
  const progress = Math.round(
    (completedSections.length / SECTIONS_ORDER.length) * 100
  );

  /**
   * Validate the current section and navigate to the next if valid
   */
  const validateAndNavigate = async (nextSection: SectionId) => {
    console.log("🔍 validateAndNavigate called");
    console.log("🔍 Current section:", currentSection);
    console.log("🔍 Next section:", nextSection);

    let fieldsToValidate: (keyof ProductFormData)[] = [];

    // Determine which fields to validate based on current section
    switch (currentSection) {
      case "basic-info":
        fieldsToValidate = ["name", "brand", "description", "productType"];
        break;
      case "pricing":
        fieldsToValidate = ["price", "currency", "taxStatus", "costPrice"];
        break;
      case "inventory":
        fieldsToValidate = ["stock", "condition", "stockManagement"];
        break;
      case "details":
        fieldsToValidate = ["category", "material", "tags", "color"];
        break;
      case "media":
        fieldsToValidate = ["mainImage"];
        // Additional validation for alt texts will be handled separately
        break;
      case "shipping":
        // Only validate shipping fields if shipping is required
        const requiresShipping = getValues("requiresShipping");
        if (requiresShipping) {
          const freeShipping = getValues("freeShipping");
          // Always require shipping class and shipping time
          // Only require shipping cost if not free shipping
          fieldsToValidate = freeShipping
            ? ["shippingClass", "shippingTime"]
            : ["shippingClass", "shippingCost", "shippingTime"];
        } else {
          fieldsToValidate = [];
        }
        break;
      case "availability":
        fieldsToValidate = ["status"];
        break;
      // SEO, warranty, and advanced are optional sections
      case "seo":
      case "warranty":
      case "advanced":
        fieldsToValidate = []; // No required fields for optional sections
        break;
    }

    console.log("🔍 Fields to validate:", fieldsToValidate);

    // Trigger validation for the specified fields
    let isValid = await trigger(fieldsToValidate as any);

    // Additional validation for media section (alt texts)
    if (currentSection === "media" && isValid) {
      if (images.length > 0) {
        const missingAltTexts = images.some(
          (_, index) =>
            !imageAltTexts[index] || imageAltTexts[index].trim() === ""
        );
        if (missingAltTexts) {
          isValid = false;
        }
      }
    }

    console.log("🔍 Validation result:", isValid);

    if (isValid) {
      console.log("🔍 Validation passed, navigating to next section");
      // Add current section to completed sections if not already there
      if (!completedSections.includes(currentSection)) {
        console.log("🔍 Adding current section to completed:", currentSection);
        setCompletedSections([...completedSections, currentSection]);
      }

      // Navigate to next section
      console.log("🔍 Setting current section to:", nextSection);
      setCurrentSection(nextSection);
    } else {
      console.log("🔍 Validation failed, showing error message");
      // Show specific error messages for each section
      switch (currentSection) {
        case "basic-info":
          toast.error(
            "Please complete all required basic information: Product Name, Brand, Description, and Product Type"
          );
          break;
        case "pricing":
          toast.error(
            "Please complete all required pricing fields: Regular Price, Currency, Tax Status, and Cost Price for business intelligence"
          );
          break;
        case "inventory":
          toast.error(
            "Please complete all required inventory fields: Stock Quantity, Condition, and Stock Management"
          );
          break;
        case "details":
          toast.error(
            "Please complete all required details: Category, Material, Product Tags, and Color are required"
          );
          break;
        case "media":
          if (images.length === 0) {
            toast.error("Please upload at least one product image");
          } else {
            const missingAltTexts = images.some(
              (_, index) =>
                !imageAltTexts[index] || imageAltTexts[index].trim() === ""
            );
            if (missingAltTexts) {
              toast.error(
                "Please add alt text for all images. Alt text is required for accessibility and SEO."
              );
            } else {
              toast.error("Please complete all required media fields");
            }
          }
          break;
        case "shipping":
          const requiresShippingForError = getValues("requiresShipping");
          if (requiresShippingForError) {
            const freeShippingForError = getValues("freeShipping");
            if (freeShippingForError) {
              toast.error(
                "Please complete all required shipping information: Shipping Class and Estimated Shipping Time are required."
              );
            } else {
              toast.error(
                "Please complete all required shipping information: Shipping Class, Shipping Cost, and Estimated Shipping Time are required."
              );
            }
          }
          break;
        case "availability":
          toast.error(
            "Please complete all required availability: Product Status is required"
          );
          break;
        default:
          toast.error("Please fill in all required fields correctly");
          break;
      }
    }
  };

  /**
   * Handle next button click
   */
  const handleNext = async () => {
    console.log("🔍 handleNext called");
    console.log("🔍 Current section:", currentSection);
    const currentIndex = SECTIONS_ORDER.indexOf(currentSection);
    console.log("🔍 Current index:", currentIndex);
    console.log("🔍 Total sections:", SECTIONS_ORDER.length);
    console.log(
      "🔍 Is last section check:",
      currentIndex >= SECTIONS_ORDER.length - 1
    );

    if (currentIndex < SECTIONS_ORDER.length - 1) {
      const nextSection = SECTIONS_ORDER[currentIndex + 1];
      console.log("🔍 Next section:", nextSection);
      console.log("🔍 Calling validateAndNavigate...");
      validateAndNavigate(nextSection);
    } else {
      console.log("🔍 Already on last section, cannot navigate further");
    }
  };

  /**
   * Handle previous button click
   */
  const handlePrevious = () => {
    const currentIndex = SECTIONS_ORDER.indexOf(currentSection);
    if (currentIndex > 0) {
      setCurrentSection(SECTIONS_ORDER[currentIndex - 1]);
    }
  };

  /**
   * Handle section selection from navigator
   */
  const handleSectionChange = (sectionId: SectionId) => {
    // Only allow navigation to completed sections or the next section
    if (
      completedSections.includes(sectionId) ||
      sectionId === currentSection ||
      SECTIONS_ORDER.indexOf(sectionId) === completedSections.length
    ) {
      setCurrentSection(sectionId);
    } else {
      toast.info("Please complete the current section first");
    }
  };

  /**
   * Validate all sections before submission
   */
  const validateAllSections = async (): Promise<boolean> => {
    console.log("🔍 Validating all sections...");

    // Trigger validation for all fields
    const isFormValid = await trigger();

    if (!isFormValid) {
      console.error("❌ Form validation failed");
      const errorFields = Object.keys(errors);
      console.error("Fields with errors:", errorFields);
      toast.error(`Please fix errors in: ${errorFields.join(", ")}`);
      return false;
    }

    return true;
  };

  /**
   * Handle form submission
   */
  const onSubmit: SubmitHandler<ProductFormData> = async (data) => {
    console.log("🚀 Form submission started...");
    console.log("📍 Current section:", currentSection);
    console.log("✅ Completed sections:", completedSections);
    console.log("📊 Progress:", progress + "%");

    // Debug: Check what watch() returns for price
    const watchedPrice = watch("price");
    console.log(
      "👀 Watched price value:",
      watchedPrice,
      "Type:",
      typeof watchedPrice
    );

    console.log("Form data received:", data);
    console.log("Tags:", tags);
    console.log("Images:", images);
    console.log("Creating state:", creating);
    console.log("Form errors:", errors);
    console.log("Form is valid:", isValid);

    try {
      // FIRST: Fix missing price field issue before validation
      if (!data.price) {
        const watchedPrice = watch("price");
        console.log(
          "🔧 Fixing missing price field. Watched value:",
          watchedPrice
        );

        if (watchedPrice !== undefined && !isNaN(watchedPrice)) {
          console.log("✅ Applied price fallback:", watchedPrice);
          data.price = watchedPrice;
        } else {
          console.error(
            "❌ Cannot fix price field - watched value is invalid:",
            watchedPrice
          );
        }
      }

      // Validate all sections before submission
      const isAllValid = await validateAllSections();
      if (!isAllValid) {
        return null;
      }

      // Validate that we have minimum required data
      if (!data.name || !data.price) {
        toast.error("Product name and price are required");
        console.error("❌ Missing required fields:", {
          name: data.name,
          price: data.price,
        });
        return null;
      }

      // Validate required fields based on current form state
      if (!data.brand) {
        toast.error("Brand is required");
        return null;
      }

      if (!data.description || data.description.length < 10) {
        toast.error("Description must be at least 10 characters long");
        return null;
      }

      if (!data.category) {
        toast.error("Category is required");
        return null;
      }

      if (!data.material) {
        toast.error("Material is required");
        return null;
      }

      if (!data.tags || data.tags.length === 0) {
        toast.error("At least one product tag is required");
        return null;
      }

      if (!data.color) {
        toast.error("Color is required");
        return null;
      }

      if (!data.mainImage && images.length === 0) {
        toast.error("At least one product image is required");
        return null;
      }

      // Validate that all images have alt text
      if (images.length > 0) {
        const missingAltTexts = images.some(
          (_, index) =>
            !imageAltTexts[index] || imageAltTexts[index].trim() === ""
        );
        if (missingAltTexts) {
          toast.error(
            "All images must have alt text for accessibility and SEO"
          );
          return null;
        }
      }

      // Clean up form data - convert empty strings to undefined for optional fields
      const cleanedData = { ...data };

      // Debug: Log the original data to see what we're working with
      console.log("🔍 Original form data:", data);
      console.log("🔍 All form data keys:", Object.keys(data));
      console.log("🔍 Price field exists in data:", "price" in data);
      console.log(
        "🔍 Price field type:",
        typeof data.price,
        "Value:",
        data.price
      );
      console.log("🔍 Name field:", data.name);

      // Check if price is undefined, null, or NaN
      if (data.price === undefined) {
        console.error("❌ Price is undefined in form data!");

        // Try to get price from watch() as fallback
        const watchedPrice = watch("price");
        console.log("🔄 Trying fallback - watched price:", watchedPrice);

        if (watchedPrice !== undefined && !isNaN(watchedPrice)) {
          console.log("✅ Using watched price as fallback");
          cleanedData.price = watchedPrice;
        } else {
          console.error("❌ Watched price is also invalid:", watchedPrice);
        }
      } else if (data.price === null) {
        console.error("❌ Price is null!");
      } else if (isNaN(data.price)) {
        console.error("❌ Price is NaN!");
      } else {
        console.log("✅ Price is valid:", data.price);
      }

      // Remove empty SKU to avoid duplicate key issues
      if (
        cleanedData.sku === "" ||
        cleanedData.sku === null ||
        cleanedData.sku === undefined
      ) {
        delete cleanedData.sku;
      }

      // Handle required number fields (convert strings to numbers, but don't delete if invalid)
      const requiredNumberFields = ["price", "stock"];
      requiredNumberFields.forEach((field) => {
        if (
          cleanedData[field] !== undefined &&
          typeof cleanedData[field] === "string"
        ) {
          const numValue = parseFloat(cleanedData[field]);
          if (!isNaN(numValue)) {
            cleanedData[field] = numValue;
          }
          // Don't delete required fields even if conversion fails
        }
      });

      // Convert empty strings to undefined for optional number fields
      const optionalNumberFields = [
        "lowStockThreshold",
        "yearMade",
        "shippingCost",
        "costPrice",
        "originalPrice",
        "minimumOrderQuantity",
        "maximumOrderQuantity",
        "salePrice",
      ];
      optionalNumberFields.forEach((field) => {
        if (cleanedData[field] === "" || cleanedData[field] === null) {
          delete cleanedData[field];
        } else if (
          cleanedData[field] !== undefined &&
          typeof cleanedData[field] === "string"
        ) {
          // Convert string numbers to actual numbers
          const numValue = parseFloat(cleanedData[field]);
          if (!isNaN(numValue)) {
            cleanedData[field] = numValue;
          } else {
            delete cleanedData[field];
          }
        }
      });

      // Convert empty strings to undefined for optional URL fields
      const urlFields = ["videoUrl", "threeDModelUrl", "canonicalUrl"];
      urlFields.forEach((field) => {
        if (cleanedData[field] === "" || cleanedData[field] === null) {
          delete cleanedData[field];
        }
      });

      // Convert empty strings to undefined for optional string fields
      const optionalStringFields = [
        "model",
        "shortDescription",
        "barcode",
        "subcategory",
        "color",
        "size",
        "location",
        "shippingTime",
        "taxClass",
        "metaTitle",
        "metaDescription",
        "focusKeyword",
        "slug",
      ];
      optionalStringFields.forEach((field) => {
        if (cleanedData[field] === "" || cleanedData[field] === null) {
          delete cleanedData[field];
        }
      });

      // Convert empty strings to undefined for optional date fields
      const dateFields = ["saleEndsAt", "availableFrom", "availableUntil"];
      dateFields.forEach((field) => {
        if (cleanedData[field] === "" || cleanedData[field] === null) {
          delete cleanedData[field];
        }
      });

      // Debug: Log cleaned data before final preparation
      console.log("🧹 Cleaned data:", cleanedData);
      console.log(
        "🧹 Cleaned price field type:",
        typeof cleanedData.price,
        "Value:",
        cleanedData.price
      );

      // Prepare the final data with images and tags
      const mainImageUrl =
        images.length > 0 ? images[0].url : data.mainImage || "";
      const additionalImages =
        images.length > 1 ? images.slice(1).map((img) => img.url) : [];

      const finalProductData = {
        ...cleanedData,
        tags: tags || [],
        images: additionalImages, // Only additional images, not including main image
        mainImage: mainImageUrl,
        imageAltTexts: imageAltTexts || [],
      };

      console.log("📦 Final product data to be sent:", finalProductData);
      console.log(
        "📦 Final price field type:",
        typeof finalProductData.price,
        "Value:",
        finalProductData.price
      );

      // Show loading toast
      toast.loading("Creating product...", { id: "create-product" });

      // Create product via API
      console.log("📡 Calling API...");
      const createdProduct = await createProduct(finalProductData);

      // Dismiss loading toast
      toast.dismiss("create-product");

      // Display the created product in the console
      console.log("✅ PRODUCT CREATED SUCCESSFULLY!");
      console.log("Product Details:", createdProduct);

      // Show success message
      toast.success("Product created successfully!");

      // Navigate to the created product's detail page
      if (createdProduct && (createdProduct._id || createdProduct.id)) {
        const productId = createdProduct._id || createdProduct.id;
        console.log(`🔗 Navigating to product: ${productId}`);
        router.push(`/admin/products/${productId}`);
      } else {
        // Fallback to products list
        console.log("🔗 Navigating to products list");
        router.push("/admin/products/list");
      }

      return createdProduct;
    } catch (err) {
      // Dismiss loading toast
      toast.dismiss("create-product");

      console.error("❌ Failed to create product:", err);

      // Show user-friendly error messages
      if (err instanceof Error) {
        let errorMessage = err.message;

        // Handle specific error types
        if (errorMessage.includes("Duplicate key error")) {
          errorMessage =
            "A product with this SKU already exists. Please use a different SKU or leave it empty.";
        } else if (errorMessage.includes("ValidationError")) {
          errorMessage = "Please check all required fields and try again.";
        } else if (errorMessage.includes("HTTP error! status: 400")) {
          // Extract the actual error message from the API response
          const match = errorMessage.match(/message: (.+)/);
          if (match) {
            const apiError = match[1];
            if (apiError.includes("Duplicate key error")) {
              errorMessage =
                "A product with this SKU already exists. Please use a different SKU or leave it empty.";
            } else {
              errorMessage = `Error: ${apiError}`;
            }
          }
        }

        toast.error(errorMessage);
        console.error("Error details:", err);
      } else {
        toast.error("Failed to create product: Unknown error");
        console.error("Unknown error:", err);
      }

      return null;
    }
  };

  return {
    // Form state
    register,
    handleSubmit,
    setValue,
    watch,
    errors,
    isValid,

    // Section navigation
    currentSection,
    completedSections,
    SECTIONS_ORDER,
    progress,
    handleNext,
    handlePrevious,
    handleSectionChange,

    // Additional state
    tags,
    setTags,
    images,
    setImages,
    imageAltTexts,
    setImageAltTexts,

    // Form submission
    onSubmit,
    creating,
    validateAllSections,
  };
}
