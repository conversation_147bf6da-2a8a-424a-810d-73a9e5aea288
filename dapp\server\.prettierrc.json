{"semi": true, "singleQuote": false, "trailingComma": "es5", "tabWidth": 2, "printWidth": 80, "bracketSpacing": true, "arrowParens": "always", "endOfLine": "lf", "jsxSingleQuote": false, "quoteProps": "as-needed", "htmlWhitespaceSensitivity": "ignore", "proseWrap": "preserve", "plugins": ["@trivago/prettier-plugin-sort-imports"], "importOrder": ["^node_modules", "^@/(.*)$", "^src/(.*)$", "^[./]"], "importOrderSeparation": true, "importOrderSortSpecifiers": true}