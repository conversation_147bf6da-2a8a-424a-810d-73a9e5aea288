import { Document } from "mongoose";

/**
 * Category interface matching the frontend Category type
 */
export interface ICategory {
  name: string;
  description: string;
  slug: string;
  icon?: string;
  color?: string;
  isActive: boolean;
  productCount: number;
  parentId?: string;
  sortOrder: number;
}

/**
 * Category document interface for MongoDB
 */
export interface CategoryDocument extends ICategory, Document {
  _id: string;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * DTO for creating a new category
 */
export interface CreateCategoryDto {
  name: string;
  description?: string;
  icon?: string;
  color?: string;
  isActive?: boolean;
  parentId?: string;
  sortOrder?: number;
}

/**
 * DTO for updating a category
 */
export interface UpdateCategoryDto {
  name?: string;
  description?: string;
  slug?: string;
  icon?: string;
  color?: string;
  isActive?: boolean;
  parentId?: string;
  sortOrder?: number;
}

/**
 * Category filters for querying
 */
export interface CategoryFilters {
  isActive?: boolean;
  parentId?: string;
  search?: string;
}
