"use client";

import { motion } from "framer-motion";
import Image from "next/image";

const services = [
  {
    title: "Profesionali Nekilnojamojo Turto Fotografija",
    description:
      "Aukš<PERSON> kokyb<PERSON>s nekilnojamojo turto n<PERSON>, kurios padeda pritraukti potencialius pirkėjus ir nuomininkus.",
    image: "/images/real-estate-photography.jpg",
  },
  {
    title: "Vaizdo Turas ir Dronų Filmavimas",
    description:
      "Siūlome dronų vaizdo įrašus ir virtualius turus, suteikiančius išskirtinį vaizdą į parduodamus objektus.",
    image: "/images/drone-footage.jpg",
  },
  {
    title: "Sklypų ir Pastatų Vizualizacijos",
    description:
      "3D vizualizacijos ir architektūriniai projektai, padedantys potencialiems klientams pamatyti galutinį rezultatą.",
    image: "/images/land-plots-visualization.jpg",
  },
];

const ServicesSection = () => {
  return (
    <section className="flex h-screen flex-col items-center justify-center bg-gray-900 px-6 text-white">
      <h2 className="mb-8 text-4xl font-bold">Mūsų Paslaugos</h2>
      <div className="grid max-w-6xl gap-8 md:grid-cols-3">
        {services.map((service, index) => (
          <motion.div
            key={index}
            className="overflow-hidden rounded-2xl bg-gray-800 p-6 shadow-lg"
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: index * 0.2 }}
            viewport={{ once: true }}
          >
            <div className="relative mb-4 h-48 w-full">
              <Image
                src={service.image}
                alt={service.title}
                layout="fill"
                objectFit="cover"
                className="rounded-lg"
              />
            </div>
            <h3 className="mb-2 text-xl font-semibold">{service.title}</h3>
            <p className="text-gray-300">{service.description}</p>
          </motion.div>
        ))}
      </div>
    </section>
  );
};

export default ServicesSection;
