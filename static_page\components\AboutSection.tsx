"use client";

import { motion } from "framer-motion";
import Image from "next/image";

const AboutSection = () => {
  return (
    <section className="flex h-screen items-center justify-center bg-gray-900 px-6 text-white">
      <div className="grid max-w-6xl grid-cols-1 gap-12 md:grid-cols-2">
        {/* Text Content */}
        <motion.div
          initial={{ opacity: 0, x: -50 }}
          whileInView={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.7 }}
          viewport={{ once: true }}
          className="flex flex-col justify-center"
        >
          <h2 className="mb-6 text-4xl font-bold">Apie Mus</h2>
          <p className="mb-4 text-gray-300">
            Mes esame inovatyvi komanda, kuri specializuojasi nekilnojamojo
            turto ir architektūros vizualizacijoje. Teikiame aukštos kokybės
            fotografiją, vaizdo įrašus ir 3D vizualizacijas, kad jūsų projektai
            atrodytų nepriekaištingai.
          </p>
          <p className="text-gray-300">
            Naudojame moderniausias technologijas ir kūrybinius sprendimus,
            siekdami padėti klientams išsiskirti rinkoje ir pritraukti daugiau
            potencialių pirkėjų bei investuotojų.
          </p>
        </motion.div>

        {/* Image Section */}
        <motion.div
          initial={{ opacity: 0, x: 80 }}
          whileInView={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.7 }}
          viewport={{ once: true }}
          className="relative h-80 md:h-auto"
        >
          <div className="relative size-full">
            <Image
              src="/images/team-working.jpg"
              alt="Komanda dirba"
              layout="fill"
              objectFit="cover"
              className="rounded-xl shadow-lg"
            />
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default AboutSection;
