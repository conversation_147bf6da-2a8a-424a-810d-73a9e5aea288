"use client";

import React, { useState } from "react";

import { DollarSign, TrendingDown, TrendingUp } from "lucide-react";

import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Product } from "@/types/product";

type ProductSectionProps = {
  product: Product;
  isEditing: boolean;
  onProductUpdate: (product: Product) => void;
};

export const ProductPricingSection = ({
  product,
  isEditing,
  onProductUpdate,
}: ProductSectionProps) => {
  const [editedProduct, setEditedProduct] = useState(product);

  const handleInputChange = (field: string, value: string | boolean) => {
    setEditedProduct((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  // Handle both string and number price formats
  const currentPrice =
    typeof product.price === "string"
      ? parseFloat(product.price.replace("$", ""))
      : parseFloat(product.price);

  const originalPrice = product.originalPrice
    ? typeof product.originalPrice === "string"
      ? parseFloat(product.originalPrice.replace("$", ""))
      : parseFloat(product.originalPrice)
    : null;

  const discount = originalPrice
    ? Math.round(((originalPrice - currentPrice) / originalPrice) * 100)
    : 0;

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <DollarSign className="h-5 w-5" />
          Pricing
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Current Price */}
        <div>
          <Label htmlFor="current-price">Current Price</Label>
          {isEditing ? (
            <Input
              id="current-price"
              value={editedProduct.price}
              onChange={(e) => handleInputChange("price", e.target.value)}
              className="mt-1"
              placeholder="$0.00"
            />
          ) : (
            <div className="mt-1 flex items-center gap-2">
              <span className="text-2xl font-bold text-green-600">
                {product.currency || "EUR"} {currentPrice.toFixed(2)}
              </span>
              {product.isOnSale && (
                <Badge className="bg-red-500 text-white">-{discount}%</Badge>
              )}
            </div>
          )}
        </div>

        {/* Original Price */}
        <div>
          <Label htmlFor="original-price">Original Price</Label>
          {isEditing ? (
            <Input
              id="original-price"
              value={editedProduct.originalPrice || ""}
              onChange={(e) =>
                handleInputChange("originalPrice", e.target.value)
              }
              className="mt-1"
              placeholder="$0.00 (optional)"
            />
          ) : (
            <div className="mt-1">
              {product.originalPrice ? (
                <span className="text-gray-500 line-through">
                  {product.currency || "EUR"} {originalPrice?.toFixed(2)}
                </span>
              ) : (
                <span className="text-gray-400">No original price set</span>
              )}
            </div>
          )}
        </div>

        {/* Sale Toggle */}
        {isEditing && (
          <div className="flex items-center justify-between">
            <Label htmlFor="on-sale">On Sale</Label>
            <Switch
              id="on-sale"
              checked={editedProduct.isOnSale || false}
              onCheckedChange={(checked) =>
                handleInputChange("isOnSale", checked)
              }
            />
          </div>
        )}

        {/* Price Analysis */}
        {!isEditing && (
          <div className="space-y-2 rounded-md bg-gray-50 p-3">
            <h4 className="text-sm font-medium">Price Analysis</h4>
            <div className="space-y-1 text-sm">
              {product.isOnSale && originalPrice ? (
                <div className="flex items-center gap-1 text-green-600">
                  <TrendingDown className="h-3 w-3" />
                  <span>
                    Discounted by {product.currency || "EUR"}{" "}
                    {(originalPrice - currentPrice).toFixed(2)}
                  </span>
                </div>
              ) : (
                <div className="flex items-center gap-1 text-gray-600">
                  <TrendingUp className="h-3 w-3" />
                  <span>Regular pricing</span>
                </div>
              )}
              {product.costPrice && (
                <div className="text-gray-500">
                  Cost: {product.currency || "EUR"}{" "}
                  {product.costPrice.toFixed(2)}
                </div>
              )}
              {product.profitMargin && (
                <div className="text-gray-500">
                  Profit margin: {product.profitMargin.toFixed(1)}%
                </div>
              )}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
