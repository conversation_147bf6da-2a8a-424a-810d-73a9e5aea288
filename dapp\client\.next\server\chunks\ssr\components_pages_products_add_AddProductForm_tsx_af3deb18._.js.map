{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/pages/products/add/AddProductForm.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useEffect, useState } from \"react\";\r\n\r\nimport {\r\n  AlertCircle,\r\n  ArrowLeft,\r\n  CheckCircle,\r\n  Clock,\r\n  Eye,\r\n  Package,\r\n  Save,\r\n  Sparkles,\r\n  Zap,\r\n} from \"lucide-react\";\r\nimport { toast } from \"sonner\";\r\n\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\r\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\r\nimport { Progress } from \"@/components/ui/progress\";\r\nimport { useProductFormWithSections } from \"@/hooks/useProductFormWithSections\";\r\n\r\nimport { FormNavigation } from \"./FormNavigation\";\r\nimport { FormSectionRenderer } from \"./FormSectionRenderer\";\r\nimport { getFormSections } from \"./FormSections\";\r\nimport { MetadataGuide } from \"./MetadataGuide\";\r\nimport { SectionNavigator } from \"./TabSections\";\r\n\r\n/**\r\n * Enhanced multi-step form for adding a new product\r\n * Features improved UI, better visual feedback, and user-friendly design\r\n */\r\nexport const AddProductForm = () => {\r\n  const [showPreview, setShowPreview] = useState(false);\r\n  const [saveStatus, setSaveStatus] = useState<\r\n    \"idle\" | \"saving\" | \"saved\" | \"error\"\r\n  >(\"idle\");\r\n\r\n  const {\r\n    // Form state\r\n    register,\r\n    handleSubmit,\r\n    setValue,\r\n    watch,\r\n    errors,\r\n\r\n    // Section navigation\r\n    currentSection,\r\n    completedSections,\r\n    SECTIONS_ORDER,\r\n    progress,\r\n    handleNext,\r\n    handlePrevious,\r\n    handleSectionChange,\r\n\r\n    // Additional state\r\n    tags,\r\n    setTags,\r\n    images,\r\n    setImages,\r\n    imageAltTexts,\r\n    setImageAltTexts,\r\n\r\n    // Form submission\r\n    onSubmit,\r\n    creating,\r\n    validateAllSections,\r\n  } = useProductFormWithSections();\r\n\r\n  // Get formatted sections for the navigator\r\n  const sections = getFormSections(completedSections);\r\n\r\n  // Check if we're on the last section\r\n  const isLastSection =\r\n    currentSection === SECTIONS_ORDER[SECTIONS_ORDER.length - 1];\r\n\r\n  // Get current section info\r\n  const currentSectionInfo = sections.find((s) => s.id === currentSection);\r\n  const completionPercentage = Math.round(\r\n    (completedSections.length / SECTIONS_ORDER.length) * 100\r\n  );\r\n\r\n  // Update form values when images or tags change\r\n  useEffect(() => {\r\n    if (images.length > 0) {\r\n      setValue(\"mainImage\", images[0].url);\r\n    }\r\n  }, [images, setValue]);\r\n\r\n  useEffect(() => {\r\n    setValue(\"tags\", tags);\r\n  }, [tags, setValue]);\r\n\r\n  // Auto-save functionality\r\n  const handleAutoSave = async () => {\r\n    setSaveStatus(\"saving\");\r\n    // Simulate auto-save\r\n    setTimeout(() => {\r\n      setSaveStatus(\"saved\");\r\n      setTimeout(() => setSaveStatus(\"idle\"), 2000);\r\n    }, 1000);\r\n  };\r\n\r\n  // Get status icon based on completion\r\n  const getStatusIcon = (sectionId: string) => {\r\n    if (completedSections.includes(sectionId as any)) {\r\n      return <CheckCircle className=\"h-4 w-4 text-green-500\" />;\r\n    }\r\n    if (sectionId === currentSection) {\r\n      return <Clock className=\"h-4 w-4 text-blue-500\" />;\r\n    }\r\n    return <AlertCircle className=\"h-4 w-4 text-gray-300\" />;\r\n  };\r\n\r\n  return (\r\n    <div className=\"mx-auto max-w-7xl space-y-8\">\r\n      {/* Enhanced Header with Progress */}\r\n      <div className=\"relative overflow-hidden rounded-xl bg-gradient-to-r from-blue-50 via-indigo-50 to-purple-50 p-8\">\r\n        <div className=\"relative z-10\">\r\n          <div className=\"mb-6 flex items-center justify-between\">\r\n            <div className=\"flex items-center gap-4\">\r\n              <div className=\"rounded-full bg-blue-100 p-3\">\r\n                <Package className=\"h-6 w-6 text-blue-600\" />\r\n              </div>\r\n              <div>\r\n                <h1 className=\"text-2xl font-bold text-gray-900\">\r\n                  Create New Product\r\n                </h1>\r\n                <p className=\"text-gray-600\">\r\n                  {currentSectionInfo?.title} • Step{\" \"}\r\n                  {SECTIONS_ORDER.indexOf(currentSection) + 1} of{\" \"}\r\n                  {SECTIONS_ORDER.length}\r\n                </p>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"flex items-center gap-3\">\r\n              {/* Auto-save status */}\r\n              <div className=\"flex items-center gap-2\">\r\n                {saveStatus === \"saving\" && (\r\n                  <div className=\"flex items-center gap-2 text-sm text-gray-600\">\r\n                    <div className=\"h-4 w-4 animate-spin rounded-full border-2 border-blue-600 border-t-transparent\" />\r\n                    Saving...\r\n                  </div>\r\n                )}\r\n                {saveStatus === \"saved\" && (\r\n                  <div className=\"flex items-center gap-2 text-sm text-green-600\">\r\n                    <CheckCircle className=\"h-4 w-4\" />\r\n                    Saved\r\n                  </div>\r\n                )}\r\n              </div>\r\n\r\n              <Button\r\n                variant=\"outline\"\r\n                size=\"sm\"\r\n                onClick={() => setShowPreview(!showPreview)}\r\n              >\r\n                <Eye className=\"mr-2 h-4 w-4\" />\r\n                {showPreview ? \"Hide Preview\" : \"Preview\"}\r\n              </Button>\r\n\r\n              <Button variant=\"outline\" size=\"sm\" onClick={handleAutoSave}>\r\n                <Save className=\"mr-2 h-4 w-4\" />\r\n                Save Draft\r\n              </Button>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Enhanced Progress Bar */}\r\n          <div className=\"space-y-3\">\r\n            <div className=\"flex items-center justify-between\">\r\n              <span className=\"text-sm font-medium text-gray-700\">\r\n                Progress: {completionPercentage}% Complete\r\n              </span>\r\n              <Badge variant=\"secondary\" className=\"bg-blue-100 text-blue-800\">\r\n                {completedSections.length}/{SECTIONS_ORDER.length} sections\r\n              </Badge>\r\n            </div>\r\n\r\n            <div className=\"relative\">\r\n              <Progress value={progress} className=\"h-3 bg-gray-200\" />\r\n              <div className=\"absolute inset-0 flex items-center justify-center\">\r\n                <span className=\"text-xs font-medium text-white drop-shadow\">\r\n                  {completionPercentage}%\r\n                </span>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Section indicators */}\r\n            <div className=\"flex justify-between\">\r\n              {sections.map((section, index) => (\r\n                <div\r\n                  key={section.id}\r\n                  className=\"flex flex-col items-center gap-1\"\r\n                >\r\n                  {getStatusIcon(section.id)}\r\n                  <span className=\"max-w-16 truncate text-center text-xs text-gray-500\">\r\n                    {section.title}\r\n                  </span>\r\n                </div>\r\n              ))}\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Background decoration */}\r\n        <div className=\"absolute right-0 top-0 -mr-4 -mt-4 h-24 w-24 rounded-full bg-blue-200 opacity-20\" />\r\n        <div className=\"absolute bottom-0 left-0 -mb-4 -ml-4 h-16 w-16 rounded-full bg-purple-200 opacity-20\" />\r\n      </div>\r\n\r\n      <div className=\"grid grid-cols-1 gap-8 lg:grid-cols-4\">\r\n        {/* Enhanced Section Navigator */}\r\n        <div className=\"lg:col-span-1\">\r\n          <Card className=\"sticky top-4 overflow-hidden\">\r\n            <CardHeader className=\"bg-gradient-to-r from-gray-50 to-gray-100 pb-4\">\r\n              <CardTitle className=\"flex items-center gap-2 text-lg\">\r\n                <Sparkles className=\"h-5 w-5 text-blue-600\" />\r\n                Form Sections\r\n              </CardTitle>\r\n            </CardHeader>\r\n            <CardContent className=\"p-0\">\r\n              <div className=\"space-y-1 p-4\">\r\n                {sections.map((section, index) => (\r\n                  <button\r\n                    key={section.id}\r\n                    onClick={() => handleSectionChange(section.id as any)}\r\n                    className={`flex w-full items-center gap-3 rounded-lg p-3 text-left transition-all duration-200 ${\r\n                      currentSection === section.id\r\n                        ? \"border-2 border-blue-200 bg-blue-50 text-blue-900 shadow-sm\"\r\n                        : section.isCompleted\r\n                          ? \"border border-green-200 bg-green-50 text-green-900 hover:bg-green-100\"\r\n                          : \"border border-gray-200 bg-gray-50 text-gray-600 hover:bg-gray-100\"\r\n                    }`}\r\n                  >\r\n                    <div className=\"flex-shrink-0\">\r\n                      {getStatusIcon(section.id)}\r\n                    </div>\r\n\r\n                    <div className=\"min-w-0 flex-1\">\r\n                      <div className=\"flex items-center gap-2\">\r\n                        <span className=\"truncate text-sm font-medium\">\r\n                          {section.title}\r\n                        </span>\r\n                        {section.isOptional && (\r\n                          <Badge variant=\"outline\" className=\"text-xs\">\r\n                            Optional\r\n                          </Badge>\r\n                        )}\r\n                      </div>\r\n                      <div className=\"mt-1 text-xs text-gray-500\">\r\n                        Step {index + 1} of {sections.length}\r\n                      </div>\r\n                    </div>\r\n\r\n                    {currentSection === section.id && (\r\n                      <Zap className=\"h-4 w-4 animate-pulse text-blue-500\" />\r\n                    )}\r\n                  </button>\r\n                ))}\r\n              </div>\r\n            </CardContent>\r\n          </Card>\r\n        </div>\r\n\r\n        {/* Enhanced Form Content */}\r\n        <div className=\"space-y-6 lg:col-span-3\">\r\n          {/* Current Section Header */}\r\n          <Card className=\"border-l-4 border-l-blue-500\">\r\n            <CardHeader className=\"pb-3\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <div className=\"flex items-center gap-3\">\r\n                  {currentSectionInfo?.icon}\r\n                  <div>\r\n                    <CardTitle className=\"text-xl\">\r\n                      {currentSectionInfo?.title}\r\n                    </CardTitle>\r\n                    <p className=\"mt-1 text-sm text-gray-600\">\r\n                      Complete this section to continue to the next step\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"flex items-center gap-2\">\r\n                  {completedSections.includes(currentSection as any) && (\r\n                    <Badge className=\"bg-green-100 text-green-800\">\r\n                      <CheckCircle className=\"mr-1 h-3 w-3\" />\r\n                      Completed\r\n                    </Badge>\r\n                  )}\r\n                  <Badge variant=\"outline\">\r\n                    Step {SECTIONS_ORDER.indexOf(currentSection) + 1}\r\n                  </Badge>\r\n                </div>\r\n              </div>\r\n            </CardHeader>\r\n          </Card>\r\n\r\n          {/* Form Content Card */}\r\n          <Card className=\"overflow-hidden shadow-lg\">\r\n            <CardContent className=\"p-8\">\r\n              <form\r\n                onSubmit={handleSubmit(onSubmit, (errors) => {\r\n                  console.error(\"Form validation errors:\", errors);\r\n                  toast.error(\"Please fix the form errors before submitting\");\r\n                })}\r\n                className=\"space-y-8\"\r\n              >\r\n                {/* Render the current form section */}\r\n                <div className=\"min-h-[400px]\">\r\n                  <FormSectionRenderer\r\n                    currentSection={currentSection}\r\n                    register={register}\r\n                    errors={errors}\r\n                    setValue={setValue}\r\n                    watch={watch}\r\n                    tags={tags}\r\n                    setTags={setTags}\r\n                    images={images}\r\n                    setImages={setImages}\r\n                    imageAltTexts={imageAltTexts}\r\n                    setImageAltTexts={setImageAltTexts}\r\n                  />\r\n                </div>\r\n\r\n                {/* Validation Error Notices for All Sections */}\r\n\r\n                {/* Basic Info Section Validation */}\r\n                {currentSection === \"basic-info\" &&\r\n                  (errors.name ||\r\n                    errors.brand ||\r\n                    errors.description ||\r\n                    errors.productType) && (\r\n                    <div className=\"rounded-lg border border-red-200 bg-red-50 p-4\">\r\n                      <div className=\"flex items-start gap-3\">\r\n                        <AlertCircle className=\"mt-0.5 h-5 w-5 text-red-600\" />\r\n                        <div>\r\n                          <h3 className=\"font-semibold text-red-900\">\r\n                            Cannot Proceed - Missing Required Information\r\n                          </h3>\r\n                          <p className=\"mt-1 text-sm text-red-800\">\r\n                            Please complete the following required fields before\r\n                            continuing:\r\n                          </p>\r\n                          <ul className=\"mt-2 list-inside list-disc text-sm text-red-700\">\r\n                            {errors.name && <li>Product Name</li>}\r\n                            {errors.brand && <li>Brand</li>}\r\n                            {errors.description && <li>Product Description</li>}\r\n                            {errors.productType && <li>Product Type</li>}\r\n                          </ul>\r\n                          <div className=\"mt-2 text-xs text-red-600\">\r\n                            💡 These fields are essential for product\r\n                            identification and categorization.\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  )}\r\n\r\n                {/* Pricing Section Validation */}\r\n                {currentSection === \"pricing\" &&\r\n                  (errors.price ||\r\n                    errors.currency ||\r\n                    errors.taxStatus ||\r\n                    errors.costPrice) && (\r\n                    <div className=\"rounded-lg border border-red-200 bg-red-50 p-4\">\r\n                      <div className=\"flex items-start gap-3\">\r\n                        <AlertCircle className=\"mt-0.5 h-5 w-5 text-red-600\" />\r\n                        <div>\r\n                          <h3 className=\"font-semibold text-red-900\">\r\n                            Cannot Proceed - Missing Required Information\r\n                          </h3>\r\n                          <p className=\"mt-1 text-sm text-red-800\">\r\n                            Please complete the following required fields before\r\n                            continuing:\r\n                          </p>\r\n                          <ul className=\"mt-2 list-inside list-disc text-sm text-red-700\">\r\n                            {errors.price && <li>Regular Price</li>}\r\n                            {errors.currency && <li>Currency</li>}\r\n                            {errors.taxStatus && <li>Tax Status</li>}\r\n                            {errors.costPrice && (\r\n                              <li>Cost Price (Business Intelligence)</li>\r\n                            )}\r\n                          </ul>\r\n                          <div className=\"mt-2 text-xs text-red-600\">\r\n                            💡 These fields are essential for business analytics\r\n                            and profit tracking.\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  )}\r\n\r\n                {/* Inventory Section Validation */}\r\n                {currentSection === \"inventory\" &&\r\n                  (errors.stock ||\r\n                    errors.condition ||\r\n                    errors.stockManagement) && (\r\n                    <div className=\"rounded-lg border border-red-200 bg-red-50 p-4\">\r\n                      <div className=\"flex items-start gap-3\">\r\n                        <AlertCircle className=\"mt-0.5 h-5 w-5 text-red-600\" />\r\n                        <div>\r\n                          <h3 className=\"font-semibold text-red-900\">\r\n                            Cannot Proceed - Missing Required Information\r\n                          </h3>\r\n                          <p className=\"mt-1 text-sm text-red-800\">\r\n                            Please complete the following required fields before\r\n                            continuing:\r\n                          </p>\r\n                          <ul className=\"mt-2 list-inside list-disc text-sm text-red-700\">\r\n                            {errors.stock && <li>Stock Quantity</li>}\r\n                            {errors.condition && <li>Product Condition</li>}\r\n                            {errors.stockManagement && (\r\n                              <li>Stock Management</li>\r\n                            )}\r\n                          </ul>\r\n                          <div className=\"mt-2 text-xs text-red-600\">\r\n                            💡 These fields are essential for inventory tracking\r\n                            and order fulfillment.\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  )}\r\n\r\n                {/* Details Section Validation */}\r\n                {currentSection === \"details\" &&\r\n                  (errors.category ||\r\n                    errors.material ||\r\n                    errors.tags ||\r\n                    errors.color) && (\r\n                    <div className=\"rounded-lg border border-red-200 bg-red-50 p-4\">\r\n                      <div className=\"flex items-start gap-3\">\r\n                        <AlertCircle className=\"mt-0.5 h-5 w-5 text-red-600\" />\r\n                        <div>\r\n                          <h3 className=\"font-semibold text-red-900\">\r\n                            Cannot Proceed - Missing Required Information\r\n                          </h3>\r\n                          <p className=\"mt-1 text-sm text-red-800\">\r\n                            Please complete the following required fields before\r\n                            continuing:\r\n                          </p>\r\n                          <ul className=\"mt-2 list-inside list-disc text-sm text-red-700\">\r\n                            {errors.category && <li>Product Category</li>}\r\n                            {errors.material && <li>Product Material</li>}\r\n                            {errors.tags && (\r\n                              <li>Product Tags (at least one)</li>\r\n                            )}\r\n                            {errors.color && <li>Product Color</li>}\r\n                          </ul>\r\n                          <div className=\"mt-2 text-xs text-red-600\">\r\n                            💡 Category helps organize products for customers,\r\n                            Material information is essential for business\r\n                            intelligence, Tags improve search visibility, and\r\n                            Color enhances product presentation and customer\r\n                            choice.\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  )}\r\n\r\n                {/* Media Section Validation */}\r\n                {currentSection === \"media\" &&\r\n                  (errors.mainImage ||\r\n                    (images.length > 0 &&\r\n                      images.some(\r\n                        (_, index) =>\r\n                          !imageAltTexts[index] ||\r\n                          imageAltTexts[index].trim() === \"\"\r\n                      ))) && (\r\n                    <div className=\"rounded-lg border border-red-200 bg-red-50 p-4\">\r\n                      <div className=\"flex items-start gap-3\">\r\n                        <AlertCircle className=\"mt-0.5 h-5 w-5 text-red-600\" />\r\n                        <div>\r\n                          <h3 className=\"font-semibold text-red-900\">\r\n                            Cannot Proceed - Missing Required Information\r\n                          </h3>\r\n                          <p className=\"mt-1 text-sm text-red-800\">\r\n                            Please complete the following required fields before\r\n                            continuing:\r\n                          </p>\r\n                          <ul className=\"mt-2 list-inside list-disc text-sm text-red-700\">\r\n                            {errors.mainImage && <li>Main Product Image</li>}\r\n                            {images.length > 0 &&\r\n                              images.some(\r\n                                (_, index) =>\r\n                                  !imageAltTexts[index] ||\r\n                                  imageAltTexts[index].trim() === \"\"\r\n                              ) && (\r\n                                <li>\r\n                                  Alt text for all images (required for\r\n                                  accessibility and SEO)\r\n                                </li>\r\n                              )}\r\n                          </ul>\r\n                          <div className=\"mt-2 text-xs text-red-600\">\r\n                            💡 Product images and alt text are essential for\r\n                            customer engagement, accessibility, and SEO\r\n                            optimization.\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  )}\r\n\r\n                {/* Shipping Section Validation */}\r\n                {currentSection === \"shipping\" &&\r\n                  (errors.shippingClass ||\r\n                    errors.shippingCost ||\r\n                    errors.shippingTime) && (\r\n                    <div className=\"rounded-lg border border-red-200 bg-red-50 p-4\">\r\n                      <div className=\"flex items-start gap-3\">\r\n                        <AlertCircle className=\"mt-0.5 h-5 w-5 text-red-600\" />\r\n                        <div>\r\n                          <h3 className=\"font-semibold text-red-900\">\r\n                            Cannot Proceed - Missing Required Information\r\n                          </h3>\r\n                          <p className=\"mt-1 text-sm text-red-800\">\r\n                            Please complete the following required fields before\r\n                            continuing:\r\n                          </p>\r\n                          <ul className=\"mt-2 list-inside list-disc text-sm text-red-700\">\r\n                            {errors.shippingClass && <li>Shipping Class</li>}\r\n                            {errors.shippingCost && <li>Shipping Cost</li>}\r\n                            {errors.shippingTime && (\r\n                              <li>Estimated Shipping Time</li>\r\n                            )}\r\n                          </ul>\r\n                          <div className=\"mt-2 text-xs text-red-600\">\r\n                            💡 Complete shipping information helps customers\r\n                            make informed purchasing decisions and sets proper\r\n                            delivery expectations.\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  )}\r\n\r\n                {/* Availability Section Validation */}\r\n                {currentSection === \"availability\" && errors.status && (\r\n                  <div className=\"rounded-lg border border-red-200 bg-red-50 p-4\">\r\n                    <div className=\"flex items-start gap-3\">\r\n                      <AlertCircle className=\"mt-0.5 h-5 w-5 text-red-600\" />\r\n                      <div>\r\n                        <h3 className=\"font-semibold text-red-900\">\r\n                          Cannot Proceed - Missing Required Information\r\n                        </h3>\r\n                        <p className=\"mt-1 text-sm text-red-800\">\r\n                          Please complete the following required fields before\r\n                          continuing:\r\n                        </p>\r\n                        <ul className=\"mt-2 list-inside list-disc text-sm text-red-700\">\r\n                          {errors.status && <li>Product Status</li>}\r\n                        </ul>\r\n                        <div className=\"mt-2 text-xs text-red-600\">\r\n                          💡 Product status determines visibility and\r\n                          availability to customers.\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                )}\r\n\r\n                {/* Enhanced Navigation Buttons */}\r\n                <div className=\"flex items-center justify-between border-t border-gray-200 pt-6\">\r\n                  <div className=\"flex items-center gap-3\">\r\n                    {SECTIONS_ORDER.indexOf(currentSection) > 0 && (\r\n                      <Button\r\n                        type=\"button\"\r\n                        variant=\"outline\"\r\n                        onClick={handlePrevious}\r\n                        className=\"flex items-center gap-2\"\r\n                      >\r\n                        <ArrowLeft className=\"h-4 w-4\" />\r\n                        Previous\r\n                      </Button>\r\n                    )}\r\n                  </div>\r\n\r\n                  <div className=\"flex items-center gap-3\">\r\n                    <Button\r\n                      type=\"button\"\r\n                      variant=\"ghost\"\r\n                      onClick={handleAutoSave}\r\n                      className=\"text-gray-600\"\r\n                    >\r\n                      <Save className=\"mr-2 h-4 w-4\" />\r\n                      Save Draft\r\n                    </Button>\r\n\r\n                    {isLastSection ? (\r\n                      <Button\r\n                        type=\"submit\"\r\n                        disabled={creating}\r\n                        onClick={async (e) => {\r\n                          console.log(\r\n                            \"🔍 Submit button clicked on last section:\",\r\n                            currentSection\r\n                          );\r\n                          console.log(\"🔍 Is last section:\", isLastSection);\r\n                          console.log(\r\n                            \"🔍 Current section index:\",\r\n                            SECTIONS_ORDER.indexOf(currentSection)\r\n                          );\r\n                          console.log(\r\n                            \"🔍 Total sections:\",\r\n                            SECTIONS_ORDER.length\r\n                          );\r\n\r\n                          // Pre-validate before submission\r\n                          const isValid = await validateAllSections();\r\n                          if (!isValid) {\r\n                            e.preventDefault();\r\n                            toast.error(\r\n                              \"Please complete all required fields before creating the product\"\r\n                            );\r\n                          }\r\n                        }}\r\n                        className=\"bg-gradient-to-r from-green-600 to-green-700 px-8 text-white hover:from-green-700 hover:to-green-800 disabled:opacity-50\"\r\n                      >\r\n                        {creating ? (\r\n                          <>\r\n                            <div className=\"mr-2 h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent\" />\r\n                            Creating Product...\r\n                          </>\r\n                        ) : (\r\n                          <>\r\n                            <Package className=\"mr-2 h-4 w-4\" />\r\n                            Create Product\r\n                          </>\r\n                        )}\r\n                      </Button>\r\n                    ) : (\r\n                      <Button\r\n                        type=\"button\"\r\n                        onClick={(e) => {\r\n                          console.log(\r\n                            \"🔍 Continue button clicked on section:\",\r\n                            currentSection\r\n                          );\r\n                          console.log(\"🔍 Is last section:\", isLastSection);\r\n                          console.log(\r\n                            \"🔍 Current section index:\",\r\n                            SECTIONS_ORDER.indexOf(currentSection)\r\n                          );\r\n                          console.log(\r\n                            \"🔍 Next section would be:\",\r\n                            SECTIONS_ORDER[\r\n                              SECTIONS_ORDER.indexOf(currentSection) + 1\r\n                            ]\r\n                          );\r\n                          e.preventDefault(); // Prevent any form submission\r\n                          handleNext();\r\n                        }}\r\n                        className=\"bg-gradient-to-r from-blue-600 to-blue-700 px-8 text-white hover:from-blue-700 hover:to-blue-800\"\r\n                      >\r\n                        Continue\r\n                        <ArrowLeft className=\"ml-2 h-4 w-4 rotate-180\" />\r\n                      </Button>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n              </form>\r\n            </CardContent>\r\n          </Card>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Enhanced Metadata Guide */}\r\n      {currentSection === \"basic-info\" && (\r\n        <Card className=\"border-amber-200 bg-gradient-to-r from-amber-50 to-orange-50\">\r\n          <CardHeader>\r\n            <CardTitle className=\"flex items-center gap-2 text-amber-800\">\r\n              <Sparkles className=\"h-5 w-5\" />\r\n              Quick Tips for Better Products\r\n            </CardTitle>\r\n          </CardHeader>\r\n          <CardContent>\r\n            <div className=\"grid gap-4 md:grid-cols-3\">\r\n              <div className=\"flex items-start gap-3\">\r\n                <div className=\"rounded-full bg-amber-100 p-2\">\r\n                  <Package className=\"h-4 w-4 text-amber-600\" />\r\n                </div>\r\n                <div>\r\n                  <h4 className=\"font-medium text-amber-900\">\r\n                    Clear Product Names\r\n                  </h4>\r\n                  <p className=\"mt-1 text-sm text-amber-700\">\r\n                    Use descriptive names that customers will search for\r\n                  </p>\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"flex items-start gap-3\">\r\n                <div className=\"rounded-full bg-amber-100 p-2\">\r\n                  <Eye className=\"h-4 w-4 text-amber-600\" />\r\n                </div>\r\n                <div>\r\n                  <h4 className=\"font-medium text-amber-900\">Quality Images</h4>\r\n                  <p className=\"mt-1 text-sm text-amber-700\">\r\n                    High-resolution images increase conversion rates\r\n                  </p>\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"flex items-start gap-3\">\r\n                <div className=\"rounded-full bg-amber-100 p-2\">\r\n                  <Zap className=\"h-4 w-4 text-amber-600\" />\r\n                </div>\r\n                <div>\r\n                  <h4 className=\"font-medium text-amber-900\">\r\n                    Detailed Descriptions\r\n                  </h4>\r\n                  <p className=\"mt-1 text-sm text-amber-700\">\r\n                    Include features, benefits, and specifications\r\n                  </p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </CardContent>\r\n        </Card>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AAEA;AACA;AACA;AACA;AACA;AAGA;AACA;AAzBA;;;;;;;;;;;;AAiCO,MAAM,iBAAiB;IAC5B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAEzC;IAEF,MAAM,EACJ,aAAa;IACb,QAAQ,EACR,YAAY,EACZ,QAAQ,EACR,KAAK,EACL,MAAM,EAEN,qBAAqB;IACrB,cAAc,EACd,iBAAiB,EACjB,cAAc,EACd,QAAQ,EACR,UAAU,EACV,cAAc,EACd,mBAAmB,EAEnB,mBAAmB;IACnB,IAAI,EACJ,OAAO,EACP,MAAM,EACN,SAAS,EACT,aAAa,EACb,gBAAgB,EAEhB,kBAAkB;IAClB,QAAQ,EACR,QAAQ,EACR,mBAAmB,EACpB,GAAG,CAAA,GAAA,mIAAA,CAAA,6BAA0B,AAAD;IAE7B,2CAA2C;IAC3C,MAAM,WAAW,CAAA,GAAA,uJAAA,CAAA,kBAAe,AAAD,EAAE;IAEjC,qCAAqC;IACrC,MAAM,gBACJ,mBAAmB,cAAc,CAAC,eAAe,MAAM,GAAG,EAAE;IAE9D,2BAA2B;IAC3B,MAAM,qBAAqB,SAAS,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;IACzD,MAAM,uBAAuB,KAAK,KAAK,CACrC,AAAC,kBAAkB,MAAM,GAAG,eAAe,MAAM,GAAI;IAGvD,gDAAgD;IAChD,CAAA,GAAA,oUAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,OAAO,MAAM,GAAG,GAAG;YACrB,SAAS,aAAa,MAAM,CAAC,EAAE,CAAC,GAAG;QACrC;IACF,GAAG;QAAC;QAAQ;KAAS;IAErB,CAAA,GAAA,oUAAA,CAAA,YAAS,AAAD,EAAE;QACR,SAAS,QAAQ;IACnB,GAAG;QAAC;QAAM;KAAS;IAEnB,0BAA0B;IAC1B,MAAM,iBAAiB;QACrB,cAAc;QACd,qBAAqB;QACrB,WAAW;YACT,cAAc;YACd,WAAW,IAAM,cAAc,SAAS;QAC1C,GAAG;IACL;IAEA,sCAAsC;IACtC,MAAM,gBAAgB,CAAC;QACrB,IAAI,kBAAkB,QAAQ,CAAC,YAAmB;YAChD,qBAAO,6WAAC,+SAAA,CAAA,cAAW;gBAAC,WAAU;;;;;;QAChC;QACA,IAAI,cAAc,gBAAgB;YAChC,qBAAO,6WAAC,wRAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;QAC1B;QACA,qBAAO,6WAAC,wSAAA,CAAA,cAAW;YAAC,WAAU;;;;;;IAChC;IAEA,qBACE,6WAAC;QAAI,WAAU;;0BAEb,6WAAC;gBAAI,WAAU;;kCACb,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;;0DACb,6WAAC;gDAAI,WAAU;0DACb,cAAA,6WAAC,4RAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;;;;;;0DAErB,6WAAC;;kEACC,6WAAC;wDAAG,WAAU;kEAAmC;;;;;;kEAGjD,6WAAC;wDAAE,WAAU;;4DACV,oBAAoB;4DAAM;4DAAQ;4DAClC,eAAe,OAAO,CAAC,kBAAkB;4DAAE;4DAAI;4DAC/C,eAAe,MAAM;;;;;;;;;;;;;;;;;;;kDAK5B,6WAAC;wCAAI,WAAU;;0DAEb,6WAAC;gDAAI,WAAU;;oDACZ,eAAe,0BACd,6WAAC;wDAAI,WAAU;;0EACb,6WAAC;gEAAI,WAAU;;;;;;4DAAoF;;;;;;;oDAItG,eAAe,yBACd,6WAAC;wDAAI,WAAU;;0EACb,6WAAC,+SAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;4DAAY;;;;;;;;;;;;;0DAMzC,6WAAC,2HAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS,IAAM,eAAe,CAAC;;kEAE/B,6WAAC,oRAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;oDACd,cAAc,iBAAiB;;;;;;;0DAGlC,6WAAC,2HAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAU,MAAK;gDAAK,SAAS;;kEAC3C,6WAAC,sRAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;0CAOvC,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;;0DACb,6WAAC;gDAAK,WAAU;;oDAAoC;oDACvC;oDAAqB;;;;;;;0DAElC,6WAAC,0HAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAY,WAAU;;oDAClC,kBAAkB,MAAM;oDAAC;oDAAE,eAAe,MAAM;oDAAC;;;;;;;;;;;;;kDAItD,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,6HAAA,CAAA,WAAQ;gDAAC,OAAO;gDAAU,WAAU;;;;;;0DACrC,6WAAC;gDAAI,WAAU;0DACb,cAAA,6WAAC;oDAAK,WAAU;;wDACb;wDAAqB;;;;;;;;;;;;;;;;;;kDAM5B,6WAAC;wCAAI,WAAU;kDACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6WAAC;gDAEC,WAAU;;oDAET,cAAc,QAAQ,EAAE;kEACzB,6WAAC;wDAAK,WAAU;kEACb,QAAQ,KAAK;;;;;;;+CALX,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;kCAczB,6WAAC;wBAAI,WAAU;;;;;;kCACf,6WAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,6WAAC;gBAAI,WAAU;;kCAEb,6WAAC;wBAAI,WAAU;kCACb,cAAA,6WAAC,yHAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,6WAAC,yHAAA,CAAA,aAAU;oCAAC,WAAU;8CACpB,cAAA,6WAAC,yHAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,6WAAC,8RAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAA0B;;;;;;;;;;;;8CAIlD,6WAAC,yHAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,6WAAC;wCAAI,WAAU;kDACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6WAAC;gDAEC,SAAS,IAAM,oBAAoB,QAAQ,EAAE;gDAC7C,WAAW,CAAC,oFAAoF,EAC9F,mBAAmB,QAAQ,EAAE,GACzB,gEACA,QAAQ,WAAW,GACjB,0EACA,qEACN;;kEAEF,6WAAC;wDAAI,WAAU;kEACZ,cAAc,QAAQ,EAAE;;;;;;kEAG3B,6WAAC;wDAAI,WAAU;;0EACb,6WAAC;gEAAI,WAAU;;kFACb,6WAAC;wEAAK,WAAU;kFACb,QAAQ,KAAK;;;;;;oEAEf,QAAQ,UAAU,kBACjB,6WAAC,0HAAA,CAAA,QAAK;wEAAC,SAAQ;wEAAU,WAAU;kFAAU;;;;;;;;;;;;0EAKjD,6WAAC;gEAAI,WAAU;;oEAA6B;oEACpC,QAAQ;oEAAE;oEAAK,SAAS,MAAM;;;;;;;;;;;;;oDAIvC,mBAAmB,QAAQ,EAAE,kBAC5B,6WAAC,oRAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;;+CA/BZ,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;kCAyC3B,6WAAC;wBAAI,WAAU;;0CAEb,6WAAC,yHAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,6WAAC,yHAAA,CAAA,aAAU;oCAAC,WAAU;8CACpB,cAAA,6WAAC;wCAAI,WAAU;;0DACb,6WAAC;gDAAI,WAAU;;oDACZ,oBAAoB;kEACrB,6WAAC;;0EACC,6WAAC,yHAAA,CAAA,YAAS;gEAAC,WAAU;0EAClB,oBAAoB;;;;;;0EAEvB,6WAAC;gEAAE,WAAU;0EAA6B;;;;;;;;;;;;;;;;;;0DAM9C,6WAAC;gDAAI,WAAU;;oDACZ,kBAAkB,QAAQ,CAAC,iCAC1B,6WAAC,0HAAA,CAAA,QAAK;wDAAC,WAAU;;0EACf,6WAAC,+SAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;kEAI5C,6WAAC,0HAAA,CAAA,QAAK;wDAAC,SAAQ;;4DAAU;4DACjB,eAAe,OAAO,CAAC,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQzD,6WAAC,yHAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,6WAAC,yHAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,6WAAC;wCACC,UAAU,aAAa,UAAU,CAAC;4CAChC,QAAQ,KAAK,CAAC,2BAA2B;4CACzC,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;wCACd;wCACA,WAAU;;0DAGV,6WAAC;gDAAI,WAAU;0DACb,cAAA,6WAAC,8JAAA,CAAA,sBAAmB;oDAClB,gBAAgB;oDAChB,UAAU;oDACV,QAAQ;oDACR,UAAU;oDACV,OAAO;oDACP,MAAM;oDACN,SAAS;oDACT,QAAQ;oDACR,WAAW;oDACX,eAAe;oDACf,kBAAkB;;;;;;;;;;;4CAOrB,mBAAmB,gBAClB,CAAC,OAAO,IAAI,IACV,OAAO,KAAK,IACZ,OAAO,WAAW,IAClB,OAAO,WAAW,mBAClB,6WAAC;gDAAI,WAAU;0DACb,cAAA,6WAAC;oDAAI,WAAU;;sEACb,6WAAC,wSAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;sEACvB,6WAAC;;8EACC,6WAAC;oEAAG,WAAU;8EAA6B;;;;;;8EAG3C,6WAAC;oEAAE,WAAU;8EAA4B;;;;;;8EAIzC,6WAAC;oEAAG,WAAU;;wEACX,OAAO,IAAI,kBAAI,6WAAC;sFAAG;;;;;;wEACnB,OAAO,KAAK,kBAAI,6WAAC;sFAAG;;;;;;wEACpB,OAAO,WAAW,kBAAI,6WAAC;sFAAG;;;;;;wEAC1B,OAAO,WAAW,kBAAI,6WAAC;sFAAG;;;;;;;;;;;;8EAE7B,6WAAC;oEAAI,WAAU;8EAA4B;;;;;;;;;;;;;;;;;;;;;;;4CAUpD,mBAAmB,aAClB,CAAC,OAAO,KAAK,IACX,OAAO,QAAQ,IACf,OAAO,SAAS,IAChB,OAAO,SAAS,mBAChB,6WAAC;gDAAI,WAAU;0DACb,cAAA,6WAAC;oDAAI,WAAU;;sEACb,6WAAC,wSAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;sEACvB,6WAAC;;8EACC,6WAAC;oEAAG,WAAU;8EAA6B;;;;;;8EAG3C,6WAAC;oEAAE,WAAU;8EAA4B;;;;;;8EAIzC,6WAAC;oEAAG,WAAU;;wEACX,OAAO,KAAK,kBAAI,6WAAC;sFAAG;;;;;;wEACpB,OAAO,QAAQ,kBAAI,6WAAC;sFAAG;;;;;;wEACvB,OAAO,SAAS,kBAAI,6WAAC;sFAAG;;;;;;wEACxB,OAAO,SAAS,kBACf,6WAAC;sFAAG;;;;;;;;;;;;8EAGR,6WAAC;oEAAI,WAAU;8EAA4B;;;;;;;;;;;;;;;;;;;;;;;4CAUpD,mBAAmB,eAClB,CAAC,OAAO,KAAK,IACX,OAAO,SAAS,IAChB,OAAO,eAAe,mBACtB,6WAAC;gDAAI,WAAU;0DACb,cAAA,6WAAC;oDAAI,WAAU;;sEACb,6WAAC,wSAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;sEACvB,6WAAC;;8EACC,6WAAC;oEAAG,WAAU;8EAA6B;;;;;;8EAG3C,6WAAC;oEAAE,WAAU;8EAA4B;;;;;;8EAIzC,6WAAC;oEAAG,WAAU;;wEACX,OAAO,KAAK,kBAAI,6WAAC;sFAAG;;;;;;wEACpB,OAAO,SAAS,kBAAI,6WAAC;sFAAG;;;;;;wEACxB,OAAO,eAAe,kBACrB,6WAAC;sFAAG;;;;;;;;;;;;8EAGR,6WAAC;oEAAI,WAAU;8EAA4B;;;;;;;;;;;;;;;;;;;;;;;4CAUpD,mBAAmB,aAClB,CAAC,OAAO,QAAQ,IACd,OAAO,QAAQ,IACf,OAAO,IAAI,IACX,OAAO,KAAK,mBACZ,6WAAC;gDAAI,WAAU;0DACb,cAAA,6WAAC;oDAAI,WAAU;;sEACb,6WAAC,wSAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;sEACvB,6WAAC;;8EACC,6WAAC;oEAAG,WAAU;8EAA6B;;;;;;8EAG3C,6WAAC;oEAAE,WAAU;8EAA4B;;;;;;8EAIzC,6WAAC;oEAAG,WAAU;;wEACX,OAAO,QAAQ,kBAAI,6WAAC;sFAAG;;;;;;wEACvB,OAAO,QAAQ,kBAAI,6WAAC;sFAAG;;;;;;wEACvB,OAAO,IAAI,kBACV,6WAAC;sFAAG;;;;;;wEAEL,OAAO,KAAK,kBAAI,6WAAC;sFAAG;;;;;;;;;;;;8EAEvB,6WAAC;oEAAI,WAAU;8EAA4B;;;;;;;;;;;;;;;;;;;;;;;4CAapD,mBAAmB,WAClB,CAAC,OAAO,SAAS,IACd,OAAO,MAAM,GAAG,KACf,OAAO,IAAI,CACT,CAAC,GAAG,QACF,CAAC,aAAa,CAAC,MAAM,IACrB,aAAa,CAAC,MAAM,CAAC,IAAI,OAAO,GAClC,mBACJ,6WAAC;gDAAI,WAAU;0DACb,cAAA,6WAAC;oDAAI,WAAU;;sEACb,6WAAC,wSAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;sEACvB,6WAAC;;8EACC,6WAAC;oEAAG,WAAU;8EAA6B;;;;;;8EAG3C,6WAAC;oEAAE,WAAU;8EAA4B;;;;;;8EAIzC,6WAAC;oEAAG,WAAU;;wEACX,OAAO,SAAS,kBAAI,6WAAC;sFAAG;;;;;;wEACxB,OAAO,MAAM,GAAG,KACf,OAAO,IAAI,CACT,CAAC,GAAG,QACF,CAAC,aAAa,CAAC,MAAM,IACrB,aAAa,CAAC,MAAM,CAAC,IAAI,OAAO,qBAElC,6WAAC;sFAAG;;;;;;;;;;;;8EAMV,6WAAC;oEAAI,WAAU;8EAA4B;;;;;;;;;;;;;;;;;;;;;;;4CAWpD,mBAAmB,cAClB,CAAC,OAAO,aAAa,IACnB,OAAO,YAAY,IACnB,OAAO,YAAY,mBACnB,6WAAC;gDAAI,WAAU;0DACb,cAAA,6WAAC;oDAAI,WAAU;;sEACb,6WAAC,wSAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;sEACvB,6WAAC;;8EACC,6WAAC;oEAAG,WAAU;8EAA6B;;;;;;8EAG3C,6WAAC;oEAAE,WAAU;8EAA4B;;;;;;8EAIzC,6WAAC;oEAAG,WAAU;;wEACX,OAAO,aAAa,kBAAI,6WAAC;sFAAG;;;;;;wEAC5B,OAAO,YAAY,kBAAI,6WAAC;sFAAG;;;;;;wEAC3B,OAAO,YAAY,kBAClB,6WAAC;sFAAG;;;;;;;;;;;;8EAGR,6WAAC;oEAAI,WAAU;8EAA4B;;;;;;;;;;;;;;;;;;;;;;;4CAWpD,mBAAmB,kBAAkB,OAAO,MAAM,kBACjD,6WAAC;gDAAI,WAAU;0DACb,cAAA,6WAAC;oDAAI,WAAU;;sEACb,6WAAC,wSAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;sEACvB,6WAAC;;8EACC,6WAAC;oEAAG,WAAU;8EAA6B;;;;;;8EAG3C,6WAAC;oEAAE,WAAU;8EAA4B;;;;;;8EAIzC,6WAAC;oEAAG,WAAU;8EACX,OAAO,MAAM,kBAAI,6WAAC;kFAAG;;;;;;;;;;;8EAExB,6WAAC;oEAAI,WAAU;8EAA4B;;;;;;;;;;;;;;;;;;;;;;;0DAUnD,6WAAC;gDAAI,WAAU;;kEACb,6WAAC;wDAAI,WAAU;kEACZ,eAAe,OAAO,CAAC,kBAAkB,mBACxC,6WAAC,2HAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAQ;4DACR,SAAS;4DACT,WAAU;;8EAEV,6WAAC,oSAAA,CAAA,YAAS;oEAAC,WAAU;;;;;;gEAAY;;;;;;;;;;;;kEAMvC,6WAAC;wDAAI,WAAU;;0EACb,6WAAC,2HAAA,CAAA,SAAM;gEACL,MAAK;gEACL,SAAQ;gEACR,SAAS;gEACT,WAAU;;kFAEV,6WAAC,sRAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;4DAIlC,8BACC,6WAAC,2HAAA,CAAA,SAAM;gEACL,MAAK;gEACL,UAAU;gEACV,SAAS,OAAO;oEACd,QAAQ,GAAG,CACT,6CACA;oEAEF,QAAQ,GAAG,CAAC,uBAAuB;oEACnC,QAAQ,GAAG,CACT,6BACA,eAAe,OAAO,CAAC;oEAEzB,QAAQ,GAAG,CACT,sBACA,eAAe,MAAM;oEAGvB,iCAAiC;oEACjC,MAAM,UAAU,MAAM;oEACtB,IAAI,CAAC,SAAS;wEACZ,EAAE,cAAc;wEAChB,wQAAA,CAAA,QAAK,CAAC,KAAK,CACT;oEAEJ;gEACF;gEACA,WAAU;0EAET,yBACC;;sFACE,6WAAC;4EAAI,WAAU;;;;;;wEAAsF;;iGAIvG;;sFACE,6WAAC,4RAAA,CAAA,UAAO;4EAAC,WAAU;;;;;;wEAAiB;;;;;;;qFAM1C,6WAAC,2HAAA,CAAA,SAAM;gEACL,MAAK;gEACL,SAAS,CAAC;oEACR,QAAQ,GAAG,CACT,0CACA;oEAEF,QAAQ,GAAG,CAAC,uBAAuB;oEACnC,QAAQ,GAAG,CACT,6BACA,eAAe,OAAO,CAAC;oEAEzB,QAAQ,GAAG,CACT,6BACA,cAAc,CACZ,eAAe,OAAO,CAAC,kBAAkB,EAC1C;oEAEH,EAAE,cAAc,IAAI,8BAA8B;oEAClD;gEACF;gEACA,WAAU;;oEACX;kFAEC,6WAAC,oSAAA,CAAA,YAAS;wEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAYtC,mBAAmB,8BAClB,6WAAC,yHAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6WAAC,yHAAA,CAAA,aAAU;kCACT,cAAA,6WAAC,yHAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6WAAC,8RAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;;;;;kCAIpC,6WAAC,yHAAA,CAAA,cAAW;kCACV,cAAA,6WAAC;4BAAI,WAAU;;8CACb,6WAAC;oCAAI,WAAU;;sDACb,6WAAC;4CAAI,WAAU;sDACb,cAAA,6WAAC,4RAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;;;;;;sDAErB,6WAAC;;8DACC,6WAAC;oDAAG,WAAU;8DAA6B;;;;;;8DAG3C,6WAAC;oDAAE,WAAU;8DAA8B;;;;;;;;;;;;;;;;;;8CAM/C,6WAAC;oCAAI,WAAU;;sDACb,6WAAC;4CAAI,WAAU;sDACb,cAAA,6WAAC,oRAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;;;;;;sDAEjB,6WAAC;;8DACC,6WAAC;oDAAG,WAAU;8DAA6B;;;;;;8DAC3C,6WAAC;oDAAE,WAAU;8DAA8B;;;;;;;;;;;;;;;;;;8CAM/C,6WAAC;oCAAI,WAAU;;sDACb,6WAAC;4CAAI,WAAU;sDACb,cAAA,6WAAC,oRAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;;;;;;sDAEjB,6WAAC;;8DACC,6WAAC;oDAAG,WAAU;8DAA6B;;;;;;8DAG3C,6WAAC;oDAAE,WAAU;8DAA8B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW7D", "debugId": null}}]}