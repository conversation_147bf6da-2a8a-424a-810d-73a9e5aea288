"use client";

import { useEffect, useState } from "react";

import { motion } from "framer-motion";
import { Menu } from "lucide-react";
import Link from "next/link";

const Navbar = () => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  return (
    <nav
      className={`fixed left-0 top-0 z-50 h-8 w-full transition-all duration-300 ${
        isScrolled
          ? "bg-white opacity-95 shadow-md dark:bg-gray-900"
          : "bg-transparent"
      }`}
    >
      <div className="mx-auto max-w-7xl px-6 lg:px-10">
        <div className="flex items-center justify-between py-4">
          {/* Logo */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
          >
            <Link
              href="/"
              className="text-2xl font-bold uppercase tracking-widest"
            >
              Tavo<span className="text-orange-400">Įmonė</span>
            </Link>
          </motion.div>

          <div className="flex gap-5">
            <Link href="/" className="nav-link">
              Pagrindinis
            </Link>

            <Link href="/services" className="nav-link">
              Paslaugos
            </Link>

            <Link href="/about" className="nav-link">
              Apie Mus
            </Link>

            <Link href="/contact" className="nav-link">
              Kontaktai
            </Link>
          </div>

          <div className="hidden space-x-6 md:flex">
            <div className="hidden space-x-2 md:flex">
              <Link
                href="#"
                className="text-white transition-colors hover:text-orange-500"
              >
                EN
              </Link>
              <span>|</span>
              <Link
                href="#"
                className="text-white transition-colors hover:text-orange-500"
              >
                LT
              </Link>
            </div>
          </div>

          {/* Mobile Menu Button */}
          <button
            className="md:hidden"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          >
            <Menu size={28} />
          </button>
        </div>
      </div>

      {/* Mobile Menu */}
      {isMobileMenuOpen && (
        <div className="absolute left-0 top-full w-full bg-white shadow-md md:hidden dark:bg-gray-900">
          <div className="flex flex-col items-center space-y-4 py-4">
            <Link
              href="/"
              className="nav-link"
              onClick={() => setIsMobileMenuOpen(false)}
            >
              Pagrindinis
            </Link>
            <Link
              href="/services"
              className="nav-link"
              onClick={() => setIsMobileMenuOpen(false)}
            >
              Paslaugos
            </Link>
            <Link
              href="/about"
              className="nav-link"
              onClick={() => setIsMobileMenuOpen(false)}
            >
              Apie Mus
            </Link>
            <Link
              href="/contact"
              className="nav-link"
              onClick={() => setIsMobileMenuOpen(false)}
            >
              Kontaktai
            </Link>

            <div className="flex space-x-4 border-t border-gray-200 pt-2">
              <Link
                href="#"
                className="text-gray-800 transition-colors hover:text-orange-500"
              >
                EN
              </Link>
              <Link
                href="#"
                className="text-gray-800 transition-colors hover:text-orange-500"
              >
                LT
              </Link>
            </div>
          </div>
        </div>
      )}
    </nav>
  );
};

export default Navbar;
