import { useState } from "react";

import { toast } from "sonner";

import { SectionId } from "@/types/form-section";

const SECTIONS_ORDER: SectionId[] = [
  "basic-info",
  "pricing",
  "inventory",
  "details",
  "media",
  "shipping",
  "availability",
];

export const useSectionNavigation = () => {
  const [currentSection, setCurrentSection] = useState<SectionId>("basic-info");
  const [completedSections, setCompletedSections] = useState<SectionId[]>([]);

  const goToSection = (next: SectionId) => setCurrentSection(next);

  const handleSectionChange = (section: SectionId) => {
    if (
      completedSections.includes(section) ||
      section === currentSection ||
      SECTIONS_ORDER.indexOf(section) === completedSections.length
    ) {
      setCurrentSection(section);
    } else {
      toast.info("Please complete the current section first");
    }
  };

  return {
    currentSection,
    completedSections,
    setCompletedSections,
    setCurrentSection,
    goToSection,
    handleSectionChange,
    SECTIONS_ORDER,
  };
};
