<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Swagger API Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #333;
        }
        .endpoint {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .endpoint h2 {
            margin-top: 0;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
        input, textarea {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            box-sizing: border-box;
        }
        .response {
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <h1>Swagger API Test</h1>
    
    <div class="endpoint">
        <h2>GET /api/products</h2>
        <p>Retrieve all products</p>
        <button onclick="getProducts()">Test</button>
        <div class="response">
            <h3>Response:</h3>
            <pre id="getProductsResponse">Click Test to see response</pre>
        </div>
    </div>

    <div class="endpoint">
        <h2>POST /api/products</h2>
        <p>Create a new product</p>
        <textarea id="createProductData" rows="15">
{
  "name": "Test Product",
  "brand": "Test Brand",
  "description": "This is a test product created via the API",
  "price": 99.99,
  "currency": "EUR",
  "stock": 10,
  "condition": "new",
  "category": "Test Category",
  "mainImage": "https://example.com/test-image.jpg",
  "status": "published",
  "ageRestriction": "none",
  "isPublished": true,
  "freeShipping": true
}
        </textarea>
        <button onclick="createProduct()">Test</button>
        <div class="response">
            <h3>Response:</h3>
            <pre id="createProductResponse">Click Test to see response</pre>
        </div>
    </div>

    <div class="endpoint">
        <h2>GET /api/products/{id}</h2>
        <p>Retrieve a specific product</p>
        <input type="text" id="getProductId" placeholder="Enter product ID">
        <button onclick="getProductById()">Test</button>
        <div class="response">
            <h3>Response:</h3>
            <pre id="getProductByIdResponse">Click Test to see response</pre>
        </div>
    </div>

    <script>
        const API_URL = 'http://localhost:3010/api';

        async function getProducts() {
            const responseElement = document.getElementById('getProductsResponse');
            responseElement.textContent = 'Loading...';
            
            try {
                const response = await fetch(`${API_URL}/products`);
                const data = await response.json();
                responseElement.textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                responseElement.textContent = `Error: ${error.message}`;
            }
        }

        async function createProduct() {
            const responseElement = document.getElementById('createProductResponse');
            responseElement.textContent = 'Loading...';
            
            try {
                const productData = document.getElementById('createProductData').value;
                const response = await fetch(`${API_URL}/products`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: productData
                });
                const data = await response.json();
                responseElement.textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                responseElement.textContent = `Error: ${error.message}`;
            }
        }

        async function getProductById() {
            const responseElement = document.getElementById('getProductByIdResponse');
            const productId = document.getElementById('getProductId').value;
            
            if (!productId) {
                responseElement.textContent = 'Please enter a product ID';
                return;
            }
            
            responseElement.textContent = 'Loading...';
            
            try {
                const response = await fetch(`${API_URL}/products/${productId}`);
                const data = await response.json();
                responseElement.textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                responseElement.textContent = `Error: ${error.message}`;
            }
        }
    </script>
</body>
</html>
