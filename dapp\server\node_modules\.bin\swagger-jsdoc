#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/Desktop/Learning/Codes/dapp/server/node_modules/.pnpm/swagger-jsdoc@6.2.8_openapi-types@12.1.3/node_modules/swagger-jsdoc/bin/node_modules:/mnt/c/Users/<USER>/Desktop/Learning/Codes/dapp/server/node_modules/.pnpm/swagger-jsdoc@6.2.8_openapi-types@12.1.3/node_modules/swagger-jsdoc/node_modules:/mnt/c/Users/<USER>/Desktop/Learning/Codes/dapp/server/node_modules/.pnpm/swagger-jsdoc@6.2.8_openapi-types@12.1.3/node_modules:/mnt/c/Users/<USER>/Desktop/Learning/Codes/dapp/server/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/Desktop/Learning/Codes/dapp/server/node_modules/.pnpm/swagger-jsdoc@6.2.8_openapi-types@12.1.3/node_modules/swagger-jsdoc/bin/node_modules:/mnt/c/Users/<USER>/Desktop/Learning/Codes/dapp/server/node_modules/.pnpm/swagger-jsdoc@6.2.8_openapi-types@12.1.3/node_modules/swagger-jsdoc/node_modules:/mnt/c/Users/<USER>/Desktop/Learning/Codes/dapp/server/node_modules/.pnpm/swagger-jsdoc@6.2.8_openapi-types@12.1.3/node_modules:/mnt/c/Users/<USER>/Desktop/Learning/Codes/dapp/server/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../swagger-jsdoc/bin/swagger-jsdoc.js" "$@"
else
  exec node  "$basedir/../swagger-jsdoc/bin/swagger-jsdoc.js" "$@"
fi
