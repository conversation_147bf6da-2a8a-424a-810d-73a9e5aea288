#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/Desktop/Learning/Codes/dapp/server/node_modules/.pnpm/z-schema@5.0.5/node_modules/z-schema/bin/node_modules:/mnt/c/Users/<USER>/Desktop/Learning/Codes/dapp/server/node_modules/.pnpm/z-schema@5.0.5/node_modules/z-schema/node_modules:/mnt/c/Users/<USER>/Desktop/Learning/Codes/dapp/server/node_modules/.pnpm/z-schema@5.0.5/node_modules:/mnt/c/Users/<USER>/Desktop/Learning/Codes/dapp/server/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/Desktop/Learning/Codes/dapp/server/node_modules/.pnpm/z-schema@5.0.5/node_modules/z-schema/bin/node_modules:/mnt/c/Users/<USER>/Desktop/Learning/Codes/dapp/server/node_modules/.pnpm/z-schema@5.0.5/node_modules/z-schema/node_modules:/mnt/c/Users/<USER>/Desktop/Learning/Codes/dapp/server/node_modules/.pnpm/z-schema@5.0.5/node_modules:/mnt/c/Users/<USER>/Desktop/Learning/Codes/dapp/server/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../../z-schema@5.0.5/node_modules/z-schema/bin/z-schema" "$@"
else
  exec node  "$basedir/../../../../../../z-schema@5.0.5/node_modules/z-schema/bin/z-schema" "$@"
fi
