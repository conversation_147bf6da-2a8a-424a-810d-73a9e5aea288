import { Info } from "lucide-react";
import Link from "next/link";

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";

/**
 * A component that provides guidance on managing product metadata
 * (categories, brands, materials) during product creation
 */
export const MetadataGuide = () => {
  return (
    <Alert className="mb-6 bg-blue-50">
      <Info className="size-5 text-blue-600" />
      <AlertTitle className="text-blue-700">Product Metadata</AlertTitle>
      <AlertDescription className="text-blue-600">
        <p className="mb-2">
          You can manage product metadata before or during product creation.
        </p>
        <Accordion type="single" collapsible className="w-full">
          <AccordionItem value="metadata-guide">
            <AccordionTrigger className="text-sm font-medium text-blue-700">
              How to manage product metadata?
            </AccordionTrigger>
            <AccordionContent className="text-sm">
              <ul className="list-inside list-disc space-y-1 pl-2">
                <li>
                  <Link
                    href="/products/categories"
                    target="_blank"
                    className="font-medium text-blue-600 hover:underline"
                  >
                    Categories
                  </Link>{" "}
                  - Organize your products into logical groups
                </li>
                <li>
                  <Link
                    href="/products/brands"
                    target="_blank"
                    className="font-medium text-blue-600 hover:underline"
                  >
                    Brands
                  </Link>{" "}
                  - Manage product manufacturers and brands
                </li>
                <li>
                  <Link
                    href="/products/materials"
                    target="_blank"
                    className="font-medium text-blue-600 hover:underline"
                  >
                    Materials
                  </Link>{" "}
                  - Define materials used in your products
                </li>
              </ul>
              <p className="mt-2 text-xs">
                You can also add custom values directly in the form if needed.
              </p>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </AlertDescription>
    </Alert>
  );
};
