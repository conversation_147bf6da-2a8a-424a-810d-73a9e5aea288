import { ReactNode } from "react";

import { Button } from "@/components/ui/button";

import { InfoRow } from "./InfoRow";

type InfoSectionProps = {
  icon: ReactNode;
  title: string;
  rows: Array<{ label?: string; value: string }>;
  linkText?: string;
  linkAction?: () => void;
};

export const InfoSection = ({
  icon,
  title,
  rows,
  linkText,
  linkAction,
}: InfoSectionProps) => (
  <div className="flex items-start">
    <div className="mr-3 mt-1 flex shrink-0 text-blue-600">{icon}</div>

    <div className="w-full">
      <h3 className="mb-1 font-medium">{title}</h3>
      {rows.map((row, index) => (
        <InfoRow key={index} label={row.label} value={row.value} />
      ))}

      {linkText && (
        <Button
          variant={"link"}
          onClick={linkAction}
          className="mt-2 p-0 text-sm font-normal text-blue-600 hover:underline"
        >
          {linkText}
        </Button>
      )}
    </div>
  </div>
);
