"use client";

import { useEffect, useState } from "react";

import {
  BarChart3,
  Bell,
  ChevronDown,
  ChevronRight,
  Command,
  HelpCircle,
  Home,
  LogOut,
  MessageSquare,
  Moon,
  Package,
  Plus,
  Search,
  Settings,
  ShoppingCart,
  Sun,
  User,
  Users,
  X,
  Zap,
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

export default function TopNavbar() {
  const [darkMode, setDarkMode] = useState(false);
  const [searchOpen, setSearchOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [notifications] = useState([
    {
      id: 1,
      title: "New order received",
      message: "Order #1234 from <PERSON>",
      time: "2 min ago",
      unread: true,
    },
    {
      id: 2,
      title: "Low stock alert",
      message: "T-shirt inventory is running low",
      time: "1 hour ago",
      unread: true,
    },
    {
      id: 3,
      title: "Payment processed",
      message: "Payment of $299.99 confirmed",
      time: "3 hours ago",
      unread: false,
    },
  ]);

  const pathname = usePathname();
  const router = useRouter();

  const toggleDarkMode = () => {
    const html = document.documentElement;
    html.classList.toggle("dark");
    setDarkMode(html.classList.contains("dark"));
  };

  // Generate breadcrumbs from pathname
  const generateBreadcrumbs = () => {
    const paths = pathname.split("/").filter(Boolean);
    const breadcrumbs = [];

    // Handle root/admin paths
    if (
      pathname === "/" ||
      pathname === "/admin" ||
      pathname === "/admin/dashboard"
    ) {
      return [{ label: "Dashboard", href: "/admin/dashboard", icon: Home }];
    }

    // Skip admin prefix and build meaningful breadcrumbs
    const adminPaths = paths.slice(1); // Remove 'admin' from paths

    // Always start with Dashboard for non-dashboard pages
    breadcrumbs.push({
      label: "Dashboard",
      href: "/admin/dashboard",
      icon: Home,
    });

    let currentPath = "/admin";
    adminPaths.forEach((path, index) => {
      currentPath += `/${path}`;

      // Convert path to readable label
      let label = path.charAt(0).toUpperCase() + path.slice(1);
      if (label === "List") label = "All";
      if (label === "Add") label = "New";

      // Add appropriate icons
      let icon = null;
      if (path === "products") icon = Package;
      if (path === "orders") icon = ShoppingCart;
      if (path === "customers") icon = Users;
      if (path === "analytics") icon = BarChart3;

      breadcrumbs.push({ label, href: currentPath, icon });
    });

    return breadcrumbs;
  };

  const breadcrumbs = generateBreadcrumbs();
  const unreadCount = notifications.filter((n) => n.unread).length;

  // Quick actions based on current page
  const getQuickActions = () => {
    if (pathname.includes("/products")) {
      return [
        { label: "Add Product", href: "/admin/products/add", icon: Plus },
        {
          label: "Categories",
          href: "/admin/products/categories",
          icon: Package,
        },
      ];
    }
    if (pathname.includes("/orders")) {
      return [
        { label: "New Order", href: "/admin/orders/new", icon: Plus },
        {
          label: "Export Orders",
          action: () => console.log("Export"),
          icon: BarChart3,
        },
      ];
    }
    if (pathname.includes("/customers")) {
      return [
        { label: "Add Customer", href: "/admin/customers/add", icon: Plus },
        {
          label: "Export Customers",
          action: () => console.log("Export"),
          icon: BarChart3,
        },
      ];
    }
    return [
      { label: "Add Product", href: "/admin/products/add", icon: Plus },
      { label: "View Orders", href: "/admin/orders/list", icon: ShoppingCart },
    ];
  };

  const quickActions = getQuickActions();

  // Close search overlay when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (!target.closest("header")) {
        setSearchOpen(false);
      }
    };

    if (searchOpen) {
      document.addEventListener("mousedown", handleClickOutside);
      return () =>
        document.removeEventListener("mousedown", handleClickOutside);
    }
  }, [searchOpen]);

  return (
    <header className="sticky top-0 z-10 border-b bg-white shadow-sm">
      {/* Main Navigation Bar */}
      <div className="flex w-full items-center justify-between px-6 py-3">
        {/* Left: Breadcrumbs */}
        <div className="flex flex-1 items-center gap-4">
          <nav className="flex items-center space-x-1 text-sm text-gray-500">
            {breadcrumbs.map((crumb, index) => (
              <div key={`${crumb.href}-${index}`} className="flex items-center">
                {index > 0 && <ChevronRight className="mx-2 h-4 w-4" />}
                <Link
                  href={crumb.href}
                  className={`flex items-center gap-1 rounded-md px-2 py-1 hover:bg-gray-100 hover:text-gray-900 ${
                    index === breadcrumbs.length - 1
                      ? "font-medium text-gray-900"
                      : "text-gray-500"
                  }`}
                >
                  {crumb.icon && <crumb.icon className="h-4 w-4" />}
                  {crumb.label}
                </Link>
              </div>
            ))}
          </nav>
        </div>

        {/* Center: Search */}
        <div className="flex flex-1 justify-center">
          <div className="relative w-full max-w-md">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Search products, orders, customers..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full rounded-lg border border-gray-200 bg-gray-50 py-2 pl-10 pr-4 text-sm focus:border-blue-500 focus:bg-white focus:outline-none focus:ring-1 focus:ring-blue-500"
              onFocus={() => setSearchOpen(true)}
            />
            {searchQuery && (
              <button
                onClick={() => setSearchQuery("")}
                className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600"
              >
                <X className="h-4 w-4" />
              </button>
            )}
          </div>
        </div>

        {/* Right: Actions & User */}
        <div className="flex flex-1 items-center justify-end gap-2">
          {/* Quick Actions */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="hidden sm:flex">
                <Zap className="mr-2 h-4 w-4" />
                Quick Actions
                <ChevronDown className="ml-2 h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              {quickActions.map((action, index) => (
                <DropdownMenuItem key={index} asChild={!!action.href}>
                  {action.href ? (
                    <Link href={action.href} className="flex items-center">
                      <action.icon className="mr-2 h-4 w-4" />
                      {action.label}
                    </Link>
                  ) : (
                    <button
                      onClick={action.action}
                      className="flex w-full items-center"
                    >
                      <action.icon className="mr-2 h-4 w-4" />
                      {action.label}
                    </button>
                  )}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Separator */}
          <div className="hidden h-6 w-px bg-gray-200 sm:block" />

          {/* Help & Support */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-9 w-9 p-0"
                title="Help & Support"
              >
                <HelpCircle className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <div className="p-3">
                <h3 className="text-sm font-semibold">Help & Support</h3>
                <p className="mt-1 text-xs text-gray-500">
                  Get help and learn more
                </p>
              </div>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                <HelpCircle className="mr-2 h-4 w-4" />
                Documentation
              </DropdownMenuItem>
              <DropdownMenuItem>
                <MessageSquare className="mr-2 h-4 w-4" />
                Contact Support
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Command className="mr-2 h-4 w-4" />
                Keyboard Shortcuts
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                <div className="flex w-full items-center justify-between">
                  <span className="text-sm">What's New</span>
                  <Badge className="bg-green-100 text-xs text-green-700">
                    New
                  </Badge>
                </div>
              </DropdownMenuItem>
              <DropdownMenuItem className="text-xs text-gray-500">
                Version 2.1.0
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Dark mode toggle */}
          <Button
            variant="ghost"
            size="sm"
            onClick={toggleDarkMode}
            className="h-9 w-9 p-0"
            title="Toggle theme"
          >
            {darkMode ? (
              <Sun className="h-4 w-4" />
            ) : (
              <Moon className="h-4 w-4" />
            )}
          </Button>

          {/* Notifications */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="relative flex h-9 w-9 p-0"
              >
                <Bell className="h-4 w-4" />
                {unreadCount > 0 && (
                  <Badge className="absolute -right-1 -top-1 flex h-5 w-5 items-center justify-center rounded-full bg-red-500 p-0 text-xs font-medium text-white">
                    {unreadCount}
                  </Badge>
                )}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-80">
              <div className="flex items-center justify-between p-3">
                <h3 className="font-semibold">Notifications</h3>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-auto p-0 text-xs"
                >
                  Mark all read
                </Button>
              </div>
              <DropdownMenuSeparator />
              <div className="max-h-64 overflow-y-auto">
                {notifications.map((notification) => (
                  <DropdownMenuItem
                    key={notification.id}
                    className="flex-col items-start p-3"
                  >
                    <div className="flex w-full items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <p className="text-sm font-medium">
                            {notification.title}
                          </p>
                          {notification.unread && (
                            <div className="h-2 w-2 rounded-full bg-blue-500" />
                          )}
                        </div>
                        <p className="mt-1 text-xs text-gray-500">
                          {notification.message}
                        </p>
                        <p className="mt-1 text-xs text-gray-400">
                          {notification.time}
                        </p>
                      </div>
                    </div>
                  </DropdownMenuItem>
                ))}
              </div>
              <DropdownMenuSeparator />
              <DropdownMenuItem className="justify-center p-3">
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-auto p-0 text-xs"
                >
                  View all notifications
                </Button>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          {/* User Menu */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                className="relative h-9 w-9 rounded-full p-0"
              >
                <Image
                  src="https://randomuser.me/api/portraits/men/75.jpg"
                  alt="User"
                  width={36}
                  height={36}
                  className="rounded-full object-cover"
                />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-64">
              {/* User Profile Header */}
              <div className="flex items-center gap-3 bg-gray-50 p-4">
                <Image
                  src="https://randomuser.me/api/portraits/men/75.jpg"
                  alt="User"
                  width={48}
                  height={48}
                  className="rounded-full object-cover shadow-sm ring-2 ring-white"
                />
                <div className="flex-1">
                  <p className="font-semibold text-gray-900">Admin User</p>
                  <p className="text-sm text-gray-500"><EMAIL></p>
                  <p className="text-xs text-gray-400">Administrator</p>
                </div>
              </div>
              <DropdownMenuSeparator />

              {/* Account Management */}
              <div className="p-1">
                <DropdownMenuItem asChild>
                  <Link href="/admin/profile" className="flex items-center">
                    <User className="mr-2 h-4 w-4" />
                    <div>
                      <div className="font-medium">Profile</div>
                      <div className="text-xs text-gray-500">
                        Manage your account
                      </div>
                    </div>
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Settings className="mr-2 h-4 w-4" />
                  <div>
                    <div className="font-medium">Account Settings</div>
                    <div className="text-xs text-gray-500">
                      Privacy and preferences
                    </div>
                  </div>
                </DropdownMenuItem>
              </div>

              <DropdownMenuSeparator />

              {/* Quick Stats */}
              <div className="bg-blue-50 p-3">
                <div className="mb-2 text-xs font-medium text-blue-900">
                  Quick Stats
                </div>
                <div className="grid grid-cols-2 gap-3 text-xs">
                  <div>
                    <div className="font-medium text-gray-900">24</div>
                    <div className="text-gray-500">Orders Today</div>
                  </div>
                  <div>
                    <div className="font-medium text-gray-900">$1,234</div>
                    <div className="text-gray-500">Revenue</div>
                  </div>
                </div>
              </div>

              <DropdownMenuSeparator />

              {/* Sign Out */}
              <div className="p-1">
                <DropdownMenuItem className="text-red-600 focus:bg-red-50 focus:text-red-700">
                  <LogOut className="mr-2 h-4 w-4" />
                  <div>
                    <div className="font-medium">Sign out</div>
                    <div className="text-xs opacity-75">End your session</div>
                  </div>
                </DropdownMenuItem>
              </div>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Search Results Overlay */}
      {searchOpen && searchQuery && (
        <div className="absolute left-0 right-0 top-full z-50 border-b bg-white shadow-lg">
          <div className="p-4">
            <div className="mb-3 text-sm text-gray-500">
              Search results for "{searchQuery}"
            </div>
            <div className="space-y-2">
              <div className="flex items-center gap-3 rounded-lg p-2 hover:bg-gray-50">
                <Package className="h-4 w-4 text-gray-400" />
                <div>
                  <p className="text-sm font-medium">T-shirt for Men</p>
                  <p className="text-xs text-gray-500">Product • $90.00</p>
                </div>
              </div>
              <div className="flex items-center gap-3 rounded-lg p-2 hover:bg-gray-50">
                <ShoppingCart className="h-4 w-4 text-gray-400" />
                <div>
                  <p className="text-sm font-medium">Order #1234</p>
                  <p className="text-xs text-gray-500">Order • $299.99</p>
                </div>
              </div>
            </div>
            <div className="mt-3 border-t pt-3">
              <Button
                variant="ghost"
                size="sm"
                className="w-full justify-start"
              >
                <Search className="mr-2 h-4 w-4" />
                Search all results for "{searchQuery}"
              </Button>
            </div>
          </div>
        </div>
      )}
    </header>
  );
}
