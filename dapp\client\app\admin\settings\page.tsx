"use client";

import { useEffect, useState } from "react";

import {
  Building2,
  CreditCard,
  Download,
  Globe,
  Key,
  Mail,
  Palette,
  Save,
  Settings,
  Shield,
  Store,
  TrendingUp,
  Upload,
  Users,
} from "lucide-react";
import { useSearchParams } from "next/navigation";

import { PageHeaderWrapper } from "@/components/common/PageHeaderWrapper";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";

export default function SystemSettings() {
  const [activeTab, setActiveTab] = useState("store");
  const [isLoading, setIsLoading] = useState(false);
  const searchParams = useSearchParams();

  // Handle URL parameters for direct tab navigation
  useEffect(() => {
    const tab = searchParams.get("tab");
    if (
      tab &&
      [
        "store",
        "communications",
        "payments",
        "security",
        "branding",
        "analytics",
      ].includes(tab)
    ) {
      setActiveTab(tab);
    }
  }, [searchParams]);

  // Store configuration state
  const [storeSettings, setStoreSettings] = useState({
    storeName: "E-Commerce Store",
    storeDescription: "Your premier online shopping destination",
    contactEmail: "<EMAIL>",
    supportEmail: "<EMAIL>",
    phone: "+****************",
    address: "123 Business St, City, State 12345",
    businessHours: "Mon-Fri 9AM-6PM EST",
    defaultCurrency: "USD",
    defaultLanguage: "en",
    timezone: "America/New_York",
    taxRate: "8.25",
    enableTax: true,
  });

  // Communication settings state
  const [communicationSettings, setCommunicationSettings] = useState({
    smtpHost: "smtp.gmail.com",
    smtpPort: "587",
    smtpUsername: "",
    smtpPassword: "",
    fromEmail: "<EMAIL>",
    fromName: "E-Commerce Store",
    enableEmailNotifications: true,
    enableSMSNotifications: false,
    orderConfirmationTemplate: "default",
    shippingNotificationTemplate: "default",
  });

  // Payment settings state
  const [paymentSettings, setPaymentSettings] = useState({
    enableStripe: true,
    stripePublishableKey: "",
    stripeSecretKey: "",
    enablePayPal: true,
    paypalClientId: "",
    paypalClientSecret: "",
    enableCOD: false,
    enableBankTransfer: false,
    minimumOrderAmount: "10.00",
    freeShippingThreshold: "50.00",
  });

  // Security settings state
  const [securitySettings, setSecuritySettings] = useState({
    enableTwoFactor: false,
    sessionTimeout: "30",
    maxLoginAttempts: "5",
    enableCaptcha: true,
    enableSSL: true,
    enableFirewall: false,
    allowedIPs: "",
    blockedIPs: "",
  });

  // Branding settings state
  const [brandingSettings, setBrandingSettings] = useState({
    primaryColor: "#3B82F6",
    secondaryColor: "#10B981",
    accentColor: "#F59E0B",
    fontFamily: "Inter",
    logoUrl: "",
    faviconUrl: "",
    customCSS: "",
    enableCustomTheme: false,
  });

  // Analytics settings state
  const [analyticsSettings, setAnalyticsSettings] = useState({
    enableGoogleAnalytics: false,
    googleAnalyticsId: "",
    enableFacebookPixel: false,
    facebookPixelId: "",
    enableHotjar: false,
    hotjarId: "",
    enableDataCollection: true,
    enablePerformanceMonitoring: true,
  });

  const handleStoreSettingChange = (field: string, value: string | boolean) => {
    setStoreSettings((prev) => ({ ...prev, [field]: value }));
  };

  const handleCommunicationSettingChange = (
    field: string,
    value: string | boolean
  ) => {
    setCommunicationSettings((prev) => ({ ...prev, [field]: value }));
  };

  const handlePaymentSettingChange = (
    field: string,
    value: string | boolean
  ) => {
    setPaymentSettings((prev) => ({ ...prev, [field]: value }));
  };

  const handleSecuritySettingChange = (
    field: string,
    value: string | boolean
  ) => {
    setSecuritySettings((prev) => ({ ...prev, [field]: value }));
  };

  const handleBrandingSettingChange = (
    field: string,
    value: string | boolean
  ) => {
    setBrandingSettings((prev) => ({ ...prev, [field]: value }));
  };

  const handleAnalyticsSettingChange = (
    field: string,
    value: string | boolean
  ) => {
    setAnalyticsSettings((prev) => ({ ...prev, [field]: value }));
  };

  const handleSaveSettings = async (section: string) => {
    setIsLoading(true);
    try {
      // TODO: Implement actual API call
      console.log(`Saving ${section} settings`);
      await new Promise((resolve) => setTimeout(resolve, 1000));
    } catch (error) {
      console.error(`Error saving ${section} settings:`, error);
    } finally {
      setIsLoading(false);
    }
  };

  const quickActions = [
    {
      label: "Backup Settings",
      icon: Download,
      action: () => console.log("Backup"),
    },
    {
      label: "Import Settings",
      icon: Upload,
      action: () => console.log("Import"),
    },
    {
      label: "Reset to Defaults",
      icon: Settings,
      action: () => console.log("Reset"),
    },
  ];

  return (
    <>
      <PageHeaderWrapper
        title="System Settings"
        description="Configure your e-commerce platform settings, integrations, and business preferences"
      >
        <div className="flex gap-2">
          {quickActions.map((action, index) => (
            <Button
              key={index}
              variant="outline"
              size="sm"
              onClick={action.action}
              className="hidden sm:flex"
            >
              <action.icon className="mr-2 h-4 w-4" />
              {action.label}
            </Button>
          ))}
          <Button
            size="sm"
            onClick={() => handleSaveSettings("all")}
            disabled={isLoading}
          >
            <Save className="mr-2 h-4 w-4" />
            {isLoading ? "Saving..." : "Save All"}
          </Button>
        </div>
      </PageHeaderWrapper>

      <div className="container mx-auto mt-6 space-y-6">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-6">
            <TabsTrigger value="store">Store Config</TabsTrigger>
            <TabsTrigger value="communications">Communications</TabsTrigger>
            <TabsTrigger value="payments">Payments</TabsTrigger>
            <TabsTrigger value="security">Security</TabsTrigger>
            <TabsTrigger value="branding">Branding</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
          </TabsList>

          {/* Store Configuration Tab */}
          <TabsContent value="store" className="space-y-6">
            <div className="grid gap-6 lg:grid-cols-2">
              {/* Basic Store Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Store className="h-5 w-5 text-blue-600" />
                    Store Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="storeName">Store Name</Label>
                    <Input
                      id="storeName"
                      value={storeSettings.storeName}
                      onChange={(e) =>
                        handleStoreSettingChange("storeName", e.target.value)
                      }
                      placeholder="Enter store name"
                    />
                  </div>

                  <div>
                    <Label htmlFor="storeDescription">Store Description</Label>
                    <Textarea
                      id="storeDescription"
                      value={storeSettings.storeDescription}
                      onChange={(e) =>
                        handleStoreSettingChange(
                          "storeDescription",
                          e.target.value
                        )
                      }
                      placeholder="Describe your store"
                      rows={3}
                    />
                  </div>

                  <div className="grid gap-4 md:grid-cols-2">
                    <div>
                      <Label htmlFor="contactEmail">Contact Email</Label>
                      <Input
                        id="contactEmail"
                        type="email"
                        value={storeSettings.contactEmail}
                        onChange={(e) =>
                          handleStoreSettingChange(
                            "contactEmail",
                            e.target.value
                          )
                        }
                        placeholder="<EMAIL>"
                      />
                    </div>
                    <div>
                      <Label htmlFor="supportEmail">Support Email</Label>
                      <Input
                        id="supportEmail"
                        type="email"
                        value={storeSettings.supportEmail}
                        onChange={(e) =>
                          handleStoreSettingChange(
                            "supportEmail",
                            e.target.value
                          )
                        }
                        placeholder="<EMAIL>"
                      />
                    </div>
                  </div>

                  <div className="grid gap-4 md:grid-cols-2">
                    <div>
                      <Label htmlFor="phone">Phone Number</Label>
                      <Input
                        id="phone"
                        type="tel"
                        value={storeSettings.phone}
                        onChange={(e) =>
                          handleStoreSettingChange("phone", e.target.value)
                        }
                        placeholder="+****************"
                      />
                    </div>
                    <div>
                      <Label htmlFor="businessHours">Business Hours</Label>
                      <Input
                        id="businessHours"
                        value={storeSettings.businessHours}
                        onChange={(e) =>
                          handleStoreSettingChange(
                            "businessHours",
                            e.target.value
                          )
                        }
                        placeholder="Mon-Fri 9AM-6PM EST"
                      />
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="address">Business Address</Label>
                    <Textarea
                      id="address"
                      value={storeSettings.address}
                      onChange={(e) =>
                        handleStoreSettingChange("address", e.target.value)
                      }
                      placeholder="123 Business St, City, State 12345"
                      rows={2}
                    />
                  </div>
                </CardContent>
              </Card>

              {/* Regional Settings */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Globe className="h-5 w-5 text-green-600" />
                    Regional Settings
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="defaultCurrency">Default Currency</Label>
                    <Select
                      value={storeSettings.defaultCurrency}
                      onValueChange={(value) =>
                        handleStoreSettingChange("defaultCurrency", value)
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="USD">USD ($)</SelectItem>
                        <SelectItem value="EUR">EUR (€)</SelectItem>
                        <SelectItem value="GBP">GBP (£)</SelectItem>
                        <SelectItem value="JPY">JPY (¥)</SelectItem>
                        <SelectItem value="CAD">CAD (C$)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="defaultLanguage">Default Language</Label>
                    <Select
                      value={storeSettings.defaultLanguage}
                      onValueChange={(value) =>
                        handleStoreSettingChange("defaultLanguage", value)
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="en">English</SelectItem>
                        <SelectItem value="es">Español</SelectItem>
                        <SelectItem value="fr">Français</SelectItem>
                        <SelectItem value="de">Deutsch</SelectItem>
                        <SelectItem value="it">Italiano</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="timezone">Timezone</Label>
                    <Select
                      value={storeSettings.timezone}
                      onValueChange={(value) =>
                        handleStoreSettingChange("timezone", value)
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="America/New_York">
                          Eastern Time (ET)
                        </SelectItem>
                        <SelectItem value="America/Chicago">
                          Central Time (CT)
                        </SelectItem>
                        <SelectItem value="America/Denver">
                          Mountain Time (MT)
                        </SelectItem>
                        <SelectItem value="America/Los_Angeles">
                          Pacific Time (PT)
                        </SelectItem>
                        <SelectItem value="Europe/London">
                          London (GMT)
                        </SelectItem>
                        <SelectItem value="Europe/Paris">
                          Paris (CET)
                        </SelectItem>
                        <SelectItem value="Asia/Tokyo">Tokyo (JST)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="border-t pt-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="font-medium">
                          Enable Tax Calculation
                        </div>
                        <div className="text-sm text-gray-500">
                          Automatically calculate taxes on orders
                        </div>
                      </div>
                      <Switch
                        checked={storeSettings.enableTax}
                        onCheckedChange={(checked) =>
                          handleStoreSettingChange("enableTax", checked)
                        }
                      />
                    </div>

                    {storeSettings.enableTax && (
                      <div className="mt-3">
                        <Label htmlFor="taxRate">Default Tax Rate (%)</Label>
                        <Input
                          id="taxRate"
                          type="number"
                          step="0.01"
                          value={storeSettings.taxRate}
                          onChange={(e) =>
                            handleStoreSettingChange("taxRate", e.target.value)
                          }
                          placeholder="8.25"
                        />
                      </div>
                    )}
                  </div>

                  <div className="flex justify-end">
                    <Button
                      onClick={() => handleSaveSettings("store")}
                      disabled={isLoading}
                    >
                      <Save className="mr-2 h-4 w-4" />
                      {isLoading ? "Saving..." : "Save Store Settings"}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Communications Tab */}
          <TabsContent value="communications" className="space-y-6">
            <div className="grid gap-6 lg:grid-cols-2">
              {/* Email Configuration */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Mail className="h-5 w-5 text-blue-600" />
                    Email Configuration
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <Alert>
                    <Mail className="h-4 w-4" />
                    <AlertDescription>
                      Configure SMTP settings to enable email notifications and
                      customer communications.
                    </AlertDescription>
                  </Alert>

                  <div className="grid gap-4 md:grid-cols-2">
                    <div>
                      <Label htmlFor="smtpHost">SMTP Host</Label>
                      <Input
                        id="smtpHost"
                        value={communicationSettings.smtpHost}
                        onChange={(e) =>
                          handleCommunicationSettingChange(
                            "smtpHost",
                            e.target.value
                          )
                        }
                        placeholder="smtp.gmail.com"
                      />
                    </div>
                    <div>
                      <Label htmlFor="smtpPort">SMTP Port</Label>
                      <Input
                        id="smtpPort"
                        type="number"
                        value={communicationSettings.smtpPort}
                        onChange={(e) =>
                          handleCommunicationSettingChange(
                            "smtpPort",
                            e.target.value
                          )
                        }
                        placeholder="587"
                      />
                    </div>
                  </div>

                  <div className="grid gap-4 md:grid-cols-2">
                    <div>
                      <Label htmlFor="smtpUsername">SMTP Username</Label>
                      <Input
                        id="smtpUsername"
                        value={communicationSettings.smtpUsername}
                        onChange={(e) =>
                          handleCommunicationSettingChange(
                            "smtpUsername",
                            e.target.value
                          )
                        }
                        placeholder="<EMAIL>"
                      />
                    </div>
                    <div>
                      <Label htmlFor="smtpPassword">SMTP Password</Label>
                      <Input
                        id="smtpPassword"
                        type="password"
                        value={communicationSettings.smtpPassword}
                        onChange={(e) =>
                          handleCommunicationSettingChange(
                            "smtpPassword",
                            e.target.value
                          )
                        }
                        placeholder="••••••••"
                      />
                    </div>
                  </div>

                  <div className="grid gap-4 md:grid-cols-2">
                    <div>
                      <Label htmlFor="fromEmail">From Email</Label>
                      <Input
                        id="fromEmail"
                        type="email"
                        value={communicationSettings.fromEmail}
                        onChange={(e) =>
                          handleCommunicationSettingChange(
                            "fromEmail",
                            e.target.value
                          )
                        }
                        placeholder="<EMAIL>"
                      />
                    </div>
                    <div>
                      <Label htmlFor="fromName">From Name</Label>
                      <Input
                        id="fromName"
                        value={communicationSettings.fromName}
                        onChange={(e) =>
                          handleCommunicationSettingChange(
                            "fromName",
                            e.target.value
                          )
                        }
                        placeholder="E-Commerce Store"
                      />
                    </div>
                  </div>

                  <div className="border-t pt-4">
                    <Button variant="outline" size="sm" className="w-full">
                      <Mail className="mr-2 h-4 w-4" />
                      Test Email Configuration
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* Notification Settings */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Settings className="h-5 w-5 text-green-600" />
                    Notification Settings
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">Email Notifications</div>
                      <div className="text-sm text-gray-500">
                        Enable email notifications for orders and updates
                      </div>
                    </div>
                    <Switch
                      checked={communicationSettings.enableEmailNotifications}
                      onCheckedChange={(checked) =>
                        handleCommunicationSettingChange(
                          "enableEmailNotifications",
                          checked
                        )
                      }
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">SMS Notifications</div>
                      <div className="text-sm text-gray-500">
                        Send SMS updates for critical events
                      </div>
                    </div>
                    <Switch
                      checked={communicationSettings.enableSMSNotifications}
                      onCheckedChange={(checked) =>
                        handleCommunicationSettingChange(
                          "enableSMSNotifications",
                          checked
                        )
                      }
                    />
                  </div>

                  <div className="space-y-3 border-t pt-4">
                    <div className="text-sm font-medium text-gray-700">
                      Email Templates
                    </div>

                    <div>
                      <Label htmlFor="orderConfirmationTemplate">
                        Order Confirmation Template
                      </Label>
                      <Select
                        value={communicationSettings.orderConfirmationTemplate}
                        onValueChange={(value) =>
                          handleCommunicationSettingChange(
                            "orderConfirmationTemplate",
                            value
                          )
                        }
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="default">
                            Default Template
                          </SelectItem>
                          <SelectItem value="modern">
                            Modern Template
                          </SelectItem>
                          <SelectItem value="minimal">
                            Minimal Template
                          </SelectItem>
                          <SelectItem value="custom">
                            Custom Template
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="shippingNotificationTemplate">
                        Shipping Notification Template
                      </Label>
                      <Select
                        value={
                          communicationSettings.shippingNotificationTemplate
                        }
                        onValueChange={(value) =>
                          handleCommunicationSettingChange(
                            "shippingNotificationTemplate",
                            value
                          )
                        }
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="default">
                            Default Template
                          </SelectItem>
                          <SelectItem value="modern">
                            Modern Template
                          </SelectItem>
                          <SelectItem value="minimal">
                            Minimal Template
                          </SelectItem>
                          <SelectItem value="custom">
                            Custom Template
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="flex justify-end">
                    <Button
                      onClick={() => handleSaveSettings("communications")}
                      disabled={isLoading}
                    >
                      <Save className="mr-2 h-4 w-4" />
                      {isLoading ? "Saving..." : "Save Communication Settings"}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Payments Tab */}
          <TabsContent value="payments" className="space-y-6">
            <div className="grid gap-6 lg:grid-cols-2">
              {/* Payment Gateways */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <CreditCard className="h-5 w-5 text-blue-600" />
                    Payment Gateways
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Stripe */}
                  <div className="rounded-lg border p-4">
                    <div className="mb-3 flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="flex h-8 w-8 items-center justify-center rounded bg-blue-100">
                          <CreditCard className="h-4 w-4 text-blue-600" />
                        </div>
                        <div>
                          <div className="font-medium">Stripe</div>
                          <div className="text-sm text-gray-500">
                            Credit cards, digital wallets
                          </div>
                        </div>
                      </div>
                      <Switch
                        checked={paymentSettings.enableStripe}
                        onCheckedChange={(checked) =>
                          handlePaymentSettingChange("enableStripe", checked)
                        }
                      />
                    </div>

                    {paymentSettings.enableStripe && (
                      <div className="space-y-3">
                        <div>
                          <Label htmlFor="stripePublishableKey">
                            Publishable Key
                          </Label>
                          <Input
                            id="stripePublishableKey"
                            value={paymentSettings.stripePublishableKey}
                            onChange={(e) =>
                              handlePaymentSettingChange(
                                "stripePublishableKey",
                                e.target.value
                              )
                            }
                            placeholder="pk_test_..."
                          />
                        </div>
                        <div>
                          <Label htmlFor="stripeSecretKey">Secret Key</Label>
                          <Input
                            id="stripeSecretKey"
                            type="password"
                            value={paymentSettings.stripeSecretKey}
                            onChange={(e) =>
                              handlePaymentSettingChange(
                                "stripeSecretKey",
                                e.target.value
                              )
                            }
                            placeholder="sk_test_..."
                          />
                        </div>
                      </div>
                    )}
                  </div>

                  {/* PayPal */}
                  <div className="rounded-lg border p-4">
                    <div className="mb-3 flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="flex h-8 w-8 items-center justify-center rounded bg-yellow-100">
                          <CreditCard className="h-4 w-4 text-yellow-600" />
                        </div>
                        <div>
                          <div className="font-medium">PayPal</div>
                          <div className="text-sm text-gray-500">
                            PayPal payments
                          </div>
                        </div>
                      </div>
                      <Switch
                        checked={paymentSettings.enablePayPal}
                        onCheckedChange={(checked) =>
                          handlePaymentSettingChange("enablePayPal", checked)
                        }
                      />
                    </div>

                    {paymentSettings.enablePayPal && (
                      <div className="space-y-3">
                        <div>
                          <Label htmlFor="paypalClientId">Client ID</Label>
                          <Input
                            id="paypalClientId"
                            value={paymentSettings.paypalClientId}
                            onChange={(e) =>
                              handlePaymentSettingChange(
                                "paypalClientId",
                                e.target.value
                              )
                            }
                            placeholder="PayPal Client ID"
                          />
                        </div>
                        <div>
                          <Label htmlFor="paypalClientSecret">
                            Client Secret
                          </Label>
                          <Input
                            id="paypalClientSecret"
                            type="password"
                            value={paymentSettings.paypalClientSecret}
                            onChange={(e) =>
                              handlePaymentSettingChange(
                                "paypalClientSecret",
                                e.target.value
                              )
                            }
                            placeholder="PayPal Client Secret"
                          />
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Other Payment Methods */}
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="font-medium">Cash on Delivery</div>
                        <div className="text-sm text-gray-500">
                          Accept cash payments on delivery
                        </div>
                      </div>
                      <Switch
                        checked={paymentSettings.enableCOD}
                        onCheckedChange={(checked) =>
                          handlePaymentSettingChange("enableCOD", checked)
                        }
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <div className="font-medium">Bank Transfer</div>
                        <div className="text-sm text-gray-500">
                          Direct bank transfers
                        </div>
                      </div>
                      <Switch
                        checked={paymentSettings.enableBankTransfer}
                        onCheckedChange={(checked) =>
                          handlePaymentSettingChange(
                            "enableBankTransfer",
                            checked
                          )
                        }
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Order Settings */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Settings className="h-5 w-5 text-green-600" />
                    Order Settings
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="minimumOrderAmount">
                      Minimum Order Amount
                    </Label>
                    <Input
                      id="minimumOrderAmount"
                      type="number"
                      step="0.01"
                      value={paymentSettings.minimumOrderAmount}
                      onChange={(e) =>
                        handlePaymentSettingChange(
                          "minimumOrderAmount",
                          e.target.value
                        )
                      }
                      placeholder="10.00"
                    />
                    <p className="mt-1 text-xs text-gray-500">
                      Minimum amount required to place an order
                    </p>
                  </div>

                  <div>
                    <Label htmlFor="freeShippingThreshold">
                      Free Shipping Threshold
                    </Label>
                    <Input
                      id="freeShippingThreshold"
                      type="number"
                      step="0.01"
                      value={paymentSettings.freeShippingThreshold}
                      onChange={(e) =>
                        handlePaymentSettingChange(
                          "freeShippingThreshold",
                          e.target.value
                        )
                      }
                      placeholder="50.00"
                    />
                    <p className="mt-1 text-xs text-gray-500">
                      Order amount for free shipping eligibility
                    </p>
                  </div>

                  <div className="border-t pt-4">
                    <div className="mb-3 text-sm font-medium text-gray-700">
                      Payment Security
                    </div>
                    <div className="space-y-2 text-sm text-gray-600">
                      <div className="flex items-center gap-2">
                        <div className="h-2 w-2 rounded-full bg-green-500"></div>
                        <span>SSL encryption enabled</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="h-2 w-2 rounded-full bg-green-500"></div>
                        <span>PCI DSS compliant</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="h-2 w-2 rounded-full bg-green-500"></div>
                        <span>Fraud detection active</span>
                      </div>
                    </div>
                  </div>

                  <div className="flex justify-end">
                    <Button
                      onClick={() => handleSaveSettings("payments")}
                      disabled={isLoading}
                    >
                      <Save className="mr-2 h-4 w-4" />
                      {isLoading ? "Saving..." : "Save Payment Settings"}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Security Tab */}
          <TabsContent value="security" className="space-y-6">
            <div className="grid gap-6 lg:grid-cols-2">
              {/* Security Settings */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Shield className="h-5 w-5 text-red-600" />
                    Security Settings
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">
                        Two-Factor Authentication
                      </div>
                      <div className="text-sm text-gray-500">
                        Require 2FA for admin accounts
                      </div>
                    </div>
                    <Switch
                      checked={securitySettings.enableTwoFactor}
                      onCheckedChange={(checked) =>
                        handleSecuritySettingChange("enableTwoFactor", checked)
                      }
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">CAPTCHA Protection</div>
                      <div className="text-sm text-gray-500">
                        Enable CAPTCHA on login forms
                      </div>
                    </div>
                    <Switch
                      checked={securitySettings.enableCaptcha}
                      onCheckedChange={(checked) =>
                        handleSecuritySettingChange("enableCaptcha", checked)
                      }
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">SSL Enforcement</div>
                      <div className="text-sm text-gray-500">
                        Force HTTPS connections
                      </div>
                    </div>
                    <Switch
                      checked={securitySettings.enableSSL}
                      onCheckedChange={(checked) =>
                        handleSecuritySettingChange("enableSSL", checked)
                      }
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">
                        Web Application Firewall
                      </div>
                      <div className="text-sm text-gray-500">
                        Advanced threat protection
                      </div>
                    </div>
                    <Switch
                      checked={securitySettings.enableFirewall}
                      onCheckedChange={(checked) =>
                        handleSecuritySettingChange("enableFirewall", checked)
                      }
                    />
                  </div>

                  <div>
                    <Label htmlFor="sessionTimeout">
                      Session Timeout (minutes)
                    </Label>
                    <Select
                      value={securitySettings.sessionTimeout}
                      onValueChange={(value) =>
                        handleSecuritySettingChange("sessionTimeout", value)
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="15">15 minutes</SelectItem>
                        <SelectItem value="30">30 minutes</SelectItem>
                        <SelectItem value="60">1 hour</SelectItem>
                        <SelectItem value="120">2 hours</SelectItem>
                        <SelectItem value="240">4 hours</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="maxLoginAttempts">Max Login Attempts</Label>
                    <Select
                      value={securitySettings.maxLoginAttempts}
                      onValueChange={(value) =>
                        handleSecuritySettingChange("maxLoginAttempts", value)
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="3">3 attempts</SelectItem>
                        <SelectItem value="5">5 attempts</SelectItem>
                        <SelectItem value="10">10 attempts</SelectItem>
                        <SelectItem value="unlimited">Unlimited</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </CardContent>
              </Card>

              {/* Access Control */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Key className="h-5 w-5 text-orange-600" />
                    Access Control
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="allowedIPs">Allowed IP Addresses</Label>
                    <Textarea
                      id="allowedIPs"
                      value={securitySettings.allowedIPs}
                      onChange={(e) =>
                        handleSecuritySettingChange(
                          "allowedIPs",
                          e.target.value
                        )
                      }
                      placeholder="***********&#10;********&#10;Leave empty to allow all"
                      rows={4}
                    />
                    <p className="mt-1 text-xs text-gray-500">
                      One IP address per line. Leave empty to allow all IPs.
                    </p>
                  </div>

                  <div>
                    <Label htmlFor="blockedIPs">Blocked IP Addresses</Label>
                    <Textarea
                      id="blockedIPs"
                      value={securitySettings.blockedIPs}
                      onChange={(e) =>
                        handleSecuritySettingChange(
                          "blockedIPs",
                          e.target.value
                        )
                      }
                      placeholder="*************&#10;********00"
                      rows={4}
                    />
                    <p className="mt-1 text-xs text-gray-500">
                      One IP address per line.
                    </p>
                  </div>

                  <div className="flex justify-end">
                    <Button
                      onClick={() => handleSaveSettings("security")}
                      disabled={isLoading}
                    >
                      <Save className="mr-2 h-4 w-4" />
                      {isLoading ? "Saving..." : "Save Security Settings"}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Branding Tab */}
          <TabsContent value="branding" className="space-y-6">
            <div className="grid gap-6 lg:grid-cols-2">
              {/* Brand Colors */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Palette className="h-5 w-5 text-purple-600" />
                    Brand Colors
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="primaryColor">Primary Color</Label>
                    <div className="flex gap-2">
                      <Input
                        id="primaryColor"
                        value={brandingSettings.primaryColor}
                        onChange={(e) =>
                          handleBrandingSettingChange(
                            "primaryColor",
                            e.target.value
                          )
                        }
                        placeholder="#3B82F6"
                      />
                      <div
                        className="h-10 w-10 rounded border"
                        style={{
                          backgroundColor: brandingSettings.primaryColor,
                        }}
                      ></div>
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="secondaryColor">Secondary Color</Label>
                    <div className="flex gap-2">
                      <Input
                        id="secondaryColor"
                        value={brandingSettings.secondaryColor}
                        onChange={(e) =>
                          handleBrandingSettingChange(
                            "secondaryColor",
                            e.target.value
                          )
                        }
                        placeholder="#10B981"
                      />
                      <div
                        className="h-10 w-10 rounded border"
                        style={{
                          backgroundColor: brandingSettings.secondaryColor,
                        }}
                      ></div>
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="accentColor">Accent Color</Label>
                    <div className="flex gap-2">
                      <Input
                        id="accentColor"
                        value={brandingSettings.accentColor}
                        onChange={(e) =>
                          handleBrandingSettingChange(
                            "accentColor",
                            e.target.value
                          )
                        }
                        placeholder="#F59E0B"
                      />
                      <div
                        className="h-10 w-10 rounded border"
                        style={{
                          backgroundColor: brandingSettings.accentColor,
                        }}
                      ></div>
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="fontFamily">Font Family</Label>
                    <Select
                      value={brandingSettings.fontFamily}
                      onValueChange={(value) =>
                        handleBrandingSettingChange("fontFamily", value)
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Inter">Inter</SelectItem>
                        <SelectItem value="Roboto">Roboto</SelectItem>
                        <SelectItem value="Open Sans">Open Sans</SelectItem>
                        <SelectItem value="Lato">Lato</SelectItem>
                        <SelectItem value="Poppins">Poppins</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </CardContent>
              </Card>

              {/* Brand Assets */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Upload className="h-5 w-5 text-green-600" />
                    Brand Assets
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="logoUrl">Logo URL</Label>
                    <Input
                      id="logoUrl"
                      value={brandingSettings.logoUrl}
                      onChange={(e) =>
                        handleBrandingSettingChange("logoUrl", e.target.value)
                      }
                      placeholder="https://example.com/logo.png"
                    />
                    <Button variant="outline" size="sm" className="mt-2 w-full">
                      <Upload className="mr-2 h-4 w-4" />
                      Upload Logo
                    </Button>
                  </div>

                  <div>
                    <Label htmlFor="faviconUrl">Favicon URL</Label>
                    <Input
                      id="faviconUrl"
                      value={brandingSettings.faviconUrl}
                      onChange={(e) =>
                        handleBrandingSettingChange(
                          "faviconUrl",
                          e.target.value
                        )
                      }
                      placeholder="https://example.com/favicon.ico"
                    />
                    <Button variant="outline" size="sm" className="mt-2 w-full">
                      <Upload className="mr-2 h-4 w-4" />
                      Upload Favicon
                    </Button>
                  </div>

                  <div>
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="font-medium">Enable Custom Theme</div>
                        <div className="text-sm text-gray-500">
                          Apply custom branding to storefront
                        </div>
                      </div>
                      <Switch
                        checked={brandingSettings.enableCustomTheme}
                        onCheckedChange={(checked) =>
                          handleBrandingSettingChange(
                            "enableCustomTheme",
                            checked
                          )
                        }
                      />
                    </div>
                  </div>

                  {brandingSettings.enableCustomTheme && (
                    <div>
                      <Label htmlFor="customCSS">Custom CSS</Label>
                      <Textarea
                        id="customCSS"
                        value={brandingSettings.customCSS}
                        onChange={(e) =>
                          handleBrandingSettingChange(
                            "customCSS",
                            e.target.value
                          )
                        }
                        placeholder="/* Add your custom CSS here */"
                        rows={6}
                      />
                    </div>
                  )}

                  <div className="flex justify-end">
                    <Button
                      onClick={() => handleSaveSettings("branding")}
                      disabled={isLoading}
                    >
                      <Save className="mr-2 h-4 w-4" />
                      {isLoading ? "Saving..." : "Save Branding Settings"}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Analytics Tab */}
          <TabsContent value="analytics" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5 text-blue-600" />
                  Analytics & Tracking
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid gap-6 lg:grid-cols-2">
                  {/* Google Analytics */}
                  <div className="rounded-lg border p-4">
                    <div className="mb-3 flex items-center justify-between">
                      <div>
                        <div className="font-medium">Google Analytics</div>
                        <div className="text-sm text-gray-500">
                          Track website traffic and user behavior
                        </div>
                      </div>
                      <Switch
                        checked={analyticsSettings.enableGoogleAnalytics}
                        onCheckedChange={(checked) =>
                          handleAnalyticsSettingChange(
                            "enableGoogleAnalytics",
                            checked
                          )
                        }
                      />
                    </div>

                    {analyticsSettings.enableGoogleAnalytics && (
                      <div>
                        <Label htmlFor="googleAnalyticsId">
                          Google Analytics ID
                        </Label>
                        <Input
                          id="googleAnalyticsId"
                          value={analyticsSettings.googleAnalyticsId}
                          onChange={(e) =>
                            handleAnalyticsSettingChange(
                              "googleAnalyticsId",
                              e.target.value
                            )
                          }
                          placeholder="G-XXXXXXXXXX"
                        />
                      </div>
                    )}
                  </div>

                  {/* Facebook Pixel */}
                  <div className="rounded-lg border p-4">
                    <div className="mb-3 flex items-center justify-between">
                      <div>
                        <div className="font-medium">Facebook Pixel</div>
                        <div className="text-sm text-gray-500">
                          Track conversions and optimize ads
                        </div>
                      </div>
                      <Switch
                        checked={analyticsSettings.enableFacebookPixel}
                        onCheckedChange={(checked) =>
                          handleAnalyticsSettingChange(
                            "enableFacebookPixel",
                            checked
                          )
                        }
                      />
                    </div>

                    {analyticsSettings.enableFacebookPixel && (
                      <div>
                        <Label htmlFor="facebookPixelId">
                          Facebook Pixel ID
                        </Label>
                        <Input
                          id="facebookPixelId"
                          value={analyticsSettings.facebookPixelId}
                          onChange={(e) =>
                            handleAnalyticsSettingChange(
                              "facebookPixelId",
                              e.target.value
                            )
                          }
                          placeholder="123456789012345"
                        />
                      </div>
                    )}
                  </div>

                  {/* Hotjar */}
                  <div className="rounded-lg border p-4">
                    <div className="mb-3 flex items-center justify-between">
                      <div>
                        <div className="font-medium">Hotjar</div>
                        <div className="text-sm text-gray-500">
                          Heatmaps and user session recordings
                        </div>
                      </div>
                      <Switch
                        checked={analyticsSettings.enableHotjar}
                        onCheckedChange={(checked) =>
                          handleAnalyticsSettingChange("enableHotjar", checked)
                        }
                      />
                    </div>

                    {analyticsSettings.enableHotjar && (
                      <div>
                        <Label htmlFor="hotjarId">Hotjar Site ID</Label>
                        <Input
                          id="hotjarId"
                          value={analyticsSettings.hotjarId}
                          onChange={(e) =>
                            handleAnalyticsSettingChange(
                              "hotjarId",
                              e.target.value
                            )
                          }
                          placeholder="1234567"
                        />
                      </div>
                    )}
                  </div>

                  {/* Privacy Settings */}
                  <div className="rounded-lg border p-4">
                    <div className="mb-3 font-medium">
                      Privacy & Data Collection
                    </div>

                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="font-medium">Data Collection</div>
                          <div className="text-sm text-gray-500">
                            Collect user analytics data
                          </div>
                        </div>
                        <Switch
                          checked={analyticsSettings.enableDataCollection}
                          onCheckedChange={(checked) =>
                            handleAnalyticsSettingChange(
                              "enableDataCollection",
                              checked
                            )
                          }
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <div>
                          <div className="font-medium">
                            Performance Monitoring
                          </div>
                          <div className="text-sm text-gray-500">
                            Monitor site performance metrics
                          </div>
                        </div>
                        <Switch
                          checked={
                            analyticsSettings.enablePerformanceMonitoring
                          }
                          onCheckedChange={(checked) =>
                            handleAnalyticsSettingChange(
                              "enablePerformanceMonitoring",
                              checked
                            )
                          }
                        />
                      </div>
                    </div>
                  </div>
                </div>

                <div className="flex justify-end">
                  <Button
                    onClick={() => handleSaveSettings("analytics")}
                    disabled={isLoading}
                  >
                    <Save className="mr-2 h-4 w-4" />
                    {isLoading ? "Saving..." : "Save Analytics Settings"}
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </>
  );
}
