import Image from "next/image";

type OrderProductRowProps = {
  imageSrc: string;
  name: string;
  quantity: number;
  unitPrice: string;
  total: string;
};

export const OrderProductRow = ({
  imageSrc,
  name,
  quantity,
  unitPrice,
  total,
}: OrderProductRowProps) => {
  return (
    <tr className="border-t">
      <td className="p-4">
        <div className="flex items-center gap-3">
          <div className="flex size-16 shrink-0 overflow-hidden rounded-lg bg-gray-100">
            <Image
              src={imageSrc}
              alt={name}
              width={64}
              height={64}
              className="object-cover"
              quality={85}
              sizes="(max-width: 768px) 100vw, 4rem"
            />
          </div>

          <span className="font-medium text-gray-800">{name}</span>
        </div>
      </td>

      <td className="p-4 text-center">{quantity}</td>
      <td className="p-4 text-center">{unitPrice}</td>
      <td className="p-4 text-right font-medium">{total}</td>
    </tr>
  );
};
