hoistPattern:
  - '*'
hoistedDependencies:
  '@apidevtools/json-schema-ref-parser@9.1.2':
    '@apidevtools/json-schema-ref-parser': private
  '@apidevtools/openapi-schemas@2.1.0':
    '@apidevtools/openapi-schemas': private
  '@apidevtools/swagger-methods@3.0.2':
    '@apidevtools/swagger-methods': private
  '@apidevtools/swagger-parser@10.0.3(openapi-types@12.1.3)':
    '@apidevtools/swagger-parser': private
  '@babel/code-frame@7.26.2':
    '@babel/code-frame': private
  '@babel/generator@7.26.10':
    '@babel/generator': private
  '@babel/helper-string-parser@7.25.9':
    '@babel/helper-string-parser': private
  '@babel/helper-validator-identifier@7.25.9':
    '@babel/helper-validator-identifier': private
  '@babel/parser@7.26.10':
    '@babel/parser': private
  '@babel/template@7.26.9':
    '@babel/template': private
  '@babel/traverse@7.26.10':
    '@babel/traverse': private
  '@babel/types@7.26.10':
    '@babel/types': private
  '@esbuild/aix-ppc64@0.25.1':
    '@esbuild/aix-ppc64': private
  '@esbuild/android-arm64@0.25.1':
    '@esbuild/android-arm64': private
  '@esbuild/android-arm@0.25.1':
    '@esbuild/android-arm': private
  '@esbuild/android-x64@0.25.1':
    '@esbuild/android-x64': private
  '@esbuild/darwin-arm64@0.25.1':
    '@esbuild/darwin-arm64': private
  '@esbuild/darwin-x64@0.25.1':
    '@esbuild/darwin-x64': private
  '@esbuild/freebsd-arm64@0.25.1':
    '@esbuild/freebsd-arm64': private
  '@esbuild/freebsd-x64@0.25.1':
    '@esbuild/freebsd-x64': private
  '@esbuild/linux-arm64@0.25.1':
    '@esbuild/linux-arm64': private
  '@esbuild/linux-arm@0.25.1':
    '@esbuild/linux-arm': private
  '@esbuild/linux-ia32@0.25.1':
    '@esbuild/linux-ia32': private
  '@esbuild/linux-loong64@0.25.1':
    '@esbuild/linux-loong64': private
  '@esbuild/linux-mips64el@0.25.1':
    '@esbuild/linux-mips64el': private
  '@esbuild/linux-ppc64@0.25.1':
    '@esbuild/linux-ppc64': private
  '@esbuild/linux-riscv64@0.25.1':
    '@esbuild/linux-riscv64': private
  '@esbuild/linux-s390x@0.25.1':
    '@esbuild/linux-s390x': private
  '@esbuild/linux-x64@0.25.1':
    '@esbuild/linux-x64': private
  '@esbuild/netbsd-arm64@0.25.1':
    '@esbuild/netbsd-arm64': private
  '@esbuild/netbsd-x64@0.25.1':
    '@esbuild/netbsd-x64': private
  '@esbuild/openbsd-arm64@0.25.1':
    '@esbuild/openbsd-arm64': private
  '@esbuild/openbsd-x64@0.25.1':
    '@esbuild/openbsd-x64': private
  '@esbuild/sunos-x64@0.25.1':
    '@esbuild/sunos-x64': private
  '@esbuild/win32-arm64@0.25.1':
    '@esbuild/win32-arm64': private
  '@esbuild/win32-ia32@0.25.1':
    '@esbuild/win32-ia32': private
  '@esbuild/win32-x64@0.25.1':
    '@esbuild/win32-x64': private
  '@eslint-community/eslint-utils@4.5.0(eslint@9.22.0)':
    '@eslint-community/eslint-utils': public
  '@eslint-community/regexpp@4.12.1':
    '@eslint-community/regexpp': public
  '@eslint/config-array@0.19.2':
    '@eslint/config-array': public
  '@eslint/config-helpers@0.1.0':
    '@eslint/config-helpers': public
  '@eslint/core@0.12.0':
    '@eslint/core': public
  '@eslint/eslintrc@3.3.0':
    '@eslint/eslintrc': public
  '@eslint/object-schema@2.1.6':
    '@eslint/object-schema': public
  '@eslint/plugin-kit@0.2.7':
    '@eslint/plugin-kit': public
  '@humanfs/core@0.19.1':
    '@humanfs/core': private
  '@humanfs/node@0.16.6':
    '@humanfs/node': private
  '@humanwhocodes/module-importer@1.0.1':
    '@humanwhocodes/module-importer': private
  '@humanwhocodes/retry@0.4.2':
    '@humanwhocodes/retry': private
  '@jridgewell/gen-mapping@0.3.8':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/set-array@1.2.1':
    '@jridgewell/set-array': private
  '@jridgewell/sourcemap-codec@1.5.0':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.25':
    '@jridgewell/trace-mapping': private
  '@jsdevtools/ono@7.1.3':
    '@jsdevtools/ono': private
  '@mongodb-js/saslprep@1.2.0':
    '@mongodb-js/saslprep': private
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': private
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': private
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': private
  '@pkgr/core@0.1.1':
    '@pkgr/core': private
  '@scarf/scarf@1.4.0':
    '@scarf/scarf': private
  '@types/body-parser@1.19.5':
    '@types/body-parser': private
  '@types/connect@3.4.38':
    '@types/connect': private
  '@types/estree@1.0.6':
    '@types/estree': private
  '@types/express-serve-static-core@5.0.6':
    '@types/express-serve-static-core': private
  '@types/http-errors@2.0.4':
    '@types/http-errors': private
  '@types/json-schema@7.0.15':
    '@types/json-schema': private
  '@types/mime@1.3.5':
    '@types/mime': private
  '@types/qs@6.9.18':
    '@types/qs': private
  '@types/range-parser@1.2.7':
    '@types/range-parser': private
  '@types/send@0.17.4':
    '@types/send': private
  '@types/serve-static@1.15.7':
    '@types/serve-static': private
  '@types/webidl-conversions@7.0.3':
    '@types/webidl-conversions': private
  '@types/whatwg-url@11.0.5':
    '@types/whatwg-url': private
  '@typescript-eslint/eslint-plugin@8.26.1(@typescript-eslint/parser@8.26.1(eslint@9.22.0)(typescript@5.8.2))(eslint@9.22.0)(typescript@5.8.2)':
    '@typescript-eslint/eslint-plugin': public
  '@typescript-eslint/parser@8.26.1(eslint@9.22.0)(typescript@5.8.2)':
    '@typescript-eslint/parser': public
  '@typescript-eslint/scope-manager@8.26.1':
    '@typescript-eslint/scope-manager': public
  '@typescript-eslint/type-utils@8.26.1(eslint@9.22.0)(typescript@5.8.2)':
    '@typescript-eslint/type-utils': public
  '@typescript-eslint/types@8.26.1':
    '@typescript-eslint/types': public
  '@typescript-eslint/typescript-estree@8.26.1(typescript@5.8.2)':
    '@typescript-eslint/typescript-estree': public
  '@typescript-eslint/utils@8.26.1(eslint@9.22.0)(typescript@5.8.2)':
    '@typescript-eslint/utils': public
  '@typescript-eslint/visitor-keys@8.26.1':
    '@typescript-eslint/visitor-keys': public
  accepts@1.3.8:
    accepts: private
  acorn-jsx@5.3.2(acorn@8.14.1):
    acorn-jsx: private
  acorn@8.14.1:
    acorn: private
  ajv@6.12.6:
    ajv: private
  ansi-styles@4.3.0:
    ansi-styles: private
  argparse@2.0.1:
    argparse: private
  array-flatten@1.1.1:
    array-flatten: private
  balanced-match@1.0.2:
    balanced-match: private
  body-parser@1.20.3:
    body-parser: private
  brace-expansion@1.1.11:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  bson@6.10.3:
    bson: private
  bytes@3.1.2:
    bytes: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  call-bound@1.0.4:
    call-bound: private
  call-me-maybe@1.0.2:
    call-me-maybe: private
  callsites@3.1.0:
    callsites: private
  chalk@4.1.2:
    chalk: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  commander@6.2.0:
    commander: private
  concat-map@0.0.1:
    concat-map: private
  content-disposition@0.5.4:
    content-disposition: private
  content-type@1.0.5:
    content-type: private
  cookie-signature@1.0.6:
    cookie-signature: private
  cookie@0.7.1:
    cookie: private
  cross-spawn@7.0.6:
    cross-spawn: private
  debug@4.4.0:
    debug: private
  deep-is@0.1.4:
    deep-is: private
  depd@2.0.0:
    depd: private
  destroy@1.2.0:
    destroy: private
  doctrine@3.0.0:
    doctrine: private
  dunder-proto@1.0.1:
    dunder-proto: private
  ee-first@1.1.1:
    ee-first: private
  encodeurl@2.0.0:
    encodeurl: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  esbuild@0.25.1:
    esbuild: private
  escape-html@1.0.3:
    escape-html: private
  escape-string-regexp@4.0.0:
    escape-string-regexp: private
  eslint-scope@8.3.0:
    eslint-scope: public
  eslint-visitor-keys@4.2.0:
    eslint-visitor-keys: public
  espree@10.3.0:
    espree: private
  esquery@1.6.0:
    esquery: private
  esrecurse@4.3.0:
    esrecurse: private
  estraverse@5.3.0:
    estraverse: private
  esutils@2.0.3:
    esutils: private
  etag@1.8.1:
    etag: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-diff@1.3.0:
    fast-diff: private
  fast-glob@3.3.3:
    fast-glob: private
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: private
  fast-levenshtein@2.0.6:
    fast-levenshtein: private
  fastq@1.19.1:
    fastq: private
  file-entry-cache@8.0.0:
    file-entry-cache: private
  fill-range@7.1.1:
    fill-range: private
  finalhandler@1.3.1:
    finalhandler: private
  find-up@5.0.0:
    find-up: private
  flat-cache@4.0.1:
    flat-cache: private
  flatted@3.3.3:
    flatted: private
  forwarded@0.2.0:
    forwarded: private
  fresh@0.5.2:
    fresh: private
  fs.realpath@1.0.0:
    fs.realpath: private
  fsevents@2.3.3:
    fsevents: private
  function-bind@1.1.2:
    function-bind: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-proto@1.0.1:
    get-proto: private
  get-tsconfig@4.10.0:
    get-tsconfig: private
  glob-parent@6.0.2:
    glob-parent: private
  glob@7.1.6:
    glob: private
  gopd@1.2.0:
    gopd: private
  graphemer@1.4.0:
    graphemer: private
  has-flag@4.0.0:
    has-flag: private
  has-symbols@1.1.0:
    has-symbols: private
  hasown@2.0.2:
    hasown: private
  http-errors@2.0.0:
    http-errors: private
  iconv-lite@0.4.24:
    iconv-lite: private
  ignore@5.3.2:
    ignore: private
  import-fresh@3.3.1:
    import-fresh: private
  imurmurhash@0.1.4:
    imurmurhash: private
  inflight@1.0.6:
    inflight: private
  inherits@2.0.4:
    inherits: private
  ipaddr.js@1.9.1:
    ipaddr.js: private
  is-extglob@2.1.1:
    is-extglob: private
  is-glob@4.0.3:
    is-glob: private
  is-number@7.0.0:
    is-number: private
  isexe@2.0.0:
    isexe: private
  javascript-natural-sort@0.7.1:
    javascript-natural-sort: private
  js-tokens@4.0.0:
    js-tokens: private
  js-yaml@4.1.0:
    js-yaml: private
  jsesc@3.1.0:
    jsesc: private
  json-buffer@3.0.1:
    json-buffer: private
  json-schema-traverse@0.4.1:
    json-schema-traverse: private
  json-stable-stringify-without-jsonify@1.0.1:
    json-stable-stringify-without-jsonify: private
  kareem@2.6.3:
    kareem: private
  keyv@4.5.4:
    keyv: private
  levn@0.4.1:
    levn: private
  locate-path@6.0.0:
    locate-path: private
  lodash.get@4.4.2:
    lodash.get: private
  lodash.isequal@4.5.0:
    lodash.isequal: private
  lodash.merge@4.6.2:
    lodash.merge: private
  lodash.mergewith@4.6.2:
    lodash.mergewith: private
  lodash@4.17.21:
    lodash: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  media-typer@0.3.0:
    media-typer: private
  memory-pager@1.5.0:
    memory-pager: private
  merge-descriptors@1.0.3:
    merge-descriptors: private
  merge2@1.4.1:
    merge2: private
  methods@1.1.2:
    methods: private
  micromatch@4.0.8:
    micromatch: private
  mime-db@1.52.0:
    mime-db: private
  mime-types@2.1.35:
    mime-types: private
  mime@1.6.0:
    mime: private
  minimatch@3.1.2:
    minimatch: private
  mongodb-connection-string-url@3.0.2:
    mongodb-connection-string-url: private
  mongodb@6.14.2:
    mongodb: private
  mpath@0.9.0:
    mpath: private
  mquery@5.0.0:
    mquery: private
  ms@2.1.3:
    ms: private
  natural-compare@1.4.0:
    natural-compare: private
  negotiator@0.6.3:
    negotiator: private
  object-assign@4.1.1:
    object-assign: private
  object-inspect@1.13.4:
    object-inspect: private
  on-finished@2.4.1:
    on-finished: private
  once@1.4.0:
    once: private
  openapi-types@12.1.3:
    openapi-types: private
  optionator@0.9.4:
    optionator: private
  p-limit@3.1.0:
    p-limit: private
  p-locate@5.0.0:
    p-locate: private
  parent-module@1.0.1:
    parent-module: private
  parseurl@1.3.3:
    parseurl: private
  path-exists@4.0.0:
    path-exists: private
  path-is-absolute@1.0.1:
    path-is-absolute: private
  path-key@3.1.1:
    path-key: private
  path-to-regexp@0.1.12:
    path-to-regexp: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@2.3.1:
    picomatch: private
  prelude-ls@1.2.1:
    prelude-ls: private
  prettier-linter-helpers@1.0.0:
    prettier-linter-helpers: public
  proxy-addr@2.0.7:
    proxy-addr: private
  punycode@2.3.1:
    punycode: private
  qs@6.13.0:
    qs: private
  queue-microtask@1.2.3:
    queue-microtask: private
  range-parser@1.2.1:
    range-parser: private
  raw-body@2.5.2:
    raw-body: private
  resolve-from@4.0.0:
    resolve-from: private
  resolve-pkg-maps@1.0.0:
    resolve-pkg-maps: private
  reusify@1.1.0:
    reusify: private
  run-parallel@1.2.0:
    run-parallel: private
  safe-buffer@5.2.1:
    safe-buffer: private
  safer-buffer@2.1.2:
    safer-buffer: private
  semver@7.7.1:
    semver: private
  send@0.19.0:
    send: private
  serve-static@1.16.2:
    serve-static: private
  setprototypeof@1.2.0:
    setprototypeof: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  side-channel-list@1.0.0:
    side-channel-list: private
  side-channel-map@1.0.1:
    side-channel-map: private
  side-channel-weakmap@1.0.2:
    side-channel-weakmap: private
  side-channel@1.1.0:
    side-channel: private
  sift@17.1.3:
    sift: private
  sparse-bitfield@3.0.3:
    sparse-bitfield: private
  statuses@2.0.1:
    statuses: private
  strip-json-comments@3.1.1:
    strip-json-comments: private
  supports-color@7.2.0:
    supports-color: private
  swagger-parser@10.0.3(openapi-types@12.1.3):
    swagger-parser: private
  swagger-ui-dist@5.22.0:
    swagger-ui-dist: private
  synckit@0.9.2:
    synckit: private
  to-regex-range@5.0.1:
    to-regex-range: private
  toidentifier@1.0.1:
    toidentifier: private
  tr46@5.0.0:
    tr46: private
  ts-api-utils@2.0.1(typescript@5.8.2):
    ts-api-utils: private
  tslib@2.8.1:
    tslib: private
  type-check@0.4.0:
    type-check: private
  type-is@1.6.18:
    type-is: private
  undici-types@6.20.0:
    undici-types: private
  unpipe@1.0.0:
    unpipe: private
  uri-js@4.4.1:
    uri-js: private
  utils-merge@1.0.1:
    utils-merge: private
  validator@13.15.0:
    validator: private
  vary@1.1.2:
    vary: private
  webidl-conversions@7.0.0:
    webidl-conversions: private
  whatwg-url@14.1.1:
    whatwg-url: private
  which@2.0.2:
    which: private
  word-wrap@1.2.5:
    word-wrap: private
  wrappy@1.0.2:
    wrappy: private
  yaml@2.0.0-1:
    yaml: private
  yocto-queue@0.1.0:
    yocto-queue: private
  z-schema@5.0.5:
    z-schema: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@9.15.4
pendingBuilds: []
prunedAt: Wed, 04 Jun 2025 17:18:44 GMT
publicHoistPattern:
  - '*eslint*'
  - '*prettier*'
registries:
  default: https://registry.npmjs.org/
skipped:
  - '@esbuild/aix-ppc64@0.25.1'
  - '@esbuild/android-arm64@0.25.1'
  - '@esbuild/android-arm@0.25.1'
  - '@esbuild/android-x64@0.25.1'
  - '@esbuild/darwin-arm64@0.25.1'
  - '@esbuild/darwin-x64@0.25.1'
  - '@esbuild/freebsd-arm64@0.25.1'
  - '@esbuild/freebsd-x64@0.25.1'
  - '@esbuild/linux-arm64@0.25.1'
  - '@esbuild/linux-arm@0.25.1'
  - '@esbuild/linux-ia32@0.25.1'
  - '@esbuild/linux-loong64@0.25.1'
  - '@esbuild/linux-mips64el@0.25.1'
  - '@esbuild/linux-ppc64@0.25.1'
  - '@esbuild/linux-riscv64@0.25.1'
  - '@esbuild/linux-s390x@0.25.1'
  - '@esbuild/linux-x64@0.25.1'
  - '@esbuild/netbsd-arm64@0.25.1'
  - '@esbuild/netbsd-x64@0.25.1'
  - '@esbuild/openbsd-arm64@0.25.1'
  - '@esbuild/openbsd-x64@0.25.1'
  - '@esbuild/sunos-x64@0.25.1'
  - '@esbuild/win32-arm64@0.25.1'
  - '@esbuild/win32-ia32@0.25.1'
  - fsevents@2.3.3
storeDir: C:\Users\<USER>\AppData\Local\pnpm\store\v3
virtualStoreDir: C:\Users\<USER>\Desktop\Learning\Codes\dapp\server\node_modules\.pnpm
virtualStoreDirMaxLength: 120
