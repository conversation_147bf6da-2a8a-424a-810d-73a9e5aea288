/**
 * Enhanced Swagger schema definitions for comprehensive product management
 */
export default {
  // Base response schema
  ApiResponse: {
    type: "object",
    properties: {
      success: {
        type: "boolean",
        description: "Indicates if the request was successful",
      },
      message: {
        type: "string",
        description: "Response message",
      },
      data: {
        type: "object",
        description: "Response data",
      },
      meta: {
        type: "object",
        description: "Metadata for pagination, etc.",
        properties: {
          total: {
            type: "number",
            description: "Total number of items",
          },
          page: {
            type: "number",
            description: "Current page number",
          },
          limit: {
            type: "number",
            description: "Number of items per page",
          },
          pages: {
            type: "number",
            description: "Total number of pages",
          },
        },
      },
    },
  },

  // Enhanced sub-schemas
  Dimensions: {
    type: "object",
    properties: {
      width: {
        type: "number",
        description: "Width of the product",
      },
      height: {
        type: "number",
        description: "Height of the product",
      },
      depth: {
        type: "number",
        description: "Depth of the product",
      },
      unit: {
        type: "string",
        enum: ["mm", "cm"],
        description: "Unit of measurement",
      },
    },
    required: ["width", "height", "depth", "unit"],
  },

  Weight: {
    type: "object",
    properties: {
      value: {
        type: "number",
        description: "Weight value",
      },
      unit: {
        type: "string",
        enum: ["g", "kg"],
        description: "Unit of weight",
      },
    },
    required: ["value", "unit"],
  },

  ProductAddress: {
    type: "object",
    properties: {
      street: {
        type: "string",
        description: "Street address",
      },
      city: {
        type: "string",
        description: "City",
      },
      state: {
        type: "string",
        description: "State or province",
      },
      postalCode: {
        type: "string",
        description: "Postal code",
      },
      country: {
        type: "string",
        description: "Country",
      },
      coordinates: {
        type: "object",
        properties: {
          latitude: {
            type: "number",
            description: "Latitude coordinate",
          },
          longitude: {
            type: "number",
            description: "Longitude coordinate",
          },
        },
        required: ["latitude", "longitude"],
      },
    },
  },

  ProductOrigin: {
    type: "object",
    properties: {
      country: {
        type: "string",
        description: "Country of origin",
      },
      region: {
        type: "string",
        description: "Region of origin",
      },
      city: {
        type: "string",
        description: "City of origin",
      },
      manufacturer: {
        type: "string",
        description: "Manufacturer name",
      },
      madeIn: {
        type: "string",
        description: "Made in location",
      },
    },
    required: ["country"],
  },

  ProductVariant: {
    type: "object",
    properties: {
      id: {
        type: "string",
        description: "Variant ID",
      },
      name: {
        type: "string",
        description: "Variant name",
      },
      sku: {
        type: "string",
        description: "Variant SKU",
      },
      price: {
        type: "number",
        description: "Variant price",
      },
      stock: {
        type: "number",
        description: "Variant stock",
      },
      image: {
        type: "string",
        description: "Variant image URL",
      },
      attributes: {
        type: "object",
        additionalProperties: {
          type: "string",
        },
        description: "Variant attributes (e.g., color: red, size: M)",
      },
    },
    required: ["id", "name"],
  },

  ProductAttribute: {
    type: "object",
    properties: {
      name: {
        type: "string",
        description: "Attribute name",
      },
      values: {
        type: "array",
        items: {
          type: "string",
        },
        description: "Possible attribute values",
      },
      visible: {
        type: "boolean",
        description: "Whether attribute is visible to customers",
      },
      variation: {
        type: "boolean",
        description: "Whether attribute is used for variations",
      },
    },
    required: ["name", "values", "visible", "variation"],
  },

  ProductSEO: {
    type: "object",
    properties: {
      metaTitle: {
        type: "string",
        description: "SEO meta title",
      },
      metaDescription: {
        type: "string",
        description: "SEO meta description",
      },
      focusKeyword: {
        type: "string",
        description: "Primary SEO keyword",
      },
      slug: {
        type: "string",
        description: "URL slug",
      },
      canonicalUrl: {
        type: "string",
        description: "Canonical URL",
      },
    },
  },

  ProductCompliance: {
    type: "object",
    properties: {
      certifications: {
        type: "array",
        items: {
          type: "string",
        },
        description: "Product certifications",
      },
      warnings: {
        type: "array",
        items: {
          type: "string",
        },
        description: "Safety warnings",
      },
      restrictions: {
        type: "array",
        items: {
          type: "string",
        },
        description: "Usage restrictions",
      },
      requiresLicense: {
        type: "boolean",
        description: "Whether product requires a license",
      },
      hazardousMaterial: {
        type: "boolean",
        description: "Whether product contains hazardous materials",
      },
    },
  },

  ProductWarranty: {
    type: "object",
    properties: {
      duration: {
        type: "number",
        description: "Warranty duration in months",
      },
      type: {
        type: "string",
        enum: ["manufacturer", "seller", "extended"],
        description: "Type of warranty",
      },
      terms: {
        type: "string",
        description: "Warranty terms and conditions",
      },
      coverage: {
        type: "array",
        items: {
          type: "string",
        },
        description: "What the warranty covers",
      },
    },
  },

  ProductReturn: {
    type: "object",
    properties: {
      returnable: {
        type: "boolean",
        description: "Whether product can be returned",
      },
      returnWindow: {
        type: "number",
        description: "Return window in days",
      },
      returnPolicy: {
        type: "string",
        description: "Return policy details",
      },
      restockingFee: {
        type: "number",
        description: "Restocking fee amount",
      },
      returnShippingPaid: {
        type: "string",
        enum: ["buyer", "seller", "shared"],
        description: "Who pays for return shipping",
      },
    },
    required: ["returnable"],
  },

  // Enhanced comprehensive Product schema
  Product: {
    type: "object",
    properties: {
      _id: {
        type: "string",
        description: "Product ID",
      },
      // Basic Info
      name: {
        type: "string",
        description: "Product name",
      },
      slug: {
        type: "string",
        description: "URL-friendly product slug",
      },
      brand: {
        type: "string",
        description: "Product brand",
      },
      model: {
        type: "string",
        description: "Product model",
      },
      description: {
        type: "string",
        description: "Product description",
      },
      shortDescription: {
        type: "string",
        description: "Short product description",
      },
      sku: {
        type: "string",
        description: "Stock Keeping Unit",
      },
      barcode: {
        type: "string",
        description: "Product barcode",
      },
      productType: {
        type: "string",
        enum: ["physical", "digital", "service", "subscription", "bundle"],
        description: "Type of product",
      },
      visibility: {
        type: "string",
        enum: ["public", "private", "hidden", "password-protected"],
        description: "Product visibility",
      },
      // Pricing
      price: {
        type: "number",
        description: "Product price",
      },
      originalPrice: {
        type: "number",
        description: "Original price before discount",
      },
      currency: {
        type: "string",
        enum: ["EUR", "USD"],
        description: "Currency",
      },
      saleEndsAt: {
        type: "string",
        format: "date-time",
        description: "Date when sale ends",
      },
      costPrice: {
        type: "number",
        description: "Cost price for profit calculations",
      },
      taxStatus: {
        type: "string",
        enum: ["taxable", "tax-exempt", "shipping-only"],
        description: "Tax status",
      },
      taxClass: {
        type: "string",
        description: "Tax class",
      },
      minimumOrderQuantity: {
        type: "number",
        description: "Minimum order quantity",
      },
      maximumOrderQuantity: {
        type: "number",
        description: "Maximum order quantity",
      },
      // Inventory
      stock: {
        type: "number",
        description: "Available stock",
      },
      condition: {
        type: "string",
        enum: [
          "new",
          "like-new",
          "excellent",
          "good",
          "fair",
          "used",
          "refurbished",
          "vintage",
          "antique",
          "damaged",
        ],
        description: "Product condition",
      },
      isPublished: {
        type: "boolean",
        description: "Whether the product is published",
      },
      stockManagement: {
        type: "string",
        enum: ["track", "no-track", "backorder"],
        description: "Stock management type",
      },
      lowStockThreshold: {
        type: "number",
        description: "Low stock alert threshold",
      },
      backorderAllowed: {
        type: "boolean",
        description: "Whether backorders are allowed",
      },
      trackQuantity: {
        type: "boolean",
        description: "Whether to track quantity",
      },
      soldIndividually: {
        type: "boolean",
        description: "Whether product is sold individually",
      },
      // Details
      category: {
        type: "string",
        description: "Product category",
      },
      subcategory: {
        type: "string",
        description: "Product subcategory",
      },
      material: {
        type: "string",
        description: "Product material",
      },
      dimensions: {
        $ref: "#/components/schemas/Dimensions",
      },
      weight: {
        $ref: "#/components/schemas/Weight",
      },
      yearMade: {
        type: "number",
        description: "Year the product was made",
      },
      tags: {
        type: "array",
        items: {
          type: "string",
        },
        description: "Product tags",
      },
      color: {
        type: "string",
        description: "Product color",
      },
      size: {
        type: "string",
        description: "Product size",
      },
      attributes: {
        type: "array",
        items: {
          $ref: "#/components/schemas/ProductAttribute",
        },
        description: "Product attributes",
      },
      variants: {
        type: "array",
        items: {
          $ref: "#/components/schemas/ProductVariant",
        },
        description: "Product variants",
      },
      origin: {
        $ref: "#/components/schemas/ProductOrigin",
      },
      compliance: {
        $ref: "#/components/schemas/ProductCompliance",
      },
      // Images
      mainImage: {
        type: "string",
        description: "Main product image URL",
      },
      images: {
        type: "array",
        items: {
          type: "string",
        },
        description: "Additional product images",
      },
      imageAltTexts: {
        type: "array",
        items: {
          type: "string",
        },
        description: "Alt texts for images",
      },
      videoUrl: {
        type: "string",
        description: "Product video URL",
      },
      threeDModelUrl: {
        type: "string",
        description: "3D model URL",
      },
      // Shipping
      location: {
        type: "string",
        description: "Product location",
      },
      address: {
        $ref: "#/components/schemas/ProductAddress",
      },
      shippingCost: {
        type: "number",
        description: "Shipping cost",
      },
      shippingTime: {
        type: "string",
        description: "Estimated shipping time",
      },
      freeShipping: {
        type: "boolean",
        description: "Whether shipping is free",
      },
      shippingClass: {
        type: "string",
        enum: [
          "standard",
          "express",
          "overnight",
          "international",
          "heavy",
          "fragile",
          "digital-only",
        ],
        description: "Shipping class",
      },
      requiresShipping: {
        type: "boolean",
        description: "Whether product requires shipping",
      },
      separateShipping: {
        type: "boolean",
        description: "Whether product ships separately",
      },
      shippingDimensions: {
        $ref: "#/components/schemas/Dimensions",
      },
      shippingWeight: {
        $ref: "#/components/schemas/Weight",
      },
      // Relations
      sellerId: {
        type: "string",
        description: "Seller ID",
      },
      shopId: {
        type: "string",
        description: "Shop ID",
      },
      relatedProducts: {
        type: "array",
        items: {
          type: "string",
        },
        description: "Related product IDs",
      },
      crossSellProducts: {
        type: "array",
        items: {
          type: "string",
        },
        description: "Cross-sell product IDs",
      },
      upSellProducts: {
        type: "array",
        items: {
          type: "string",
        },
        description: "Up-sell product IDs",
      },
      bundledProducts: {
        type: "array",
        items: {
          type: "string",
        },
        description: "Bundled product IDs",
      },
      // Metrics
      averageRating: {
        type: "number",
        minimum: 0,
        maximum: 5,
        description: "Average customer rating",
      },
      reviewCount: {
        type: "number",
        description: "Number of reviews",
      },
      views: {
        type: "number",
        description: "Number of views",
      },
      purchases: {
        type: "number",
        description: "Number of purchases",
      },
      wishlistCount: {
        type: "number",
        description: "Number of times added to wishlist",
      },
      conversionRate: {
        type: "number",
        minimum: 0,
        maximum: 100,
        description: "Conversion rate percentage",
      },
      returnRate: {
        type: "number",
        minimum: 0,
        maximum: 100,
        description: "Return rate percentage",
      },
      profitMargin: {
        type: "number",
        description: "Profit margin",
      },
      // Availability
      status: {
        type: "string",
        enum: [
          "in-stock",
          "out-of-stock",
          "coming-soon",
          "archived",
          "draft",
          "suspended",
        ],
        description: "Product status",
      },
      ageRestriction: {
        type: "string",
        enum: ["none", "18+", "21+"],
        description: "Age restriction",
      },
      availableFrom: {
        type: "string",
        format: "date-time",
        description: "Date when product becomes available",
      },
      availableUntil: {
        type: "string",
        format: "date-time",
        description: "Date when product is no longer available",
      },
      featured: {
        type: "boolean",
        description: "Whether product is featured",
      },
      sticky: {
        type: "boolean",
        description: "Whether product is pinned to top",
      },
      downloadable: {
        type: "boolean",
        description: "Whether product is downloadable",
      },
      virtual: {
        type: "boolean",
        description: "Whether product is virtual",
      },
      // SEO
      seo: {
        $ref: "#/components/schemas/ProductSEO",
      },
      // Warranty & Returns
      warranty: {
        $ref: "#/components/schemas/ProductWarranty",
      },
      returnPolicy: {
        $ref: "#/components/schemas/ProductReturn",
      },
      // Timestamps
      createdAt: {
        type: "string",
        format: "date-time",
        description: "Creation date",
      },
      updatedAt: {
        type: "string",
        format: "date-time",
        description: "Last update date",
      },
      publishedAt: {
        type: "string",
        format: "date-time",
        description: "Publication date",
      },
      lastModifiedBy: {
        type: "string",
        description: "ID of user who last modified the product",
      },
    },
    required: [
      "name",
      "brand",
      "description",
      "price",
      "currency",
      "stock",
      "condition",
      "category",
      "mainImage",
      "status",
      "productType",
      "visibility",
      "taxStatus",
      "stockManagement",
    ],
  },

  // Enhanced Create Product DTO
  CreateProductDto: {
    type: "object",
    properties: {
      // Basic Info (required)
      name: {
        type: "string",
        description: "Product name",
        example: "iPhone 15 Pro Max",
      },
      brand: {
        type: "string",
        description: "Product brand",
        example: "Apple",
      },
      description: {
        type: "string",
        description: "Product description",
        example: "Latest iPhone with advanced camera system and A17 Pro chip",
      },
      price: {
        type: "number",
        description: "Product price",
        example: 1199.99,
      },
      currency: {
        type: "string",
        enum: ["EUR", "USD"],
        description: "Currency",
        default: "EUR",
        example: "EUR",
      },
      stock: {
        type: "number",
        description: "Available stock",
        example: 50,
      },
      condition: {
        type: "string",
        enum: [
          "new",
          "like-new",
          "excellent",
          "good",
          "fair",
          "used",
          "refurbished",
          "vintage",
          "antique",
          "damaged",
        ],
        description: "Product condition",
        default: "new",
        example: "new",
      },
      category: {
        type: "string",
        description: "Product category",
        example: "Electronics",
      },
      mainImage: {
        type: "string",
        description: "Main product image URL",
        example: "https://example.com/images/iphone-15-pro-max.jpg",
      },
      productType: {
        type: "string",
        enum: ["physical", "digital", "service", "subscription", "bundle"],
        description: "Type of product",
        default: "physical",
        example: "physical",
      },
      visibility: {
        type: "string",
        enum: ["public", "private", "hidden", "password-protected"],
        description: "Product visibility",
        default: "public",
        example: "public",
      },
      taxStatus: {
        type: "string",
        enum: ["taxable", "tax-exempt", "shipping-only"],
        description: "Tax status",
        default: "taxable",
        example: "taxable",
      },
      stockManagement: {
        type: "string",
        enum: ["track", "no-track", "backorder"],
        description: "Stock management type",
        default: "track",
        example: "track",
      },
      status: {
        type: "string",
        enum: [
          "in-stock",
          "out-of-stock",
          "coming-soon",
          "archived",
          "draft",
          "suspended",
        ],
        description: "Product status",
        default: "draft",
        example: "in-stock",
      },
      // Optional fields
      slug: {
        type: "string",
        description: "URL-friendly product slug",
      },
      model: {
        type: "string",
        description: "Product model",
      },
      shortDescription: {
        type: "string",
        description: "Short product description",
      },
      sku: {
        type: "string",
        description: "Stock Keeping Unit",
      },
      barcode: {
        type: "string",
        description: "Product barcode",
      },
      originalPrice: {
        type: "number",
        description: "Original price before discount",
      },
      saleEndsAt: {
        type: "string",
        format: "date-time",
        description: "Date when sale ends",
      },
      costPrice: {
        type: "number",
        description: "Cost price for profit calculations",
      },
      taxClass: {
        type: "string",
        description: "Tax class",
      },
      minimumOrderQuantity: {
        type: "number",
        description: "Minimum order quantity",
        default: 1,
      },
      maximumOrderQuantity: {
        type: "number",
        description: "Maximum order quantity",
      },
      isPublished: {
        type: "boolean",
        description: "Whether the product is published",
        default: true,
      },
      lowStockThreshold: {
        type: "number",
        description: "Low stock alert threshold",
      },
      backorderAllowed: {
        type: "boolean",
        description: "Whether backorders are allowed",
        default: false,
      },
      trackQuantity: {
        type: "boolean",
        description: "Whether to track quantity",
        default: true,
      },
      soldIndividually: {
        type: "boolean",
        description: "Whether product is sold individually",
        default: false,
      },
      subcategory: {
        type: "string",
        description: "Product subcategory",
      },
      material: {
        type: "string",
        description: "Product material",
      },
      dimensions: {
        $ref: "#/components/schemas/Dimensions",
      },
      weight: {
        $ref: "#/components/schemas/Weight",
      },
      yearMade: {
        type: "number",
        description: "Year the product was made",
        minimum: 1800,
        maximum: 2030,
        example: 2023,
      },
      tags: {
        type: "array",
        items: {
          type: "string",
        },
        description: "Product tags",
      },
      color: {
        type: "string",
        description: "Product color",
      },
      size: {
        type: "string",
        description: "Product size",
      },
      attributes: {
        type: "array",
        items: {
          $ref: "#/components/schemas/ProductAttribute",
        },
        description: "Product attributes",
      },
      variants: {
        type: "array",
        items: {
          $ref: "#/components/schemas/ProductVariant",
        },
        description: "Product variants",
      },
      origin: {
        $ref: "#/components/schemas/ProductOrigin",
      },
      compliance: {
        $ref: "#/components/schemas/ProductCompliance",
      },
      images: {
        type: "array",
        items: {
          type: "string",
        },
        description: "Additional product images",
      },
      imageAltTexts: {
        type: "array",
        items: {
          type: "string",
        },
        description: "Alt texts for images",
      },
      videoUrl: {
        type: "string",
        description: "Product video URL",
      },
      threeDModelUrl: {
        type: "string",
        description: "3D model URL",
      },
      location: {
        type: "string",
        description: "Product location",
      },
      address: {
        $ref: "#/components/schemas/ProductAddress",
      },
      shippingCost: {
        type: "number",
        description: "Shipping cost",
      },
      shippingTime: {
        type: "string",
        description: "Estimated shipping time",
      },
      freeShipping: {
        type: "boolean",
        description: "Whether shipping is free",
        default: false,
      },
      shippingClass: {
        type: "string",
        enum: [
          "standard",
          "express",
          "overnight",
          "international",
          "heavy",
          "fragile",
          "digital-only",
        ],
        description: "Shipping class",
        default: "standard",
      },
      requiresShipping: {
        type: "boolean",
        description: "Whether product requires shipping",
        default: true,
      },
      separateShipping: {
        type: "boolean",
        description: "Whether product ships separately",
        default: false,
      },
      shippingDimensions: {
        $ref: "#/components/schemas/Dimensions",
      },
      shippingWeight: {
        $ref: "#/components/schemas/Weight",
      },
      sellerId: {
        type: "string",
        description: "Seller ID (MongoDB ObjectId)",
        example: "507f1f77bcf86cd799439011",
      },
      shopId: {
        type: "string",
        description: "Shop ID (MongoDB ObjectId)",
        example: "507f1f77bcf86cd799439012",
      },
      relatedProducts: {
        type: "array",
        items: {
          type: "string",
        },
        description: "Related product IDs (MongoDB ObjectIds)",
        example: ["507f1f77bcf86cd799439013", "507f1f77bcf86cd799439014"],
      },
      crossSellProducts: {
        type: "array",
        items: {
          type: "string",
        },
        description: "Cross-sell product IDs (MongoDB ObjectIds)",
        example: ["507f1f77bcf86cd799439015"],
      },
      upSellProducts: {
        type: "array",
        items: {
          type: "string",
        },
        description: "Up-sell product IDs (MongoDB ObjectIds)",
        example: ["507f1f77bcf86cd799439016"],
      },
      bundledProducts: {
        type: "array",
        items: {
          type: "string",
        },
        description: "Bundled product IDs (MongoDB ObjectIds)",
        example: ["507f1f77bcf86cd799439017"],
      },
      ageRestriction: {
        type: "string",
        enum: ["none", "18+", "21+"],
        description: "Age restriction",
        default: "none",
      },
      availableFrom: {
        type: "string",
        format: "date-time",
        description: "Date when product becomes available",
      },
      availableUntil: {
        type: "string",
        format: "date-time",
        description: "Date when product is no longer available",
      },
      featured: {
        type: "boolean",
        description: "Whether product is featured",
        default: false,
      },
      sticky: {
        type: "boolean",
        description: "Whether product is pinned to top",
        default: false,
      },
      downloadable: {
        type: "boolean",
        description: "Whether product is downloadable",
        default: false,
      },
      virtual: {
        type: "boolean",
        description: "Whether product is virtual",
        default: false,
      },
      seo: {
        $ref: "#/components/schemas/ProductSEO",
      },
      warranty: {
        $ref: "#/components/schemas/ProductWarranty",
      },
      returnPolicy: {
        $ref: "#/components/schemas/ProductReturn",
      },
    },
    required: [
      "name",
      "brand",
      "description",
      "price",
      "currency",
      "stock",
      "condition",
      "category",
      "mainImage",
      "status",
      "productType",
      "visibility",
      "taxStatus",
      "stockManagement",
    ],
  },

  // Enhanced Update Product DTO (all fields optional)
  UpdateProductDto: {
    type: "object",
    description: "All fields are optional for updates",
    properties: {
      // Reference the CreateProductDto but make all fields optional
      name: {
        type: "string",
        description: "Product name",
        example: "iPhone 15 Pro Max - Updated",
      },
      slug: {
        type: "string",
        description: "URL-friendly product slug",
      },
      brand: {
        type: "string",
        description: "Product brand",
      },
      model: {
        type: "string",
        description: "Product model",
      },
      description: {
        type: "string",
        description: "Product description",
      },
      shortDescription: {
        type: "string",
        description: "Short product description",
      },
      sku: {
        type: "string",
        description: "Stock Keeping Unit",
      },
      barcode: {
        type: "string",
        description: "Product barcode",
      },
      productType: {
        type: "string",
        enum: ["physical", "digital", "service", "subscription", "bundle"],
        description: "Type of product",
      },
      visibility: {
        type: "string",
        enum: ["public", "private", "hidden", "password-protected"],
        description: "Product visibility",
      },
      price: {
        type: "number",
        description: "Product price",
        example: 1099.99,
      },
      originalPrice: {
        type: "number",
        description: "Original price before discount",
      },
      currency: {
        type: "string",
        enum: ["EUR", "USD"],
        description: "Currency",
      },
      saleEndsAt: {
        type: "string",
        format: "date-time",
        description: "Date when sale ends",
      },
      costPrice: {
        type: "number",
        description: "Cost price for profit calculations",
      },
      taxStatus: {
        type: "string",
        enum: ["taxable", "tax-exempt", "shipping-only"],
        description: "Tax status",
      },
      taxClass: {
        type: "string",
        description: "Tax class",
      },
      minimumOrderQuantity: {
        type: "number",
        description: "Minimum order quantity",
      },
      maximumOrderQuantity: {
        type: "number",
        description: "Maximum order quantity",
      },
      stock: {
        type: "number",
        description: "Available stock",
        example: 25,
      },
      condition: {
        type: "string",
        enum: [
          "new",
          "like-new",
          "excellent",
          "good",
          "fair",
          "used",
          "refurbished",
          "vintage",
          "antique",
          "damaged",
        ],
        description: "Product condition",
      },
      isPublished: {
        type: "boolean",
        description: "Whether the product is published",
      },
      stockManagement: {
        type: "string",
        enum: ["track", "no-track", "backorder"],
        description: "Stock management type",
      },
      lowStockThreshold: {
        type: "number",
        description: "Low stock alert threshold",
      },
      backorderAllowed: {
        type: "boolean",
        description: "Whether backorders are allowed",
      },
      trackQuantity: {
        type: "boolean",
        description: "Whether to track quantity",
      },
      soldIndividually: {
        type: "boolean",
        description: "Whether product is sold individually",
      },
      category: {
        type: "string",
        description: "Product category",
      },
      subcategory: {
        type: "string",
        description: "Product subcategory",
      },
      material: {
        type: "string",
        description: "Product material",
      },
      dimensions: {
        $ref: "#/components/schemas/Dimensions",
      },
      weight: {
        $ref: "#/components/schemas/Weight",
      },
      yearMade: {
        type: "number",
        description: "Year the product was made",
      },
      tags: {
        type: "array",
        items: {
          type: "string",
        },
        description: "Product tags",
      },
      color: {
        type: "string",
        description: "Product color",
      },
      size: {
        type: "string",
        description: "Product size",
      },
      attributes: {
        type: "array",
        items: {
          $ref: "#/components/schemas/ProductAttribute",
        },
        description: "Product attributes",
      },
      variants: {
        type: "array",
        items: {
          $ref: "#/components/schemas/ProductVariant",
        },
        description: "Product variants",
      },
      origin: {
        $ref: "#/components/schemas/ProductOrigin",
      },
      compliance: {
        $ref: "#/components/schemas/ProductCompliance",
      },
      mainImage: {
        type: "string",
        description: "Main product image URL",
      },
      images: {
        type: "array",
        items: {
          type: "string",
        },
        description: "Additional product images",
      },
      imageAltTexts: {
        type: "array",
        items: {
          type: "string",
        },
        description: "Alt texts for images",
      },
      videoUrl: {
        type: "string",
        description: "Product video URL",
      },
      threeDModelUrl: {
        type: "string",
        description: "3D model URL",
      },
      location: {
        type: "string",
        description: "Product location",
      },
      address: {
        $ref: "#/components/schemas/ProductAddress",
      },
      shippingCost: {
        type: "number",
        description: "Shipping cost",
      },
      shippingTime: {
        type: "string",
        description: "Estimated shipping time",
      },
      freeShipping: {
        type: "boolean",
        description: "Whether shipping is free",
      },
      shippingClass: {
        type: "string",
        enum: [
          "standard",
          "express",
          "overnight",
          "international",
          "heavy",
          "fragile",
          "digital-only",
        ],
        description: "Shipping class",
      },
      requiresShipping: {
        type: "boolean",
        description: "Whether product requires shipping",
      },
      separateShipping: {
        type: "boolean",
        description: "Whether product ships separately",
      },
      shippingDimensions: {
        $ref: "#/components/schemas/Dimensions",
      },
      shippingWeight: {
        $ref: "#/components/schemas/Weight",
      },
      sellerId: {
        type: "string",
        description: "Seller ID",
      },
      shopId: {
        type: "string",
        description: "Shop ID",
      },
      relatedProducts: {
        type: "array",
        items: {
          type: "string",
        },
        description: "Related product IDs",
      },
      crossSellProducts: {
        type: "array",
        items: {
          type: "string",
        },
        description: "Cross-sell product IDs",
      },
      upSellProducts: {
        type: "array",
        items: {
          type: "string",
        },
        description: "Up-sell product IDs",
      },
      bundledProducts: {
        type: "array",
        items: {
          type: "string",
        },
        description: "Bundled product IDs",
      },
      status: {
        type: "string",
        enum: [
          "in-stock",
          "out-of-stock",
          "coming-soon",
          "archived",
          "draft",
          "suspended",
        ],
        description: "Product status",
      },
      ageRestriction: {
        type: "string",
        enum: ["none", "18+", "21+"],
        description: "Age restriction",
      },
      availableFrom: {
        type: "string",
        format: "date-time",
        description: "Date when product becomes available",
      },
      availableUntil: {
        type: "string",
        format: "date-time",
        description: "Date when product is no longer available",
      },
      featured: {
        type: "boolean",
        description: "Whether product is featured",
      },
      sticky: {
        type: "boolean",
        description: "Whether product is pinned to top",
      },
      downloadable: {
        type: "boolean",
        description: "Whether product is downloadable",
      },
      virtual: {
        type: "boolean",
        description: "Whether product is virtual",
      },
      seo: {
        $ref: "#/components/schemas/ProductSEO",
      },
      warranty: {
        $ref: "#/components/schemas/ProductWarranty",
      },
      returnPolicy: {
        $ref: "#/components/schemas/ProductReturn",
      },
      lastModifiedBy: {
        type: "string",
        description: "ID of user who last modified the product",
      },
    },
  },
};
