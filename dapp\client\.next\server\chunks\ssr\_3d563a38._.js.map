{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 12, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/app/page.tsx"], "sourcesContent": ["import { redirect } from \"next/navigation\";\r\n\r\n// Redirect to the admin dashboard\r\nexport default function RootPage() {\r\n  redirect(\"/admin/dashboard\");\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AAAA;;AAGe,SAAS;IACtB,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;AACX", "debugId": null}}]}