"use client";

import React, { useState } from "react";

import { Plus, Upload, X } from "lucide-react";
import Image from "next/image";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Product } from "@/types/product";

type ProductSectionProps = {
  product: Product;
  isEditing: boolean;
  onProductUpdate: (product: Product) => void;
};

export const ProductImageSection = ({
  product,
  isEditing,
  onProductUpdate,
}: ProductSectionProps) => {
  const [isLoading, setIsLoading] = useState(true);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [showImagePreview, setShowImagePreview] = useState(false);

  // Get additional images from product data (excluding main image)
  const mainImageUrl = product.mainImage || product.image;
  const allProductImages = product.images || [];

  // Filter out the main image from additional images to avoid duplicates
  const additionalImages = allProductImages.filter(
    (img) => img !== mainImageUrl
  );

  // Combine main image with additional images for preview
  const allImages = [mainImageUrl, ...additionalImages].filter(Boolean);

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-700";
      case "draft":
        return "bg-yellow-100 text-yellow-700";
      case "archived":
        return "bg-gray-100 text-gray-700";
      default:
        return "bg-gray-100 text-gray-700";
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          Product Images
          {!isEditing && (
            <div className="flex gap-2">
              {product.status && (
                <Badge className={getStatusColor(product.status)}>
                  {product.status.charAt(0).toUpperCase() +
                    product.status.slice(1)}
                </Badge>
              )}
              {product.isOnSale && (
                <Badge className="bg-red-500 text-white">Sale</Badge>
              )}
            </div>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Main Product Image */}
        <div className="relative aspect-square overflow-hidden rounded-lg border">
          {(product.mainImage || product.image) &&
          (product.mainImage || product.image).trim() !== "" ? (
            <Image
              src={product.mainImage || product.image}
              alt={product.name}
              fill
              className="cursor-pointer object-cover transition-opacity duration-300 hover:opacity-90"
              onLoad={() => setIsLoading(false)}
              onError={() => setIsLoading(false)}
              onClick={() => {
                setSelectedImageIndex(0);
                setShowImagePreview(true);
              }}
            />
          ) : (
            <div className="flex h-full items-center justify-center bg-gray-100">
              <div className="text-center text-gray-500">
                <Upload className="mx-auto mb-2 h-12 w-12" />
                <p className="text-sm">No image available</p>
              </div>
            </div>
          )}

          {isEditing && (
            <div className="absolute inset-0 flex items-center justify-center bg-black/50 opacity-0 transition-opacity hover:opacity-100">
              <Button size="sm" variant="secondary">
                <Upload className="mr-2 h-4 w-4" />
                Change Image
              </Button>
            </div>
          )}
        </div>

        {/* Additional Images */}
        <div>
          <h4 className="mb-2 text-sm font-medium">
            Additional Images{" "}
            {additionalImages.length > 0 && `(${additionalImages.length})`}
          </h4>
          {additionalImages.length === 0 ? (
            <div className="rounded border border-dashed border-gray-300 p-4 text-center">
              <p className="text-sm text-gray-500">No additional images</p>
            </div>
          ) : (
            <div className="grid grid-cols-3 gap-2">
              {additionalImages.slice(0, 2).map((img, index) => (
                <div
                  key={index}
                  className="group relative aspect-square overflow-hidden rounded border"
                >
                  {img && img.trim() !== "" ? (
                    <Image
                      src={img}
                      alt={`${product.name} ${index + 1}`}
                      fill
                      className="cursor-pointer object-cover transition-opacity hover:opacity-90"
                      onClick={() => {
                        setSelectedImageIndex(index + 1);
                        setShowImagePreview(true);
                      }}
                    />
                  ) : (
                    <div className="flex h-full items-center justify-center bg-gray-100">
                      <div className="text-center text-gray-400">
                        <Upload className="mx-auto h-6 w-6" />
                      </div>
                    </div>
                  )}
                  {isEditing && (
                    <div className="absolute inset-0 flex items-center justify-center bg-black/50 opacity-0 transition-opacity group-hover:opacity-100">
                      <Button
                        size="sm"
                        variant="ghost"
                        className="h-6 w-6 p-0 text-white"
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </div>
                  )}
                </div>
              ))}

              {/* Show more indicator if there are more than 2 additional images */}
              {additionalImages.length > 2 && (
                <div className="flex aspect-square items-center justify-center rounded border border-dashed border-gray-300 bg-gray-50">
                  <div className="text-center text-gray-500">
                    <span className="text-sm font-medium">
                      +{additionalImages.length - 2} more
                    </span>
                  </div>
                </div>
              )}

              {/* Add Image Button */}
              {isEditing && (
                <div className="flex aspect-square items-center justify-center rounded border border-dashed border-gray-300 bg-gray-50 hover:bg-gray-100">
                  <Button size="sm" variant="ghost" className="h-6 w-6 p-0">
                    <Plus className="h-3 w-3" />
                  </Button>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Image Guidelines (when editing) */}
        {isEditing && (
          <div className="rounded-md bg-blue-50 p-3">
            <h5 className="text-sm font-medium text-blue-900">
              Image Guidelines
            </h5>
            <ul className="mt-1 text-xs text-blue-700">
              <li>• Use high-quality images (min 800x800px)</li>
              <li>• Square aspect ratio recommended</li>
              <li>• Maximum file size: 5MB</li>
              <li>• Supported formats: JPG, PNG, WebP</li>
            </ul>
          </div>
        )}

        {/* Image Preview Modal */}
        {showImagePreview && allImages.length > 0 && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/80">
            <div className="relative max-h-[90vh] max-w-[90vw]">
              {/* Close Button */}
              <Button
                variant="ghost"
                size="sm"
                className="absolute -top-12 right-0 text-white hover:bg-white/20"
                onClick={() => setShowImagePreview(false)}
              >
                <X className="h-6 w-6" />
              </Button>

              {/* Main Preview Image */}
              <div className="relative h-[70vh] w-[80vw]">
                <Image
                  src={allImages[selectedImageIndex]}
                  alt={`${product.name} preview`}
                  fill
                  className="object-contain"
                />
              </div>

              {/* Navigation Arrows */}
              {allImages.length > 1 && (
                <>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="absolute left-4 top-1/2 -translate-y-1/2 text-white hover:bg-white/20"
                    onClick={() =>
                      setSelectedImageIndex(
                        selectedImageIndex === 0
                          ? allImages.length - 1
                          : selectedImageIndex - 1
                      )
                    }
                  >
                    ←
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="absolute right-4 top-1/2 -translate-y-1/2 text-white hover:bg-white/20"
                    onClick={() =>
                      setSelectedImageIndex(
                        selectedImageIndex === allImages.length - 1
                          ? 0
                          : selectedImageIndex + 1
                      )
                    }
                  >
                    →
                  </Button>
                </>
              )}

              {/* Image Counter */}
              {allImages.length > 1 && (
                <div className="absolute bottom-4 left-1/2 -translate-x-1/2 rounded-full bg-black/50 px-3 py-1 text-sm text-white">
                  {selectedImageIndex + 1} / {allImages.length}
                </div>
              )}

              {/* Thumbnail Navigation */}
              {allImages.length > 1 && (
                <div className="mt-4 flex justify-center gap-2">
                  {allImages.map((img, index) => (
                    <div
                      key={index}
                      className={`relative h-16 w-16 cursor-pointer overflow-hidden rounded border-2 ${
                        index === selectedImageIndex
                          ? "border-white"
                          : "border-transparent opacity-60 hover:opacity-80"
                      }`}
                      onClick={() => setSelectedImageIndex(index)}
                    >
                      <Image
                        src={img}
                        alt={`Thumbnail ${index + 1}`}
                        fill
                        className="object-cover"
                      />
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
