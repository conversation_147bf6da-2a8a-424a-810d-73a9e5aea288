"use client";

import { useState } from "react";

import { Pencil, Plus, Save, Trash2, X } from "lucide-react";
import { toast } from "sonner";

import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

export type Material = {
  id: string;
  name: string;
  description: string;
  slug: string;
};

type MaterialManagerProps = {
  initialMaterials?: Material[];
  onMaterialsChange?: (materials: Material[]) => void;
};

/**
 * Component for managing product materials
 */
export const MaterialManager = ({
  initialMaterials = [],
  onMaterialsChange,
}: MaterialManagerProps) => {
  const [materials, setMaterials] = useState<Material[]>(initialMaterials);
  const [newMaterial, setNewMaterial] = useState<Partial<Material>>({
    name: "",
    description: "",
  });
  const [editingMaterialId, setEditingMaterialId] = useState<string | null>(
    null
  );
  const [editForm, setEditForm] = useState<Partial<Material>>({});

  // Generate a slug from the material name
  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, "-")
      .replace(/^-|-$/g, "");
  };

  // Add a new material
  const handleAddMaterial = () => {
    if (!newMaterial.name) {
      toast.error("Material name is required");
      return;
    }

    const slug = generateSlug(newMaterial.name);

    // Check if slug already exists
    if (materials.some((mat) => mat.slug === slug)) {
      toast.error("A material with this name already exists");
      return;
    }

    const newMaterialWithId: Material = {
      id: `material-${Date.now()}`,
      name: newMaterial.name,
      description: newMaterial.description || "",
      slug,
    };

    const updatedMaterials = [...materials, newMaterialWithId];
    setMaterials(updatedMaterials);
    setNewMaterial({ name: "", description: "" });

    // Notify parent component
    if (onMaterialsChange) {
      onMaterialsChange(updatedMaterials);
    }

    // Save to localStorage
    localStorage.setItem("product-materials", JSON.stringify(updatedMaterials));
    toast.success("Material added successfully");
  };

  // Start editing a material
  const handleEditStart = (material: Material) => {
    setEditingMaterialId(material.id);
    setEditForm({ ...material });
  };

  // Cancel editing
  const handleEditCancel = () => {
    setEditingMaterialId(null);
    setEditForm({});
  };

  // Save edited material
  const handleEditSave = () => {
    if (!editForm.name) {
      toast.error("Material name is required");
      return;
    }

    const updatedMaterials = materials.map((mat) =>
      mat.id === editingMaterialId
        ? {
            ...mat,
            name: editForm.name || mat.name,
            description: editForm.description || mat.description,

            slug:
              mat.name !== editForm.name
                ? generateSlug(editForm.name || mat.name)
                : mat.slug,
          }
        : mat
    );

    setMaterials(updatedMaterials);
    setEditingMaterialId(null);
    setEditForm({});

    // Notify parent component
    if (onMaterialsChange) {
      onMaterialsChange(updatedMaterials);
    }

    // Save to localStorage
    localStorage.setItem("product-materials", JSON.stringify(updatedMaterials));
    toast.success("Material updated successfully");
  };

  // Delete a material
  const handleDelete = (materialId: string) => {
    const updatedMaterials = materials.filter((mat) => mat.id !== materialId);
    setMaterials(updatedMaterials);

    // Notify parent component
    if (onMaterialsChange) {
      onMaterialsChange(updatedMaterials);
    }

    // Save to localStorage
    localStorage.setItem("product-materials", JSON.stringify(updatedMaterials));
    toast.success("Material deleted successfully");
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Manage Materials</CardTitle>
      </CardHeader>
      <CardContent>
        {/* Add new material form */}
        <div className="mb-6 space-y-4 rounded-md border p-4">
          <h3 className="text-lg font-medium">Add New Material</h3>
          <div className="grid gap-4 sm:grid-cols-2">
            <div>
              <Label htmlFor="new-material-name">Name</Label>
              <Input
                id="new-material-name"
                value={newMaterial.name}
                onChange={(e) =>
                  setNewMaterial({ ...newMaterial, name: e.target.value })
                }
                placeholder="e.g. Cotton"
              />
            </div>
            <div>
              <Label htmlFor="new-material-description">
                Description (optional)
              </Label>
              <Input
                id="new-material-description"
                value={newMaterial.description}
                onChange={(e) =>
                  setNewMaterial({
                    ...newMaterial,
                    description: e.target.value,
                  })
                }
                placeholder="e.g. Natural fabric made from cotton plants"
              />
            </div>
          </div>
          <Button onClick={handleAddMaterial} className="mt-2">
            <Plus className="mr-2 size-4" />
            Add Material
          </Button>
        </div>

        {/* Materials list */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Existing Materials</h3>
          {materials.length === 0 ? (
            <p className="text-sm text-muted-foreground">
              No materials yet. Add your first material above.
            </p>
          ) : (
            <div className="space-y-2">
              {materials.map((material) => (
                <div
                  key={material.id}
                  className="flex items-center justify-between rounded-md border p-3"
                >
                  {editingMaterialId === material.id ? (
                    // Edit mode
                    <div className="flex w-full flex-col space-y-2">
                      <div className="grid gap-2 sm:grid-cols-2">
                        <Input
                          value={editForm.name || ""}
                          onChange={(e) =>
                            setEditForm({ ...editForm, name: e.target.value })
                          }
                          placeholder="Material name"
                        />
                        <Input
                          value={editForm.description || ""}
                          onChange={(e) =>
                            setEditForm({
                              ...editForm,
                              description: e.target.value,
                            })
                          }
                          placeholder="Description (optional)"
                        />
                      </div>
                      <div className="flex space-x-2">
                        <Button size="sm" onClick={handleEditSave}>
                          <Save className="mr-2 size-4" />
                          Save
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={handleEditCancel}
                        >
                          <X className="mr-2 size-4" />
                          Cancel
                        </Button>
                      </div>
                    </div>
                  ) : (
                    // View mode
                    <>
                      <div>
                        <h4 className="font-medium">{material.name}</h4>
                        {material.description && (
                          <p className="text-sm text-muted-foreground">
                            {material.description}
                          </p>
                        )}
                      </div>
                      <div className="flex space-x-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleEditStart(material)}
                        >
                          <Pencil className="size-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          className="text-destructive hover:bg-destructive hover:text-destructive-foreground"
                          onClick={() => handleDelete(material.id)}
                        >
                          <Trash2 className="size-4" />
                        </Button>
                      </div>
                    </>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
