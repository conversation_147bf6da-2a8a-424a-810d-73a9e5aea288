// Enhanced product types matching client-side structure

// Basic enums and types
export type CurrencyUnit = "EUR" | "USD";
export type WeightUnit = "g" | "kg";
export type DimensionUnit = "mm" | "cm";
export type AgeRestriction = "none" | "18+" | "21+";

export type ProductCondition =
  | "new"
  | "like-new"
  | "excellent"
  | "good"
  | "fair"
  | "used"
  | "refurbished"
  | "vintage"
  | "antique"
  | "damaged";

// Enhanced product types
export type ProductType =
  | "physical"
  | "digital"
  | "service"
  | "subscription"
  | "bundle";

export type ProductVisibility =
  | "public"
  | "private"
  | "hidden"
  | "password-protected";

export type TaxStatus = "taxable" | "tax-exempt" | "shipping-only";

export type StockManagement = "track" | "no-track" | "backorder";

export type ShippingClass =
  | "standard"
  | "express"
  | "overnight"
  | "international"
  | "heavy"
  | "fragile"
  | "digital-only";

export type ProductStatus =
  | "in-stock"
  | "out-of-stock"
  | "coming-soon"
  | "archived"
  | "draft"
  | "suspended";

// Complex type interfaces
export interface ProductOrigin {
  country: string;
  region?: string;
  city?: string;
  manufacturer?: string;
  madeIn?: string;
}

export interface ProductAddress {
  street?: string;
  city?: string;
  state?: string;
  postalCode?: string;
  country?: string;
  coordinates?: {
    latitude: number;
    longitude: number;
  };
}

export interface ProductVariant {
  id: string;
  name: string;
  sku?: string;
  price?: number;
  stock?: number;
  image?: string;
  attributes: Record<string, string>;
}

export interface ProductAttribute {
  name: string;
  values: string[];
  visible: boolean;
  variation: boolean;
}

export interface ProductSEO {
  metaTitle?: string;
  metaDescription?: string;
  focusKeyword?: string;
  slug?: string;
  canonicalUrl?: string;
}

export interface ProductCompliance {
  certifications?: string[];
  warnings?: string[];
  restrictions?: string[];
  requiresLicense?: boolean;
  hazardousMaterial?: boolean;
}

export interface ProductWarranty {
  duration?: number;
  type?: "manufacturer" | "seller" | "extended";
  terms?: string;
  coverage?: string[];
}

export interface ProductReturn {
  returnable: boolean;
  returnWindow?: number;
  returnPolicy?: string;
  restockingFee?: number;
  returnShippingPaid?: "buyer" | "seller" | "shared";
}

export interface Dimensions {
  width: number;
  height: number;
  depth: number;
  unit: DimensionUnit;
}

export interface Weight {
  value: number;
  unit: WeightUnit;
}

// Interface for creating a new product
export interface CreateProductDto {
  name: string;
  brand: string;
  model?: string;
  description: string;
  price: number;
  originalPrice?: number;
  currency: CurrencyUnit;
  saleEndsAt?: Date | string;
  stock: number;
  condition: ProductCondition;
  isPublished: boolean;
  category: string;
  material?: string;
  dimensions?: Dimensions;
  weight?: Weight;
  yearMade?: number;
  tags?: string[];
  mainImage: string;
  images?: string[];
  location?: string;
  shippingCost?: number;
  shippingTime?: string;
  freeShipping: boolean;
  status: ProductStatus;
  ageRestriction: AgeRestriction;
  availableFrom?: Date | string;
  availableUntil?: Date | string;
}

// Interface for updating an existing product
export interface UpdateProductDto extends Partial<CreateProductDto> {
  lastModifiedBy?: string;
}

// Interface for product query filters
export interface ProductFilters {
  search?: string;
  category?: string;
  brand?: string;
  minPrice?: number;
  maxPrice?: number;
  condition?: ProductCondition;
  inStock?: boolean;
  tags?: string[];
  status?: ProductStatus;
  sortBy?: "price" | "createdAt" | "name";
  sortOrder?: "asc" | "desc";
  page?: number;
  limit?: number;
}

// Sectioned product interfaces matching client structure
export interface ProductBasicInfo {
  _id?: string;
  name: string;
  slug?: string;
  brand: string;
  model?: string;
  description: string;
  shortDescription?: string;
  sku?: string;
  barcode?: string;
  productType: ProductType;
  visibility: ProductVisibility;
}

export interface ProductPricing {
  price: number;
  originalPrice?: number;
  currency: CurrencyUnit;
  saleEndsAt?: Date | string;
  costPrice?: number;
  taxStatus: TaxStatus;
  taxClass?: string;
  minimumOrderQuantity?: number;
  maximumOrderQuantity?: number;
}

export interface ProductInventory {
  stock: number;
  condition: ProductCondition;
  isPublished: boolean;
  stockManagement: StockManagement;
  lowStockThreshold?: number;
  backorderAllowed?: boolean;
  trackQuantity?: boolean;
  soldIndividually?: boolean;
}

export interface ProductDetails {
  category: string;
  subcategory?: string;
  material?: string;
  dimensions?: Dimensions;
  weight?: Weight;
  yearMade?: number;
  tags?: string[];
  color?: string;
  size?: string;
  attributes?: ProductAttribute[];
  variants?: ProductVariant[];
  origin?: ProductOrigin;
  compliance?: ProductCompliance;
}

export interface ProductImages {
  mainImage: string;
  images?: string[];
  imageAltTexts?: string[];
  videoUrl?: string;
  threeDModelUrl?: string;
}

export interface ProductShipping {
  location?: string;
  address?: ProductAddress;
  shippingCost?: number;
  shippingTime?: string;
  freeShipping?: boolean;
  shippingClass?: ShippingClass;
  requiresShipping?: boolean;
  separateShipping?: boolean;
  shippingDimensions?: Dimensions;
  shippingWeight?: Weight;
}

export interface ProductRelations {
  sellerId?: string;
  shopId?: string;
  relatedProducts?: string[];
  crossSellProducts?: string[];
  upSellProducts?: string[];
  bundledProducts?: string[];
}

export interface ProductMetrics {
  averageRating?: number;
  reviewCount?: number;
  views?: number;
  purchases?: number;
  wishlistCount?: number;
  conversionRate?: number;
  returnRate?: number;
  profitMargin?: number;
}

export interface ProductAvailability {
  status: ProductStatus;
  ageRestriction?: AgeRestriction;
  availableFrom?: Date | string;
  availableUntil?: Date | string;
  featured?: boolean;
  sticky?: boolean;
  downloadable?: boolean;
  virtual?: boolean;
}

export interface ProductSEOSection {
  seo?: ProductSEO;
}

export interface ProductWarrantySection {
  warranty?: ProductWarranty;
}

export interface ProductReturnSection {
  returnPolicy?: ProductReturn;
}

export interface ProductTimestamps {
  createdAt: Date;
  updatedAt: Date;
  publishedAt?: Date;
  lastModifiedBy?: string;
}

// Main Product interface combining all sections
export interface IProduct
  extends ProductBasicInfo,
    ProductPricing,
    ProductInventory,
    ProductDetails,
    ProductImages,
    ProductShipping,
    ProductRelations,
    ProductMetrics,
    ProductAvailability,
    ProductSEOSection,
    ProductWarrantySection,
    ProductReturnSection,
    ProductTimestamps {}

// Enhanced DTOs for API operations
export interface CreateProductDtoEnhanced {
  // Basic Info
  name: string;
  slug?: string;
  brand: string;
  model?: string;
  description: string;
  shortDescription?: string;
  sku?: string;
  barcode?: string;
  productType: ProductType;
  visibility: ProductVisibility;

  // Pricing
  price: number;
  originalPrice?: number;
  currency: CurrencyUnit;
  saleEndsAt?: Date | string;
  costPrice?: number;
  taxStatus: TaxStatus;
  taxClass?: string;
  minimumOrderQuantity?: number;
  maximumOrderQuantity?: number;

  // Inventory
  stock: number;
  condition: ProductCondition;
  isPublished: boolean;
  stockManagement: StockManagement;
  lowStockThreshold?: number;
  backorderAllowed?: boolean;
  trackQuantity?: boolean;
  soldIndividually?: boolean;

  // Details
  category: string;
  subcategory?: string;
  material?: string;
  dimensions?: Dimensions;
  weight?: Weight;
  yearMade?: number;
  tags?: string[];
  color?: string;
  size?: string;
  attributes?: ProductAttribute[];
  variants?: ProductVariant[];
  origin?: ProductOrigin;
  compliance?: ProductCompliance;

  // Images
  mainImage: string;
  images?: string[];
  imageAltTexts?: string[];
  videoUrl?: string;
  threeDModelUrl?: string;

  // Shipping
  location?: string;
  address?: ProductAddress;
  shippingCost?: number;
  shippingTime?: string;
  freeShipping?: boolean;
  shippingClass?: ShippingClass;
  requiresShipping?: boolean;
  separateShipping?: boolean;
  shippingDimensions?: Dimensions;
  shippingWeight?: Weight;

  // Relations
  sellerId?: string;
  shopId?: string;
  relatedProducts?: string[];
  crossSellProducts?: string[];
  upSellProducts?: string[];
  bundledProducts?: string[];

  // Availability
  status: ProductStatus;
  ageRestriction?: AgeRestriction;
  availableFrom?: Date | string;
  availableUntil?: Date | string;
  featured?: boolean;
  sticky?: boolean;
  downloadable?: boolean;
  virtual?: boolean;

  // SEO
  seo?: ProductSEO;

  // Warranty & Returns
  warranty?: ProductWarranty;
  returnPolicy?: ProductReturn;
}

// Enhanced update DTO
export interface UpdateProductDtoEnhanced
  extends Partial<CreateProductDtoEnhanced> {
  lastModifiedBy?: string;
}

// Enhanced filters with new fields
export interface ProductFiltersEnhanced extends ProductFilters {
  productType?: ProductType;
  visibility?: ProductVisibility;
  stockManagement?: StockManagement;
  shippingClass?: ShippingClass;
  taxStatus?: TaxStatus;
  featured?: boolean;
  downloadable?: boolean;
  virtual?: boolean;
  sellerId?: string;
  shopId?: string;
  subcategory?: string;
  color?: string;
  size?: string;
  origin?: string;
  hasWarranty?: boolean;
  returnable?: boolean;
}
