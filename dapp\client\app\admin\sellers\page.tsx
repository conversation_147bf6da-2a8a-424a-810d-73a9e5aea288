import {
  BarChart3,
  CheckCircle,
  Clock,
  CreditCard,
  Package,
  Shield,
  Star,
  Store,
  TrendingUp,
  Users,
} from "lucide-react";

import { PageHeaderWrapper } from "@/components/common/PageHeaderWrapper";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

export default function Sellers() {
  return (
    <>
      <PageHeaderWrapper
        title="Sellers Management"
        description="Multi-vendor marketplace management (Coming Soon)"
      />

      <div className="container mx-auto mt-6">
        {/* Coming Soon Section */}
        <Card className="border-2 border-dashed border-gray-300 bg-gray-50/50">
          <CardContent className="p-12 text-center">
            <div className="mx-auto mb-6 flex h-20 w-20 items-center justify-center rounded-full bg-blue-100">
              <Store className="h-10 w-10 text-blue-600" />
            </div>

            <h2 className="mb-4 text-2xl font-bold text-gray-900">
              Multi-Vendor Marketplace
            </h2>

            <p className="mx-auto mb-8 max-w-2xl text-gray-600">
              Transform your e-commerce platform into a thriving marketplace
              where multiple sellers can manage their own products, track sales,
              and grow their businesses. This advanced feature is planned for
              future development.
            </p>

            <Badge
              variant="outline"
              className="mb-8 border-blue-200 bg-blue-50 text-blue-700"
            >
              Coming in Future Release
            </Badge>

            {/* Feature Preview */}
            <div className="mx-auto grid max-w-4xl gap-6 md:grid-cols-2 lg:grid-cols-3">
              <div className="rounded-lg border bg-white p-6 text-left">
                <div className="mb-3 flex h-10 w-10 items-center justify-center rounded-lg bg-green-100">
                  <Users className="h-5 w-5 text-green-600" />
                </div>
                <h3 className="mb-2 font-semibold">Seller Onboarding</h3>
                <p className="text-sm text-gray-600">
                  Streamlined registration, verification, and approval process
                  for new sellers.
                </p>
              </div>

              <div className="rounded-lg border bg-white p-6 text-left">
                <div className="mb-3 flex h-10 w-10 items-center justify-center rounded-lg bg-blue-100">
                  <Package className="h-5 w-5 text-blue-600" />
                </div>
                <h3 className="mb-2 font-semibold">Product Management</h3>
                <p className="text-sm text-gray-600">
                  Individual seller dashboards for managing their product
                  catalogs and inventory.
                </p>
              </div>

              <div className="rounded-lg border bg-white p-6 text-left">
                <div className="mb-3 flex h-10 w-10 items-center justify-center rounded-lg bg-purple-100">
                  <CreditCard className="h-5 w-5 text-purple-600" />
                </div>
                <h3 className="mb-2 font-semibold">Commission System</h3>
                <p className="text-sm text-gray-600">
                  Automated commission calculation and payout management for
                  marketplace revenue.
                </p>
              </div>

              <div className="rounded-lg border bg-white p-6 text-left">
                <div className="mb-3 flex h-10 w-10 items-center justify-center rounded-lg bg-orange-100">
                  <BarChart3 className="h-5 w-5 text-orange-600" />
                </div>
                <h3 className="mb-2 font-semibold">Analytics & Reports</h3>
                <p className="text-sm text-gray-600">
                  Comprehensive analytics for both marketplace owners and
                  individual sellers.
                </p>
              </div>

              <div className="rounded-lg border bg-white p-6 text-left">
                <div className="mb-3 flex h-10 w-10 items-center justify-center rounded-lg bg-red-100">
                  <Shield className="h-5 w-5 text-red-600" />
                </div>
                <h3 className="mb-2 font-semibold">Quality Control</h3>
                <p className="text-sm text-gray-600">
                  Product approval workflows and seller performance monitoring
                  systems.
                </p>
              </div>

              <div className="rounded-lg border bg-white p-6 text-left">
                <div className="mb-3 flex h-10 w-10 items-center justify-center rounded-lg bg-yellow-100">
                  <Star className="h-5 w-5 text-yellow-600" />
                </div>
                <h3 className="mb-2 font-semibold">Seller Ratings</h3>
                <p className="text-sm text-gray-600">
                  Customer feedback and rating system for seller reputation
                  management.
                </p>
              </div>
            </div>

            {/* Current Focus */}
            <div className="mt-12 rounded-lg bg-blue-50 p-6">
              <h3 className="mb-3 text-lg font-semibold text-blue-900">
                Current Focus: Single-Vendor Excellence
              </h3>
              <p className="text-blue-700">
                We're currently perfecting the single-vendor e-commerce
                experience with advanced product management, order processing,
                customer relationships, and promotional tools. This solid
                foundation will enable a seamless transition to multi-vendor
                capabilities.
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Implementation Timeline */}
        <Card className="mt-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Development Roadmap
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <CheckCircle className="h-5 w-5 text-green-600" />
                <span className="font-medium">
                  Phase 1: Core E-commerce Platform
                </span>
                <Badge
                  variant="default"
                  className="bg-green-100 text-green-800"
                >
                  Completed
                </Badge>
              </div>

              <div className="flex items-center gap-3">
                <CheckCircle className="h-5 w-5 text-green-600" />
                <span className="font-medium">
                  Phase 2: Advanced Management Tools
                </span>
                <Badge
                  variant="default"
                  className="bg-green-100 text-green-800"
                >
                  Completed
                </Badge>
              </div>

              <div className="flex items-center gap-3">
                <Clock className="h-5 w-5 text-blue-600" />
                <span className="font-medium">
                  Phase 3: Analytics & Optimization
                </span>
                <Badge
                  variant="outline"
                  className="border-blue-200 bg-blue-50 text-blue-700"
                >
                  In Progress
                </Badge>
              </div>

              <div className="flex items-center gap-3">
                <Clock className="h-5 w-5 text-gray-400" />
                <span className="font-medium">
                  Phase 4: Multi-Vendor Marketplace
                </span>
                <Badge
                  variant="outline"
                  className="border-gray-200 bg-gray-50 text-gray-600"
                >
                  Planned
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </>
  );
}
