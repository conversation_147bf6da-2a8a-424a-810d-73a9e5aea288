import React, { useEffect, useState } from "react";

import clsx from "clsx";
import {
  ChevronDown,
  ChevronRight,
  IndentDecrease,
  IndentIncrease,
  Search,
  Sparkles,
} from "lucide-react";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";

import { NavItemType, NavItems } from "./NavItems";

// Create a custom event for sidebar collapse state changes
const SIDEBAR_COLLAPSE_EVENT = "sidebarCollapseChange";

// Create a function to dispatch the event
const dispatchCollapseEvent = (isCollapsed: boolean) => {
  const event = new CustomEvent(SIDEBAR_COLLAPSE_EVENT, {
    detail: { collapsed: isCollapsed },
  });
  window.dispatchEvent(event);
};

export default function Sidebar() {
  const [openMenus, setOpenMenus] = useState<Record<string, boolean>>({});
  const [collapsed, setCollapsed] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");

  // Notify about collapse state changes
  const handleCollapseToggle = () => {
    const newCollapsedState = !collapsed;
    setCollapsed(newCollapsedState);
    dispatchCollapseEvent(newCollapsedState);
  };

  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    let activeParentLabel: string | null = null;
    for (const item of NavItems) {
      if (item.subItems) {
        const basePath = item.subItems[0]?.href.substring(
          0,
          item.subItems[0].href.lastIndexOf("/")
        );
        if (basePath && pathname.startsWith(basePath + "/")) {
          if (
            item.subItems.some(
              (sub) =>
                pathname === sub.href || pathname.startsWith(sub.href + "/")
            )
          ) {
            activeParentLabel = item.label;
            break;
          }
        }
      }
    }

    if (activeParentLabel) {
      setOpenMenus((prev) => ({ ...prev, [activeParentLabel!]: true }));
    }
    // If you want to close *all other* menus when one becomes active:
    // const newOpenMenus: Record<string, boolean> = {};
    // if (activeParentLabel) {
    //   newOpenMenus[activeParentLabel] = true;
    // }
    // setOpenMenus(newOpenMenus);
  }, [pathname]);

  const toggleMenu = (label: string) => {
    setOpenMenus((prev) => ({ ...prev, [label]: !prev[label] }));
  };

  const handleParentClick = (item: NavItemType) => {
    if (item.subItems?.length) {
      const isMenuOpen = openMenus[item.label];

      if (isMenuOpen) {
        toggleMenu(item.label);
        return;
      }

      toggleMenu(item.label);

      const isAlreadyInSection = item.subItems.some((sub) =>
        pathname.startsWith(sub.href)
      );

      if (!isAlreadyInSection) {
        router.push(item.subItems[0].href);
      }
    } else if (item.href) {
      router.push(item.href);
    }
  };

  // Filter navigation items based on search query
  const filteredNavItems = NavItems.filter((item) => {
    if (!searchQuery) return true;

    const query = searchQuery.toLowerCase();
    const matchesLabel = item.label.toLowerCase().includes(query);
    const matchesDescription = item.description?.toLowerCase().includes(query);
    const matchesSubItems = item.subItems?.some(
      (sub) =>
        sub.label.toLowerCase().includes(query) ||
        sub.description?.toLowerCase().includes(query)
    );

    return matchesLabel || matchesDescription || matchesSubItems;
  });

  return (
    <aside
      className={`fixed left-0 top-0 flex h-screen flex-col border-r bg-white transition-all duration-200 ${
        collapsed ? "w-20" : "w-72"
      }`}
    >
      {/* Header Section */}
      <div className="border-b border-gray-100 p-4">
        <div
          className={clsx("flex items-center justify-between", {
            "px-1": collapsed,
            "px-0": !collapsed,
          })}
        >
          {/* Logo */}
          {!collapsed && (
            <Link href={"/admin/dashboard"}>
              <div className="flex items-center gap-3">
                <div className="rounded-lg bg-gradient-to-br from-blue-600 to-blue-700 p-2.5 text-white shadow-lg">
                  <svg width="20" height="20" fill="currentColor">
                    <circle cx="10" cy="10" r="8" />
                  </svg>
                </div>
                <div>
                  <div className="text-lg font-bold text-gray-900">Admin</div>
                  <div className="-mt-1 text-xs text-gray-500">
                    E-commerce Dashboard
                  </div>
                </div>
              </div>
            </Link>
          )}

          {/* Toggle collapse button */}
          <button
            className="rounded-lg p-2 text-gray-600 hover:bg-gray-100 hover:text-gray-900"
            onClick={handleCollapseToggle}
          >
            {collapsed ? (
              <IndentIncrease size={18} />
            ) : (
              <IndentDecrease size={18} />
            )}
          </button>
        </div>

        {/* Navigation Search */}
        {!collapsed && (
          <div className="relative mt-4">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Find navigation..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full rounded-lg border border-gray-200 bg-gray-50 py-2 pl-10 pr-4 text-sm focus:border-blue-500 focus:bg-white focus:outline-none focus:ring-1 focus:ring-blue-500"
            />
            {searchQuery && (
              <div className="absolute right-3 top-1/2 -translate-y-1/2">
                <button
                  onClick={() => setSearchQuery("")}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <span className="text-xs">✕</span>
                </button>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Navigation */}
      <nav className="flex-1 space-y-1 overflow-y-auto p-4 text-sm">
        {filteredNavItems.map((item, index) => {
          const isParentItselfActive = !!item.href && pathname === item.href;
          const isParentHighlighted = isParentItselfActive;

          return (
            <div key={index} className="group">
              {/* Parent item Button/Link */}
              <div className="relative">
                <button
                  onClick={() => handleParentClick(item)}
                  className={clsx(
                    "flex w-full items-center rounded-lg px-3 py-2.5 transition-all duration-150 hover:bg-gray-50",
                    {
                      "border border-blue-200 bg-blue-50 font-medium text-blue-700 shadow-sm":
                        isParentHighlighted,
                      "text-gray-700 hover:text-gray-900": !isParentHighlighted,
                      "justify-center": collapsed,
                    }
                  )}
                  title={collapsed ? item.label : undefined}
                >
                  <span
                    className={clsx("flex-shrink-0", { "mr-3": !collapsed })}
                  >
                    {item.icon}
                  </span>

                  {!collapsed && (
                    <>
                      <div className="flex-1 text-left">
                        <div className="flex items-center gap-2">
                          <span className="truncate font-medium">
                            {item.label}
                          </span>
                          {item.isNew && (
                            <span className="inline-flex items-center gap-1 rounded-full bg-green-100 px-2 py-0.5 text-xs font-medium text-green-700">
                              <Sparkles className="h-3 w-3" />
                              New
                            </span>
                          )}
                        </div>
                        {item.description && !searchQuery && (
                          <div className="mt-0.5 text-xs text-gray-500">
                            {item.description}
                          </div>
                        )}
                      </div>

                      <div className="flex items-center gap-1">
                        {item.badge && (
                          <span className="rounded-full bg-red-500 px-2 py-0.5 text-xs font-medium text-white">
                            {item.badge}
                          </span>
                        )}
                        {item.subItems && (
                          <span className="text-gray-400">
                            {openMenus[item.label] ? (
                              <ChevronDown size={16} />
                            ) : (
                              <ChevronRight size={16} />
                            )}
                          </span>
                        )}
                      </div>
                    </>
                  )}
                </button>

                {/* Tooltip for collapsed state */}
                {collapsed && (
                  <div className="absolute left-full top-1/2 z-50 ml-2 hidden -translate-y-1/2 group-hover:block">
                    <div className="rounded-lg bg-gray-900 px-3 py-2 text-sm text-white shadow-lg">
                      <div className="font-medium">{item.label}</div>
                      {item.description && (
                        <div className="mt-1 text-xs text-gray-300">
                          {item.description}
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>

              {/* Sub-items */}
              {!collapsed && item.subItems && openMenus[item.label] && (
                <div className="ml-6 mt-2 space-y-1 border-l-2 border-gray-100 pl-4">
                  {item.subItems.map((sub, i) => {
                    const isSubActive = pathname === sub.href;
                    return (
                      <Link key={i} href={sub.href} passHref legacyBehavior>
                        <a
                          className={clsx(
                            "flex w-full items-center rounded-lg px-3 py-2 text-left transition-all duration-150 hover:bg-gray-50",
                            {
                              "border border-blue-200 bg-blue-50 font-medium text-blue-600 shadow-sm":
                                isSubActive,
                              "text-gray-600 hover:text-gray-900": !isSubActive,
                            }
                          )}
                        >
                          {sub.icon && (
                            <span className="mr-3 flex-shrink-0 text-gray-400">
                              {sub.icon}
                            </span>
                          )}

                          <div className="flex-1">
                            <div className="font-medium">{sub.label}</div>
                            {sub.description && (
                              <div className="mt-0.5 text-xs text-gray-500">
                                {sub.description}
                              </div>
                            )}
                          </div>

                          {sub.badge && (
                            <span className="rounded-full bg-orange-500 px-2 py-0.5 text-xs font-medium text-white">
                              {sub.badge}
                            </span>
                          )}
                        </a>
                      </Link>
                    );
                  })}
                </div>
              )}

              {/* Divider line */}
              {item.isDividerAfter && !collapsed && (
                <div className="my-4 border-t border-gray-200" />
              )}
            </div>
          );
        })}
      </nav>
    </aside>
  );
}
