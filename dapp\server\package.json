{"name": "ts-sever-example", "version": "1.0.0", "type": "module", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "tsx watch src/index.ts", "start": "tsx src/index.ts", "build": "tsc", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write .", "add-test-product": "tsx src/scripts/add-test-product.ts", "seed-categories": "tsx src/scripts/seed-categories.ts"}, "keywords": [], "author": "", "license": "ISC", "packageManager": "pnpm@9.15.4+sha512.b2dc20e2fc72b3e18848459b37359a32064663e5627a51e4c74b2c29dd8e8e0491483c3abb40789cfd578bf362fb6ba8261b05f0387d76792ed6e23ea3b1b6a0", "dependencies": {"@types/cors": "^2.8.17", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.8", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "helmet": "^8.0.0", "http-status-codes": "^2.3.0", "mongoose": "^8.12.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1"}, "devDependencies": {"@eslint/js": "^9.22.0", "@trivago/prettier-plugin-sort-imports": "^5.2.2", "@types/express": "^5.0.0", "@types/node": "^22.13.10", "eslint": "^9.22.0", "eslint-config-prettier": "10.0.2", "eslint-plugin-prettier": "^5.2.3", "globals": "^16.0.0", "prettier": "^3.5.3", "tsx": "^4.19.3", "typescript": "^5.8.2", "typescript-eslint": "^8.26.1"}}