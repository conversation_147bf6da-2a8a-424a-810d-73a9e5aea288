import { useState } from "react";

import { format } from "date-fns";
import {
  Alert<PERSON>ircle,
  CalendarIcon,
  Calendar as CalendarL<PERSON><PERSON>,
  CheckCircle,
  Clock,
  Eye,
  EyeOff,
  Globe,
  Info,
  Timer,
  Users,
  Zap,
} from "lucide-react";
import {
  FieldErrors,
  UseFormRegister,
  UseFormSetValue,
  UseFormWatch,
} from "react-hook-form";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { ProductFormData } from "@/schemas/productSchema";

import { FormField } from "../../../common/FormField";
import { CollapsibleSection } from "../../../ui/collapsible-section";

type AvailabilitySectionProps = {
  register: UseFormRegister<ProductFormData>;
  errors: FieldErrors<ProductFormData>;
  setValue: UseFormSetValue<ProductFormData>;
  watch: UseFormWatch<ProductFormData>;
};

/**
 * Availability section of the product form
 * Handles product status, age restrictions, and availability dates
 */
export const AvailabilitySection = ({
  register,
  errors,
  setValue,
  watch,
}: AvailabilitySectionProps) => {
  const [availableFromOpen, setAvailableFromOpen] = useState(false);
  const [availableUntilOpen, setAvailableUntilOpen] = useState(false);

  const availableFrom = watch("availableFrom");
  const availableUntil = watch("availableUntil");
  const isPublished = watch("isPublished");
  const featured = watch("featured");
  const sticky = watch("sticky");

  // Calculate availability status
  const getAvailabilityStatus = () => {
    const now = new Date();
    const fromDate = availableFrom ? new Date(availableFrom) : null;
    const untilDate = availableUntil ? new Date(availableUntil) : null;

    if (fromDate && fromDate > now)
      return { status: "scheduled", color: "blue", icon: Timer };
    if (untilDate && untilDate < now)
      return { status: "expired", color: "red", icon: AlertCircle };
    if (isPublished)
      return { status: "live", color: "green", icon: CheckCircle };
    return { status: "draft", color: "gray", icon: EyeOff };
  };

  const availabilityStatus = getAvailabilityStatus();

  return (
    <div className="space-y-8">
      {/* Section Header */}
      <div className="flex items-center gap-3 border-b border-gray-200 pb-4">
        <div className="rounded-full bg-green-100 p-2">
          <Clock className="h-5 w-5 text-green-600" />
        </div>
        <div>
          <h2 className="text-2xl font-bold text-gray-900">
            Availability & Launch
          </h2>
          <p className="text-gray-600">
            Configure when and how your product becomes available
          </p>
        </div>
      </div>

      {/* Availability Status Card */}
      <Card className="border-l-4 border-l-green-500">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <CheckCircle className="h-5 w-5 text-green-600" />
            Publication Status
            <Badge variant="secondary" className="text-xs">
              Optional
            </Badge>
          </CardTitle>
          <p className="text-sm text-gray-600">
            Control whether your product is live and visible to customers
          </p>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Published Toggle */}
            <div className="flex items-center space-x-3">
              <Switch
                id="is-published"
                checked={isPublished}
                onCheckedChange={(checked) => setValue("isPublished", checked)}
              />
              <Label htmlFor="is-published" className="text-base font-medium">
                Publish this product immediately
              </Label>
            </div>

            {/* Current Status Display */}
            <div
              className={`rounded-lg border p-3 ${
                availabilityStatus.color === "green"
                  ? "border-green-200 bg-green-50"
                  : availabilityStatus.color === "blue"
                    ? "border-blue-200 bg-blue-50"
                    : availabilityStatus.color === "red"
                      ? "border-red-200 bg-red-50"
                      : "border-gray-200 bg-gray-50"
              }`}
            >
              <div className="flex items-center gap-2">
                <availabilityStatus.icon
                  className={`h-4 w-4 ${
                    availabilityStatus.color === "green"
                      ? "text-green-600"
                      : availabilityStatus.color === "blue"
                        ? "text-blue-600"
                        : availabilityStatus.color === "red"
                          ? "text-red-600"
                          : "text-gray-600"
                  }`}
                />
                <span
                  className={`font-medium ${
                    availabilityStatus.color === "green"
                      ? "text-green-900"
                      : availabilityStatus.color === "blue"
                        ? "text-blue-900"
                        : availabilityStatus.color === "red"
                          ? "text-red-900"
                          : "text-gray-900"
                  }`}
                >
                  {availabilityStatus.status === "live" && "Product is Live"}
                  {availabilityStatus.status === "draft" &&
                    "Product is in Draft"}
                  {availabilityStatus.status === "scheduled" &&
                    "Product is Scheduled"}
                  {availabilityStatus.status === "expired" &&
                    "Product has Expired"}
                </span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Launch Schedule - Collapsible */}
      <CollapsibleSection
        title="Launch Schedule"
        description="Schedule when your product becomes available and when to remove it"
        icon={<CalendarLucide className="h-5 w-5 text-blue-600" />}
        borderColor="border-l-blue-500"
        defaultOpen={false}
        isOptional={true}
      >
        <div className="space-y-4">
          {/* Available From Date */}
          <FormField
            id="availableFrom"
            label="Launch Date (Available From)"
            error={errors.availableFrom?.message}
            optional={true}
          >
            <Popover
              open={availableFromOpen}
              onOpenChange={setAvailableFromOpen}
            >
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className="w-full justify-start border-2 text-left font-normal focus:border-blue-500"
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {availableFrom
                    ? format(new Date(availableFrom), "PPP")
                    : "Select launch date"}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <Calendar
                  mode="single"
                  selected={availableFrom ? new Date(availableFrom) : undefined}
                  onSelect={(date) => {
                    setValue(
                      "availableFrom",
                      date ? date.toISOString() : undefined
                    );
                    setAvailableFromOpen(false);
                  }}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </FormField>

          {/* Available Until Date */}
          <FormField
            id="availableUntil"
            label="End Date (Available Until)"
            error={errors.availableUntil?.message}
            optional={true}
          >
            <Popover
              open={availableUntilOpen}
              onOpenChange={setAvailableUntilOpen}
            >
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className="w-full justify-start border-2 text-left font-normal focus:border-blue-500"
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {availableUntil
                    ? format(new Date(availableUntil), "PPP")
                    : "Select end date"}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <Calendar
                  mode="single"
                  selected={
                    availableUntil ? new Date(availableUntil) : undefined
                  }
                  onSelect={(date) => {
                    setValue(
                      "availableUntil",
                      date ? date.toISOString() : undefined
                    );
                    setAvailableUntilOpen(false);
                  }}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </FormField>

          {/* Schedule Tips */}
          <div className="rounded-lg border border-blue-200 bg-blue-50 p-3">
            <div className="flex items-start gap-2">
              <Timer className="mt-0.5 h-4 w-4 text-blue-600" />
              <div className="text-sm text-blue-800">
                <strong>Scheduling Tip:</strong> Use launch dates for product
                releases, seasonal items, or limited-time offers.
              </div>
            </div>
          </div>
        </div>
      </CollapsibleSection>

      {/* Availability Summary Card */}
      <Card className="border-green-200 bg-gradient-to-r from-green-50 to-blue-50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-green-800">
            <Globe className="h-5 w-5" />
            Availability Summary
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <div className="flex items-center gap-3">
              <div className="rounded-full bg-green-100 p-2">
                <CheckCircle className="h-4 w-4 text-green-600" />
              </div>
              <div>
                <h4 className="font-medium text-green-900">Status</h4>
                <p className="text-sm text-green-700">
                  {isPublished ? "Published" : "Draft"}
                </p>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <div className="rounded-full bg-blue-100 p-2">
                <CalendarLucide className="h-4 w-4 text-blue-600" />
              </div>
              <div>
                <h4 className="font-medium text-blue-900">Launch Date</h4>
                <p className="text-sm text-blue-700">
                  {availableFrom
                    ? format(new Date(availableFrom), "MMM dd, yyyy")
                    : "Immediate"}
                </p>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <div className="rounded-full bg-purple-100 p-2">
                <Zap className="h-4 w-4 text-purple-600" />
              </div>
              <div>
                <h4 className="font-medium text-purple-900">Marketing</h4>
                <p className="text-sm text-purple-700">
                  {featured || sticky
                    ? `${featured ? "Featured" : ""} ${sticky ? "Sticky" : ""}`.trim()
                    : "Standard"}
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
