"use client";

import React from "react";

import { format } from "date-fns";
import { Edit, Eye, MoreHorizontal, Trash2, User } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

type OrderRowProps = {
  order: {
    id: string;
    customer: {
      name: string;
      email: string;
      avatar: string;
    };
    items: Array<{
      name: string;
      quantity: number;
      image: string;
    }>;
    totalAmount: number;
    status: string;
    shippingStatus: string;
    placedAt: string;
    updatedAt: string;
  };
};

const getStatusColor = (status: string) => {
  switch (status) {
    case "paid":
      return "bg-green-100 text-green-700 hover:bg-green-200";
    case "pending":
      return "bg-yellow-100 text-yellow-700 hover:bg-yellow-200";
    case "shipped":
      return "bg-blue-100 text-blue-700 hover:bg-blue-200";
    case "delivered":
      return "bg-emerald-100 text-emerald-700 hover:bg-emerald-200";
    case "cancelled":
      return "bg-red-100 text-red-700 hover:bg-red-200";
    case "refunded":
      return "bg-purple-100 text-purple-700 hover:bg-purple-200";
    default:
      return "bg-gray-100 text-gray-700 hover:bg-gray-200";
  }
};

const getShippingStatusColor = (status: string) => {
  switch (status) {
    case "shipped":
      return "bg-blue-100 text-blue-700 hover:bg-blue-200";
    case "delivered":
      return "bg-green-100 text-green-700 hover:bg-green-200";
    case "pending":
      return "bg-yellow-100 text-yellow-700 hover:bg-yellow-200";
    case "processing":
      return "bg-orange-100 text-orange-700 hover:bg-orange-200";
    case "returned":
      return "bg-purple-100 text-purple-700 hover:bg-purple-200";
    case "cancelled":
      return "bg-red-100 text-red-700 hover:bg-red-200";
    default:
      return "bg-gray-100 text-gray-700 hover:bg-gray-200";
  }
};

export const OrderRow = ({ order }: OrderRowProps) => {
  const router = useRouter();
  const totalItems = order.items.reduce((sum, item) => sum + item.quantity, 0);
  const displayItems = order.items.slice(0, 3); // Show max 3 items
  const remainingItems = order.items.length - 3;

  const handleViewDetails = () => {
    router.push(`/admin/orders/${order.id}`);
  };

  const handleEditOrder = () => {
    // TODO: Implement edit functionality
    console.log("Edit order:", order.id);
  };

  const handleCancelOrder = () => {
    // TODO: Implement cancel functionality
    console.log("Cancel order:", order.id);
  };

  return (
    <tr
      className="cursor-pointer border-t transition-colors hover:bg-gray-50/50"
      onClick={handleViewDetails}
    >
      {/* Order ID */}
      <td className="p-4" onClick={(e) => e.stopPropagation()}>
        <Link
          href={`/admin/orders/${order.id}`}
          className="font-medium text-blue-600 hover:text-blue-800 hover:underline"
        >
          #{order.id}
        </Link>
      </td>

      {/* Customer */}
      <td className="p-4">
        <div className="flex items-center gap-3">
          <div className="flex h-8 w-8 shrink-0 items-center justify-center rounded-full bg-gray-100">
            <User className="h-4 w-4 text-gray-500" />
          </div>
          <div>
            <div className="font-medium text-gray-900">
              {order.customer.name}
            </div>
            <div className="text-sm text-gray-500">{order.customer.email}</div>
          </div>
        </div>
      </td>

      {/* Products */}
      <td className="p-4">
        <div className="flex items-center gap-2">
          <div className="flex -space-x-2">
            {displayItems.map((item, index) => (
              <div
                key={index}
                className="flex h-8 w-8 shrink-0 overflow-hidden rounded-full border-2 border-white bg-gray-100"
              >
                <Image
                  src={item.image}
                  alt={item.name}
                  width={32}
                  height={32}
                  className="object-cover"
                />
              </div>
            ))}
            {remainingItems > 0 && (
              <div className="flex h-8 w-8 shrink-0 items-center justify-center rounded-full border-2 border-white bg-gray-200 text-xs font-medium text-gray-600">
                +{remainingItems}
              </div>
            )}
          </div>
          <span className="text-sm text-gray-500">
            {totalItems} item{totalItems !== 1 ? "s" : ""}
          </span>
        </div>
      </td>

      {/* Status */}
      <td className="p-4 text-center">
        <Badge
          className={`cursor-default font-normal ${getStatusColor(order.status)}`}
        >
          {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
        </Badge>
      </td>

      {/* Shipping Status */}
      <td className="p-4 text-center">
        <Badge
          className={`cursor-default font-normal ${getShippingStatusColor(order.shippingStatus)}`}
        >
          {order.shippingStatus.charAt(0).toUpperCase() +
            order.shippingStatus.slice(1)}
        </Badge>
      </td>

      {/* Total */}
      <td className="p-4 text-right">
        <div className="font-medium text-gray-900">
          ${order.totalAmount.toFixed(2)}
        </div>
      </td>

      {/* Date */}
      <td className="p-4 text-center">
        <div className="text-sm text-gray-900">
          {format(new Date(order.placedAt), "MMM dd, yyyy")}
        </div>
        <div className="text-xs text-gray-500">
          {format(new Date(order.placedAt), "HH:mm")}
        </div>
      </td>

      {/* Actions */}
      <td className="p-4 text-center" onClick={(e) => e.stopPropagation()}>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={handleViewDetails}>
              <Eye className="mr-2 h-4 w-4" />
              View Details
            </DropdownMenuItem>
            <DropdownMenuItem onClick={handleEditOrder}>
              <Edit className="mr-2 h-4 w-4" />
              Edit Order
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              className="text-red-600"
              onClick={handleCancelOrder}
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Cancel Order
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </td>
    </tr>
  );
};
