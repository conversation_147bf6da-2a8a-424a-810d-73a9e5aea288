"use client";

import { useState } from "react";

import {
  Archive,
  Atom,
  Copy,
  Edit,
  Eye,
  Filter,
  Grid3X3,
  Layers,
  List,
  MoreHorizontal,
  Package,
  Pencil,
  Plus,
  Recycle,
  Save,
  Search,
  Shield,
  SortAsc,
  SortDesc,
  Sparkles,
  Tag,
  Trash2,
  TrendingUp,
  X,
} from "lucide-react";
import { toast } from "sonner";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";

export type Material = {
  id: string;
  name: string;
  description: string;
  slug: string;
  icon?: string;
  color?: string;
  type: "natural" | "synthetic" | "composite" | "recycled";
  isActive: boolean;
  isEcoFriendly: boolean;
  isDurable: boolean;
  productCount: number;
  properties: string[];
  careInstructions?: string;
  origin?: string;
  sortOrder: number;
  createdAt: string;
  updatedAt: string;
};

// Mock materials with enhanced data
const mockMaterials: Material[] = [
  {
    id: "mat-1",
    name: "Organic Cotton",
    description:
      "100% organic cotton grown without harmful chemicals or pesticides",
    slug: "organic-cotton",
    icon: "🌱",
    color: "#10B981",
    type: "natural",
    isActive: true,
    isEcoFriendly: true,
    isDurable: true,
    productCount: 156,
    properties: ["Breathable", "Soft", "Hypoallergenic", "Biodegradable"],
    careInstructions: "Machine wash cold, tumble dry low",
    origin: "India",
    sortOrder: 1,
    createdAt: "2024-01-01T00:00:00Z",
    updatedAt: "2024-01-15T10:30:00Z",
  },
  {
    id: "mat-2",
    name: "Recycled Polyester",
    description: "High-quality polyester made from recycled plastic bottles",
    slug: "recycled-polyester",
    icon: "♻️",
    color: "#3B82F6",
    type: "recycled",
    isActive: true,
    isEcoFriendly: true,
    isDurable: true,
    productCount: 234,
    properties: ["Water-resistant", "Quick-dry", "Lightweight", "Durable"],
    careInstructions: "Machine wash warm, hang dry",
    origin: "Global",
    sortOrder: 2,
    createdAt: "2024-01-02T00:00:00Z",
    updatedAt: "2024-01-14T16:45:00Z",
  },
  {
    id: "mat-3",
    name: "Genuine Leather",
    description: "Premium full-grain leather from ethically sourced cattle",
    slug: "genuine-leather",
    icon: "🐄",
    color: "#92400E",
    type: "natural",
    isActive: true,
    isEcoFriendly: false,
    isDurable: true,
    productCount: 89,
    properties: ["Durable", "Flexible", "Ages beautifully", "Water-resistant"],
    careInstructions: "Clean with leather conditioner, avoid water",
    origin: "Italy",
    sortOrder: 3,
    createdAt: "2024-01-03T00:00:00Z",
    updatedAt: "2024-01-13T09:20:00Z",
  },
  {
    id: "mat-4",
    name: "Bamboo Fiber",
    description:
      "Sustainable bamboo fiber with natural antibacterial properties",
    slug: "bamboo-fiber",
    icon: "🎋",
    color: "#059669",
    type: "natural",
    isActive: true,
    isEcoFriendly: true,
    isDurable: false,
    productCount: 67,
    properties: ["Antibacterial", "Moisture-wicking", "Soft", "Renewable"],
    careInstructions: "Gentle machine wash, air dry",
    origin: "China",
    sortOrder: 4,
    createdAt: "2024-01-04T00:00:00Z",
    updatedAt: "2024-01-12T14:15:00Z",
  },
  {
    id: "mat-5",
    name: "Carbon Fiber",
    description: "Ultra-lightweight and strong carbon fiber composite material",
    slug: "carbon-fiber",
    icon: "⚫",
    color: "#1F2937",
    type: "composite",
    isActive: true,
    isEcoFriendly: false,
    isDurable: true,
    productCount: 23,
    properties: [
      "Ultra-light",
      "High strength",
      "Corrosion resistant",
      "Conductive",
    ],
    careInstructions: "Wipe clean with dry cloth, avoid harsh chemicals",
    origin: "Japan",
    sortOrder: 5,
    createdAt: "2024-01-05T00:00:00Z",
    updatedAt: "2024-01-11T11:30:00Z",
  },
  {
    id: "mat-6",
    name: "Merino Wool",
    description:
      "Premium merino wool known for its softness and temperature regulation",
    slug: "merino-wool",
    icon: "🐑",
    color: "#F59E0B",
    type: "natural",
    isActive: false,
    isEcoFriendly: true,
    isDurable: true,
    productCount: 45,
    properties: [
      "Temperature regulating",
      "Odor resistant",
      "Soft",
      "Moisture-wicking",
    ],
    careInstructions: "Hand wash or gentle cycle, lay flat to dry",
    origin: "New Zealand",
    sortOrder: 6,
    createdAt: "2024-01-06T00:00:00Z",
    updatedAt: "2024-01-10T08:45:00Z",
  },
];

type MaterialManagerProps = {
  initialMaterials?: Material[];
  onMaterialsChange?: (materials: Material[]) => void;
};

/**
 * Enhanced component for managing product materials with professional UI
 */
export const MaterialManagerEnhanced = ({
  initialMaterials = mockMaterials,
  onMaterialsChange,
}: MaterialManagerProps) => {
  const [materials, setMaterials] = useState<Material[]>(initialMaterials);
  const [newMaterial, setNewMaterial] = useState<Partial<Material>>({
    name: "",
    description: "",
    icon: "",
    color: "#10B981",
    type: "natural",
    isActive: true,
    isEcoFriendly: false,
    isDurable: true,
    properties: [],
    careInstructions: "",
    origin: "",
  });
  const [editingMaterialId, setEditingMaterialId] = useState<string | null>(
    null
  );
  const [editForm, setEditForm] = useState<Partial<Material>>({});
  const [searchQuery, setSearchQuery] = useState("");
  const [filterStatus, setFilterStatus] = useState("all");
  const [filterType, setFilterType] = useState("all");
  const [filterEco, setFilterEco] = useState("all");
  const [sortBy, setSortBy] = useState("name");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("asc");
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [showAddForm, setShowAddForm] = useState(false);

  // Generate a slug from the material name
  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, "-")
      .replace(/^-|-$/g, "");
  };

  // Filter and sort materials
  const filteredAndSortedMaterials = materials
    .filter((material) => {
      const matchesSearch =
        material.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        material.description
          .toLowerCase()
          .includes(searchQuery.toLowerCase()) ||
        material.properties.some((prop) =>
          prop.toLowerCase().includes(searchQuery.toLowerCase())
        );

      const matchesStatus =
        filterStatus === "all" ||
        (filterStatus === "active" && material.isActive) ||
        (filterStatus === "inactive" && !material.isActive);

      const matchesType = filterType === "all" || material.type === filterType;

      const matchesEco =
        filterEco === "all" ||
        (filterEco === "eco" && material.isEcoFriendly) ||
        (filterEco === "non-eco" && !material.isEcoFriendly);

      return matchesSearch && matchesStatus && matchesType && matchesEco;
    })
    .sort((a, b) => {
      let comparison = 0;
      switch (sortBy) {
        case "name":
          comparison = a.name.localeCompare(b.name);
          break;
        case "products":
          comparison = a.productCount - b.productCount;
          break;
        case "type":
          comparison = a.type.localeCompare(b.type);
          break;
        case "created":
          comparison =
            new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
          break;
        default:
          comparison = a.sortOrder - b.sortOrder;
      }
      return sortOrder === "asc" ? comparison : -comparison;
    });

  // Add a new material
  const handleAddMaterial = () => {
    if (!newMaterial.name) {
      toast.error("Material name is required");
      return;
    }

    const slug = generateSlug(newMaterial.name);

    // Check if slug already exists
    if (materials.some((material) => material.slug === slug)) {
      toast.error("A material with this name already exists");
      return;
    }

    const newMaterialWithId: Material = {
      id: `material-${Date.now()}`,
      name: newMaterial.name,
      description: newMaterial.description || "",
      slug,
      icon: newMaterial.icon || "🧵",
      color: newMaterial.color || "#10B981",
      type: newMaterial.type || "natural",
      isActive: newMaterial.isActive ?? true,
      isEcoFriendly: newMaterial.isEcoFriendly ?? false,
      isDurable: newMaterial.isDurable ?? true,
      productCount: 0,
      properties: newMaterial.properties || [],
      careInstructions: newMaterial.careInstructions || "",
      origin: newMaterial.origin || "",
      sortOrder: materials.length + 1,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    const updatedMaterials = [...materials, newMaterialWithId];
    setMaterials(updatedMaterials);
    setNewMaterial({
      name: "",
      description: "",
      icon: "",
      color: "#10B981",
      type: "natural",
      isActive: true,
      isEcoFriendly: false,
      isDurable: true,
      properties: [],
      careInstructions: "",
      origin: "",
    });
    setShowAddForm(false);

    // Notify parent component
    if (onMaterialsChange) {
      onMaterialsChange(updatedMaterials);
    }

    toast.success("Material added successfully");
  };

  // Start editing a material
  const handleEditStart = (material: Material) => {
    setEditingMaterialId(material.id);
    setEditForm({ ...material });
  };

  // Cancel editing
  const handleEditCancel = () => {
    setEditingMaterialId(null);
    setEditForm({});
  };

  // Save edited material
  const handleEditSave = () => {
    if (!editForm.name) {
      toast.error("Material name is required");
      return;
    }

    const updatedMaterials = materials.map((material) =>
      material.id === editingMaterialId
        ? {
            ...material,
            name: editForm.name || material.name,
            description: editForm.description || material.description,
            icon: editForm.icon || material.icon,
            color: editForm.color || material.color,
            type: editForm.type || material.type,
            isActive: editForm.isActive ?? material.isActive,
            isEcoFriendly: editForm.isEcoFriendly ?? material.isEcoFriendly,
            isDurable: editForm.isDurable ?? material.isDurable,
            properties: editForm.properties || material.properties,
            careInstructions:
              editForm.careInstructions || material.careInstructions,
            origin: editForm.origin || material.origin,
            // Only update slug if name changed
            slug:
              material.name !== editForm.name
                ? generateSlug(editForm.name)
                : material.slug,
            updatedAt: new Date().toISOString(),
          }
        : material
    );

    setMaterials(updatedMaterials);
    setEditingMaterialId(null);
    setEditForm({});

    // Notify parent component
    if (onMaterialsChange) {
      onMaterialsChange(updatedMaterials);
    }

    toast.success("Material updated successfully");
  };

  // Delete a material
  const handleDeleteMaterial = (materialId: string) => {
    const updatedMaterials = materials.filter(
      (material) => material.id !== materialId
    );
    setMaterials(updatedMaterials);

    // Notify parent component
    if (onMaterialsChange) {
      onMaterialsChange(updatedMaterials);
    }

    toast.success("Material deleted successfully");
  };

  return (
    <div className="space-y-6">
      {/* Header with Statistics */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="rounded-full bg-purple-100 p-2">
                <Layers className="h-5 w-5 text-purple-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Total Materials</p>
                <p className="text-2xl font-bold">{materials.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="rounded-full bg-green-100 p-2">
                <Recycle className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Eco-Friendly</p>
                <p className="text-2xl font-bold">
                  {materials.filter((m) => m.isEcoFriendly).length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="rounded-full bg-blue-100 p-2">
                <Shield className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Durable</p>
                <p className="text-2xl font-bold">
                  {materials.filter((m) => m.isDurable).length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="rounded-full bg-orange-100 p-2">
                <Atom className="h-5 w-5 text-orange-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Natural</p>
                <p className="text-2xl font-bold">
                  {materials.filter((m) => m.type === "natural").length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Controls */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
            {/* Search and Filters */}
            <div className="flex flex-1 gap-4">
              <div className="relative max-w-md flex-1">
                <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
                <Input
                  placeholder="Search materials..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>

              <Select value={filterStatus} onValueChange={setFilterStatus}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                </SelectContent>
              </Select>

              <Select value={filterType} onValueChange={setFilterType}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="natural">Natural</SelectItem>
                  <SelectItem value="synthetic">Synthetic</SelectItem>
                  <SelectItem value="composite">Composite</SelectItem>
                  <SelectItem value="recycled">Recycled</SelectItem>
                </SelectContent>
              </Select>

              <Select value={filterEco} onValueChange={setFilterEco}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Eco</SelectItem>
                  <SelectItem value="eco">Eco-Friendly</SelectItem>
                  <SelectItem value="non-eco">Standard</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* View Controls */}
            <div className="flex items-center gap-2">
              <Button
                variant={sortOrder === "asc" ? "default" : "outline"}
                size="sm"
                onClick={() =>
                  setSortOrder(sortOrder === "asc" ? "desc" : "asc")
                }
              >
                {sortOrder === "asc" ? (
                  <SortAsc className="h-4 w-4" />
                ) : (
                  <SortDesc className="h-4 w-4" />
                )}
              </Button>

              <div className="flex rounded-md border">
                <Button
                  variant={viewMode === "grid" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setViewMode("grid")}
                  className="rounded-r-none"
                >
                  <Grid3X3 className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === "list" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setViewMode("list")}
                  className="rounded-l-none"
                >
                  <List className="h-4 w-4" />
                </Button>
              </div>

              <Button onClick={() => setShowAddForm(true)}>
                <Plus className="mr-2 h-4 w-4" />
                Add Material
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Materials Display - Grid View */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {filteredAndSortedMaterials.map((material) => (
          <Card key={material.id} className="transition-shadow hover:shadow-md">
            <CardContent className="p-4">
              <div className="space-y-3">
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-3">
                    <span className="text-3xl">{material.icon}</span>
                    <div>
                      <h3 className="font-semibold">{material.name}</h3>
                      <div className="flex flex-wrap gap-1">
                        <Badge
                          variant={material.isActive ? "default" : "secondary"}
                          className="text-xs"
                        >
                          {material.isActive ? "Active" : "Inactive"}
                        </Badge>
                        <Badge
                          variant="outline"
                          className="text-xs"
                          style={{
                            borderColor: material.color,
                            color: material.color,
                          }}
                        >
                          {material.type}
                        </Badge>
                        {material.isEcoFriendly && (
                          <Badge
                            variant="outline"
                            className="border-green-200 text-xs text-green-600"
                          >
                            Eco-Friendly
                          </Badge>
                        )}
                        {material.isDurable && (
                          <Badge
                            variant="outline"
                            className="border-blue-200 text-xs text-blue-600"
                          >
                            Durable
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>

                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem
                        onClick={() => handleEditStart(material)}
                      >
                        <Edit className="mr-2 h-4 w-4" />
                        Edit
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <Eye className="mr-2 h-4 w-4" />
                        View Products
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <Copy className="mr-2 h-4 w-4" />
                        Duplicate
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem>
                        <Archive className="mr-2 h-4 w-4" />
                        Archive
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => handleDeleteMaterial(material.id)}
                        className="text-red-600"
                      >
                        <Trash2 className="mr-2 h-4 w-4" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>

                <p className="line-clamp-2 text-sm text-gray-600">
                  {material.description || "No description provided"}
                </p>

                <div className="space-y-2 text-xs text-gray-500">
                  <div className="flex items-center justify-between">
                    <span>{material.productCount} products</span>
                    {material.origin && <span>Origin: {material.origin}</span>}
                  </div>

                  {material.properties.length > 0 && (
                    <div className="flex flex-wrap gap-1">
                      {material.properties
                        .slice(0, 3)
                        .map((property, index) => (
                          <span
                            key={index}
                            className="rounded bg-gray-100 px-2 py-1 text-xs"
                          >
                            {property}
                          </span>
                        ))}
                      {material.properties.length > 3 && (
                        <span className="rounded bg-gray-100 px-2 py-1 text-xs">
                          +{material.properties.length - 3} more
                        </span>
                      )}
                    </div>
                  )}

                  {material.careInstructions && (
                    <p className="text-xs italic text-gray-400">
                      Care: {material.careInstructions}
                    </p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Empty State */}
      {filteredAndSortedMaterials.length === 0 && (
        <Card>
          <CardContent className="p-12 text-center">
            <Layers className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-lg font-medium text-gray-900">
              No materials found
            </h3>
            <p className="mt-1 text-sm text-gray-500">
              {searchQuery ||
              filterStatus !== "all" ||
              filterType !== "all" ||
              filterEco !== "all"
                ? "Try adjusting your search or filter criteria."
                : "Get started by adding your first material."}
            </p>
            {!searchQuery &&
              filterStatus === "all" &&
              filterType === "all" &&
              filterEco === "all" && (
                <Button className="mt-4" onClick={() => setShowAddForm(true)}>
                  <Plus className="mr-2 h-4 w-4" />
                  Add Your First Material
                </Button>
              )}
          </CardContent>
        </Card>
      )}
    </div>
  );
};
