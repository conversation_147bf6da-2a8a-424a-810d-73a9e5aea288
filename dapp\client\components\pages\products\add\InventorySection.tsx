import {
  Alert<PERSON><PERSON>gle,
  Archive,
  BarChart3,
  CheckCircle,
  Clock,
  Eye,
  Package,
  RefreshCw,
  Shield,
  ShoppingCart,
  Star,
  TrendingUp,
  Truck,
} from "lucide-react";
import {
  FieldErrors,
  UseFormRegister,
  UseFormSetValue,
  UseFormWatch,
} from "react-hook-form";

import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { CollapsibleSection } from "@/components/ui/collapsible-section";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import {
  ProductFormData,
  conditionValues,
  stockManagementValues,
} from "@/schemas/productSchema";
import { Condition } from "@/types/common";

import { FormField } from "../../../common/FormField";
import { ImportantNotice } from "../../../ui/important-notice";

type InventorySectionProps = {
  register: UseFormRegister<ProductFormData>;
  errors: FieldErrors<ProductFormData>;
  setValue: UseFormSetValue<ProductFormData>;
  watch: UseFormWatch<ProductFormData>;
};

export const InventorySection: React.FC<InventorySectionProps> = ({
  register,
  errors,
  setValue,
  watch,
}) => {
  const stockQuantity = watch("stock");
  const stockManagement = watch("stockManagement");
  const trackQuantity = watch("trackQuantity");
  const lowStockThreshold = watch("lowStockThreshold");

  // Calculate stock status
  const getStockStatus = () => {
    if (!stockQuantity || stockQuantity === 0)
      return { status: "out-of-stock", color: "red", icon: AlertTriangle };
    if (lowStockThreshold && stockQuantity <= lowStockThreshold)
      return { status: "low-stock", color: "yellow", icon: AlertTriangle };
    return { status: "in-stock", color: "green", icon: CheckCircle };
  };

  const stockStatus = getStockStatus();

  return (
    <div className="space-y-8">
      {/* Section Header */}
      <div className="flex items-center gap-3 border-b border-gray-200 pb-4">
        <div className="rounded-full bg-purple-100 p-2">
          <Package className="h-5 w-5 text-purple-600" />
        </div>
        <div>
          <h2 className="text-2xl font-bold text-gray-900">
            Inventory Management
          </h2>
          <p className="text-gray-600">
            Track stock levels and manage inventory settings
          </p>
        </div>
      </div>

      {/* Important Notice */}
      <ImportantNotice
        description="You must complete all required fields in this section before proceeding to the next step."
        requiredFields={["Product Condition", "Stock Quantity"]}
        tip="Proper inventory tracking helps manage stock levels and provides accurate product information to customers."
        variant="amber"
      />

      {/* Product Condition Card */}
      <Card className="border-l-4 border-l-orange-500">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Star className="h-5 w-5 text-orange-600" />
            Product Condition
            <Badge variant="destructive" className="text-xs">
              Required
            </Badge>
          </CardTitle>
          <p className="text-sm text-gray-600">
            Specify the condition of the product being sold
          </p>
        </CardHeader>
        <CardContent>
          <FormField
            id="condition"
            label=""
            error={errors.condition?.message}
            optional={false}
          >
            <Select
              onValueChange={(value) =>
                setValue("condition", value as Condition)
              }
              defaultValue={watch("condition")}
            >
              <SelectTrigger className="border-2 p-4 text-lg focus:border-orange-500">
                <SelectValue placeholder="Select product condition..." />
              </SelectTrigger>
              <SelectContent>
                {conditionValues.map((condition) => (
                  <SelectItem key={condition} value={condition}>
                    <div className="flex items-center gap-2">
                      {condition === "new" && (
                        <Star className="h-4 w-4 text-green-500" />
                      )}
                      {condition === "used" && (
                        <RefreshCw className="h-4 w-4 text-blue-500" />
                      )}
                      {condition === "refurbished" && (
                        <Shield className="h-4 w-4 text-purple-500" />
                      )}
                      {condition === "damaged" && (
                        <AlertTriangle className="h-4 w-4 text-red-500" />
                      )}
                      {condition === "vintage" && (
                        <Clock className="h-4 w-4 text-amber-500" />
                      )}
                      {condition === "antique" && (
                        <Clock className="h-4 w-4 text-amber-600" />
                      )}
                      {condition === "like-new" && (
                        <Star className="h-4 w-4 text-green-400" />
                      )}
                      {condition === "excellent" && (
                        <Star className="h-4 w-4 text-blue-500" />
                      )}
                      {condition === "good" && (
                        <CheckCircle className="h-4 w-4 text-green-500" />
                      )}
                      {condition === "fair" && (
                        <AlertTriangle className="h-4 w-4 text-yellow-500" />
                      )}
                      <span className="font-medium capitalize">
                        {condition.replace("-", " ")}
                      </span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </FormField>

          {/* Condition Description */}
          {watch("condition") && (
            <div className="mt-3 rounded-lg border border-orange-200 bg-orange-50 p-3">
              <div className="flex items-start gap-2">
                {watch("condition") === "new" && (
                  <Star className="mt-0.5 h-4 w-4 text-green-600" />
                )}
                {watch("condition") === "used" && (
                  <RefreshCw className="mt-0.5 h-4 w-4 text-blue-600" />
                )}
                {watch("condition") === "refurbished" && (
                  <Shield className="mt-0.5 h-4 w-4 text-purple-600" />
                )}
                {watch("condition") === "damaged" && (
                  <AlertTriangle className="mt-0.5 h-4 w-4 text-red-600" />
                )}
                {watch("condition") === "vintage" && (
                  <Clock className="mt-0.5 h-4 w-4 text-amber-600" />
                )}
                {watch("condition") === "antique" && (
                  <Clock className="mt-0.5 h-4 w-4 text-amber-700" />
                )}
                {watch("condition") === "like-new" && (
                  <Star className="mt-0.5 h-4 w-4 text-green-500" />
                )}
                {watch("condition") === "excellent" && (
                  <Star className="mt-0.5 h-4 w-4 text-blue-600" />
                )}
                {watch("condition") === "good" && (
                  <CheckCircle className="mt-0.5 h-4 w-4 text-green-600" />
                )}
                {watch("condition") === "fair" && (
                  <AlertTriangle className="mt-0.5 h-4 w-4 text-yellow-600" />
                )}
                <div className="text-sm text-orange-800">
                  <strong>
                    {watch("condition") === "new" && "New:"}
                    {watch("condition") === "like-new" && "Like New:"}
                    {watch("condition") === "excellent" && "Excellent:"}
                    {watch("condition") === "good" && "Good:"}
                    {watch("condition") === "fair" && "Fair:"}
                    {watch("condition") === "used" && "Used:"}
                    {watch("condition") === "refurbished" && "Refurbished:"}
                    {watch("condition") === "vintage" && "Vintage:"}
                    {watch("condition") === "antique" && "Antique:"}
                    {watch("condition") === "damaged" && "Damaged:"}
                  </strong>
                  <span className="ml-1">
                    {watch("condition") === "new" &&
                      "Brand new, unused item in original packaging with all accessories"}
                    {watch("condition") === "like-new" &&
                      "Barely used item in excellent condition, appears almost new"}
                    {watch("condition") === "excellent" &&
                      "Very good condition with minimal signs of use, fully functional"}
                    {watch("condition") === "good" &&
                      "Good condition with some signs of use but works perfectly"}
                    {watch("condition") === "fair" &&
                      "Shows wear and use but still functional, may have minor issues"}
                    {watch("condition") === "used" &&
                      "Previously owned item, may show signs of wear but fully functional"}
                    {watch("condition") === "refurbished" &&
                      "Restored to working condition, tested and certified with warranty"}
                    {watch("condition") === "vintage" &&
                      "Older item with historical value, condition varies by age"}
                    {watch("condition") === "antique" &&
                      "Very old item with historical significance, condition varies"}
                    {watch("condition") === "damaged" &&
                      "Item has visible damage but may still be functional - sold as-is"}
                  </span>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Stock Quantity Card */}
      <Card className="border-l-4 border-l-blue-500">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <BarChart3 className="h-5 w-5 text-blue-600" />
            Stock Quantity
            <Badge variant="destructive" className="text-xs">
              Required
            </Badge>
          </CardTitle>
          <p className="text-sm text-gray-600">
            Current number of items available for sale
          </p>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <FormField
              id="stock"
              label=""
              error={errors.stock?.message}
              optional={false}
            >
              <Input
                id="stock"
                type="number"
                min="0"
                {...register("stock", {
                  setValueAs: (value) => {
                    if (value === "" || value === null || value === undefined) {
                      return undefined;
                    }
                    const numValue = parseInt(value, 10);
                    return isNaN(numValue) ? undefined : numValue;
                  },
                })}
                placeholder="Enter stock quantity"
                className="border-2 p-4 text-lg focus:border-blue-500"
              />
            </FormField>

            {/* Stock Status Display */}
            {stockQuantity !== undefined && (
              <div
                className={`rounded-lg border p-3 ${
                  stockStatus.color === "green"
                    ? "border-green-200 bg-green-50"
                    : stockStatus.color === "yellow"
                      ? "border-yellow-200 bg-yellow-50"
                      : "border-red-200 bg-red-50"
                }`}
              >
                <div className="flex items-center gap-2">
                  <stockStatus.icon
                    className={`h-4 w-4 ${
                      stockStatus.color === "green"
                        ? "text-green-600"
                        : stockStatus.color === "yellow"
                          ? "text-yellow-600"
                          : "text-red-600"
                    }`}
                  />
                  <span
                    className={`font-medium ${
                      stockStatus.color === "green"
                        ? "text-green-900"
                        : stockStatus.color === "yellow"
                          ? "text-yellow-900"
                          : "text-red-900"
                    }`}
                  >
                    {stockStatus.status === "in-stock" &&
                      `In Stock (${stockQuantity} units)`}
                    {stockStatus.status === "low-stock" &&
                      `Low Stock Warning (${stockQuantity} units remaining)`}
                    {stockStatus.status === "out-of-stock" && "Out of Stock"}
                  </span>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Stock Management Settings - Collapsible */}
      <CollapsibleSection
        title="Stock Management"
        description="Configure how inventory is tracked and managed"
        icon={<RefreshCw className="h-5 w-5 text-green-600" />}
        borderColor="border-l-green-500"
        defaultOpen={false}
        isOptional={true}
      >
        <div className="space-y-4">
          {/* Stock Management Type */}
          <FormField
            id="stockManagement"
            label="Stock Management Type"
            optional={true}
          >
            <Select
              onValueChange={(value) =>
                setValue(
                  "stockManagement",
                  value as "track" | "no-track" | "backorder"
                )
              }
              value={stockManagement}
            >
              <SelectTrigger className="border-2 focus:border-green-500">
                <SelectValue placeholder="Select stock management..." />
              </SelectTrigger>
              <SelectContent>
                {stockManagementValues.map((type) => (
                  <SelectItem key={type} value={type}>
                    <div className="flex items-center gap-2">
                      {type === "track" && <BarChart3 className="h-4 w-4" />}
                      {type === "no-track" && <Eye className="h-4 w-4" />}
                      {type === "backorder" && <Clock className="h-4 w-4" />}
                      <span className="font-medium capitalize">
                        {type === "no-track" ? "Don't Track" : type}
                      </span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </FormField>

          {/* Track Quantity Toggle */}
          <div className="flex items-center space-x-3">
            <Switch
              id="track-quantity"
              checked={trackQuantity}
              onCheckedChange={(checked) => setValue("trackQuantity", checked)}
            />
            <Label htmlFor="track-quantity" className="text-base font-medium">
              Track quantity changes
            </Label>
          </div>

          {/* Low Stock Threshold */}
          {trackQuantity && (
            <FormField
              id="lowStockThreshold"
              label="Low Stock Alert Threshold"
              optional={true}
            >
              <Input
                id="lowStockThreshold"
                type="number"
                min="0"
                {...register("lowStockThreshold", {
                  setValueAs: (value) => {
                    if (value === "" || value === null || value === undefined) {
                      return undefined;
                    }
                    const numValue = parseInt(value, 10);
                    return isNaN(numValue) ? undefined : numValue;
                  },
                })}
                placeholder="Alert when stock reaches this level"
                className="border-2 focus:border-green-500"
              />
            </FormField>
          )}
        </div>
      </CollapsibleSection>

      {/* Advanced Inventory Options - Collapsible */}
      <CollapsibleSection
        title="Advanced Options"
        description="Additional inventory and sales settings"
        icon={<Archive className="h-5 w-5 text-purple-600" />}
        borderColor="border-l-purple-500"
        defaultOpen={false}
        isOptional={true}
      >
        <div className="space-y-4">
          {/* Backorder Allowed */}
          <div className="flex items-center space-x-3">
            <Switch
              id="backorder-allowed"
              checked={watch("backorderAllowed")}
              onCheckedChange={(checked) =>
                setValue("backorderAllowed", checked)
              }
            />
            <Label
              htmlFor="backorder-allowed"
              className="text-base font-medium"
            >
              Allow backorders when out of stock
            </Label>
          </div>

          {/* Sold Individually */}
          <div className="flex items-center space-x-3">
            <Switch
              id="sold-individually"
              checked={watch("soldIndividually")}
              onCheckedChange={(checked) =>
                setValue("soldIndividually", checked)
              }
            />
            <Label
              htmlFor="sold-individually"
              className="text-base font-medium"
            >
              Sold individually (limit 1 per order)
            </Label>
          </div>

          {/* Published Status */}
          <div className="flex items-center space-x-3">
            <Switch
              id="is-published"
              checked={watch("isPublished")}
              onCheckedChange={(checked) => setValue("isPublished", checked)}
            />
            <Label htmlFor="is-published" className="text-base font-medium">
              Publish this product immediately
            </Label>
          </div>
        </div>
      </CollapsibleSection>

      {/* Inventory Summary Card */}
      <Card className="border-blue-200 bg-gradient-to-r from-blue-50 to-purple-50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-blue-800">
            <TrendingUp className="h-5 w-5" />
            Inventory Summary
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <div className="flex items-center gap-3">
              <div className="rounded-full bg-blue-100 p-2">
                <Package className="h-4 w-4 text-blue-600" />
              </div>
              <div>
                <h4 className="font-medium text-blue-900">Current Stock</h4>
                <p className="text-sm text-blue-700">
                  {stockQuantity || 0} units available
                </p>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <div className="rounded-full bg-purple-100 p-2">
                <BarChart3 className="h-4 w-4 text-purple-600" />
              </div>
              <div>
                <h4 className="font-medium text-purple-900">Management</h4>
                <p className="text-sm text-purple-700">
                  {stockManagement || "Not set"}
                </p>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <div className="rounded-full bg-green-100 p-2">
                <CheckCircle className="h-4 w-4 text-green-600" />
              </div>
              <div>
                <h4 className="font-medium text-green-900">Status</h4>
                <p className="text-sm text-green-700">
                  {watch("isPublished") ? "Published" : "Draft"}
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
