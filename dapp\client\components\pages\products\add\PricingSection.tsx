import { useCallback, useEffect, useState } from "react";

import { format } from "date-fns";
import {
  Calculator,
  CalendarIcon,
  DollarSign,
  Percent,
  TrendingDown,
} from "lucide-react";
import {
  FieldErrors,
  UseFormRegister,
  UseFormSetValue,
  UseFormWatch,
} from "react-hook-form";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { ProductFormData, currencyValues } from "@/schemas/productSchema";
import { CurrencyUnit } from "@/types/common";

import { FormField } from "../../../common/FormField";
import { ImportantNotice } from "../../../ui/important-notice";

type PricingSectionProps = {
  register: UseFormRegister<ProductFormData>;
  errors: FieldErrors<ProductFormData>;
  setValue: UseFormSetValue<ProductFormData>;
  watch: UseFormWatch<ProductFormData>;
};

export const PricingSection: React.FC<PricingSectionProps> = ({
  register,
  errors,
  setValue,
  watch,
}) => {
  const [saleDate, setSaleDate] = useState<Date | undefined>();

  // Watch form values for dynamic calculations
  const regularPrice = watch("price"); // Regular/standard price
  const salePrice = watch("salePrice"); // Discounted sale price (new field)
  const isOnSale = watch("isOnSale");
  const costPrice = watch("costPrice");

  // Helper function to check if a value is a valid positive number
  const isValidNumber = useCallback((value: unknown): value is number => {
    return typeof value === "number" && !isNaN(value) && value > 0;
  }, []);

  // Calculate current selling price (what customers actually pay)
  const currentSellingPrice =
    isOnSale && isValidNumber(salePrice) ? salePrice : regularPrice;

  // Calculate savings and profit margin - only if we have valid numbers
  const savings =
    isOnSale && isValidNumber(regularPrice) && isValidNumber(salePrice)
      ? regularPrice - salePrice
      : 0;

  const savingsPercentage =
    isOnSale && isValidNumber(regularPrice) && savings > 0
      ? Math.round((savings / regularPrice) * 100)
      : 0;

  const profitMargin =
    isValidNumber(costPrice) && isValidNumber(currentSellingPrice)
      ? Math.round(
          ((currentSellingPrice - costPrice) / currentSellingPrice) * 100
        )
      : 0;

  // Quick discount percentages
  const quickDiscounts = [5, 10, 15, 20, 25, 30, 40, 50];

  // Apply quick discount
  const applyQuickDiscount = (percentage: number) => {
    if (isValidNumber(regularPrice)) {
      const discountAmount = regularPrice * (percentage / 100);
      const newSalePrice = regularPrice - discountAmount;
      setValue("salePrice", Number(newSalePrice.toFixed(2))); // Using proper salePrice field
      setValue("originalPrice", regularPrice); // Set originalPrice for backwards compatibility
      setValue("isOnSale", true);
    }
  };

  // Clear sale
  const clearSale = () => {
    setValue("isOnSale", false);
    setValue("salePrice", undefined);
    setValue("originalPrice", undefined);
  };

  const handleSaleToggle = (checked: boolean) => {
    setValue("isOnSale", checked);
    if (!checked) {
      setValue("originalPrice", undefined);
      setValue("saleEndsAt", undefined);
      setSaleDate(undefined);
    }
  };

  const handleDateSelect = (date: Date | undefined) => {
    setSaleDate(date);
    if (date) {
      setValue("saleEndsAt", date.toISOString());
    } else {
      setValue("saleEndsAt", undefined);
    }
  };

  // Update isOnSale when component mounts
  useEffect(() => {
    if (
      isValidNumber(regularPrice) &&
      isValidNumber(salePrice) &&
      regularPrice > salePrice
    ) {
      setValue("isOnSale", true);
    }
  }, [regularPrice, salePrice, setValue, isValidNumber]);

  return (
    <div className="space-y-8">
      {/* Section Header */}
      <div className="flex items-center gap-3 border-b border-gray-200 pb-4">
        <div className="rounded-full bg-green-100 p-2">
          <DollarSign className="h-5 w-5 text-green-600" />
        </div>
        <div>
          <h2 className="text-2xl font-bold text-gray-900">
            Pricing Information
          </h2>
          <p className="text-gray-600">
            Set your product pricing and sale options
          </p>
        </div>
      </div>

      {/* Important Notice */}
      <ImportantNotice
        description="You must complete all required fields in this section before proceeding to the next step."
        requiredFields={["Regular Price", "Currency", "Cost Price"]}
        tip="Cost price helps calculate profit margins and is essential for business analytics."
        variant="amber"
      />

      {/* Regular Price Card */}
      <Card className="border-l-4 border-l-blue-500">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <DollarSign className="h-5 w-5 text-blue-600" />
            Regular Price
            <Badge variant="destructive" className="text-xs">
              Required
            </Badge>
          </CardTitle>
          <p className="text-sm text-gray-600">
            The standard price of your product (before any discounts)
          </p>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <div className="flex min-h-[20px] items-center justify-between">
                <span className="text-sm font-medium text-gray-700">Price</span>
                {errors.price && (
                  <span className="text-xs font-medium text-destructive">
                    {errors.price.message}
                  </span>
                )}
              </div>
              <Input
                id="price"
                type="number"
                step="0.01"
                {...register("price", {
                  setValueAs: (value) => {
                    if (value === "" || value === null || value === undefined) {
                      return undefined;
                    }
                    const numValue = parseFloat(value);
                    return isNaN(numValue) ? undefined : numValue;
                  },
                })}
                placeholder="0.00"
                className="border-2 p-4 text-lg focus:border-blue-500"
              />
            </div>

            <div className="space-y-2">
              <div className="flex min-h-[20px] items-center justify-between">
                <span className="text-sm font-medium text-gray-700">
                  Currency
                </span>
                {errors.currency && (
                  <span className="text-xs font-medium text-destructive">
                    {errors.currency.message}
                  </span>
                )}
              </div>
              <Select
                onValueChange={(value) =>
                  setValue("currency", value as CurrencyUnit)
                }
                defaultValue={watch("currency")}
              >
                <SelectTrigger className="border-2 p-4 text-lg focus:border-blue-500">
                  <SelectValue placeholder="Select currency" />
                </SelectTrigger>
                <SelectContent>
                  {currencyValues.map((currency) => (
                    <SelectItem key={currency} value={currency}>
                      {currency}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Current Selling Price Display */}
          {isValidNumber(regularPrice) && (
            <div className="mt-4 rounded-lg border border-blue-200 bg-blue-50 p-3">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-blue-900">
                  Customers will pay:
                </span>
                <span className="text-lg font-bold text-blue-700">
                  {watch("currency")}{" "}
                  {isValidNumber(currentSellingPrice)
                    ? currentSellingPrice.toFixed(2)
                    : regularPrice.toFixed(2)}
                  {isOnSale && savingsPercentage > 0 && (
                    <span className="ml-2 text-sm text-green-600">
                      ({savingsPercentage}% OFF!)
                    </span>
                  )}
                </span>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Cost Price & Business Intelligence Card */}
      <Card className="border-l-4 border-l-blue-500">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Calculator className="h-5 w-5 text-blue-600" />
            Business Intelligence
            <Badge variant="destructive" className="text-xs">
              Required
            </Badge>
          </CardTitle>
          <p className="text-sm text-gray-600">
            Track costs and profit margins for business analysis - required for
            proceeding
          </p>
        </CardHeader>
        <CardContent>
          <FormField
            id="costPrice"
            label="Cost Price (What you paid)"
            error={errors.costPrice?.message}
            optional={false}
          >
            <Input
              id="costPrice"
              type="number"
              step="0.01"
              {...register("costPrice", {
                setValueAs: (value) => {
                  if (value === "" || value === null || value === undefined) {
                    return undefined;
                  }
                  const numValue = parseFloat(value);
                  return isNaN(numValue) ? undefined : numValue;
                },
              })}
              placeholder="0.00"
              className="border-2 p-4 text-lg focus:border-blue-500"
            />
          </FormField>

          {/* Profit Margin Display */}
          {isValidNumber(costPrice) && isValidNumber(currentSellingPrice) && (
            <div className="mt-3 rounded-lg bg-blue-50 p-3">
              <div className="mb-2 flex items-center gap-2">
                <TrendingDown className="h-4 w-4 text-blue-600" />
                <span className="font-medium text-blue-900">
                  Profit Analysis
                </span>
              </div>
              <div className="grid gap-2 text-sm">
                <div className="flex justify-between">
                  <span>Profit per Sale:</span>
                  <span className="font-medium text-blue-600">
                    {watch("currency")}{" "}
                    {(currentSellingPrice - costPrice).toFixed(2)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Profit Margin:</span>
                  <span
                    className={`font-medium ${profitMargin > 0 ? "text-green-600" : "text-red-600"}`}
                  >
                    {profitMargin}%
                  </span>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Sale Configuration Card */}
      <Card className="border-l-4 border-l-red-500">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <TrendingDown className="h-5 w-5 text-red-600" />
            Sale & Discounts
            <Badge variant="secondary" className="text-xs">
              Optional
            </Badge>
          </CardTitle>
          <p className="text-sm text-gray-600">
            Set up discounts and promotional pricing
          </p>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center space-x-3">
              <Switch
                id="sale-toggle"
                checked={isOnSale}
                onCheckedChange={handleSaleToggle}
              />
              <Label htmlFor="sale-toggle" className="text-base font-medium">
                Put this product on sale
              </Label>
            </div>

            {/* Quick Discount Buttons */}
            {isValidNumber(regularPrice) && !isOnSale && (
              <div className="rounded-lg border border-gray-200 bg-gray-50 p-4">
                <h4 className="mb-3 font-medium text-gray-900">
                  Quick Discounts
                </h4>
                <div className="grid grid-cols-4 gap-2 md:grid-cols-8">
                  {quickDiscounts.map((percentage) => (
                    <Button
                      key={percentage}
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => applyQuickDiscount(percentage)}
                      className="text-xs hover:border-red-300 hover:bg-red-50"
                    >
                      {percentage}% OFF
                    </Button>
                  ))}
                </div>
                <p className="mt-2 text-xs text-gray-500">
                  Click any button to automatically calculate the sale price
                </p>
              </div>
            )}

            {isOnSale && (
              <div className="space-y-4 rounded-lg border border-red-200 bg-red-50 p-4">
                <div className="grid gap-4 md:grid-cols-2">
                  <FormField
                    id="salePrice"
                    label="Sale Price"
                    error={errors.salePrice?.message}
                    optional={true}
                  >
                    <Input
                      id="salePrice"
                      type="number"
                      step="0.01"
                      {...register("salePrice", {
                        setValueAs: (value) => {
                          if (
                            value === "" ||
                            value === null ||
                            value === undefined
                          ) {
                            return undefined;
                          }
                          const numValue = parseFloat(value);
                          return isNaN(numValue) ? undefined : numValue;
                        },
                      })}
                      placeholder="0.00"
                      className="border-2 focus:border-red-500"
                    />
                    <p className="mt-1 text-xs text-gray-500">
                      The discounted price customers will pay
                    </p>
                  </FormField>

                  <FormField
                    id="saleEndsAt"
                    label="Sale End Date"
                    optional={true}
                  >
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className="w-full justify-start border-2 text-left font-normal focus:border-red-500"
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {saleDate ? (
                            format(saleDate, "PPP")
                          ) : (
                            <span>Pick a date</span>
                          )}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0">
                        <Calendar
                          mode="single"
                          selected={saleDate}
                          onSelect={handleDateSelect}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                  </FormField>
                </div>

                {/* Quick Discount Buttons for Active Sale */}
                <div className="rounded-lg border border-red-300 bg-white p-3">
                  <div className="mb-2 flex items-center justify-between">
                    <h4 className="font-medium text-red-900">
                      Quick Discounts
                    </h4>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={clearSale}
                      className="text-xs text-red-600 hover:bg-red-100"
                    >
                      Clear Sale
                    </Button>
                  </div>
                  <div className="grid grid-cols-4 gap-2 md:grid-cols-8">
                    {quickDiscounts.map((percentage) => (
                      <Button
                        key={percentage}
                        type="button"
                        variant={
                          savingsPercentage === percentage
                            ? "default"
                            : "outline"
                        }
                        size="sm"
                        onClick={() => applyQuickDiscount(percentage)}
                        className={`text-xs ${
                          savingsPercentage === percentage
                            ? "bg-red-600 text-white"
                            : "hover:border-red-300 hover:bg-red-50"
                        }`}
                      >
                        {percentage}%
                      </Button>
                    ))}
                  </div>
                </div>

                {/* Enhanced Sale Summary */}
                {isValidNumber(regularPrice) && isValidNumber(salePrice) && (
                  <div className="rounded-lg border border-red-300 bg-white p-3">
                    <div className="mb-2 flex items-center gap-2">
                      <Percent className="h-4 w-4 text-red-600" />
                      <span className="font-medium text-red-900">
                        Sale Analysis
                      </span>
                    </div>
                    <div className="grid gap-2 text-sm">
                      {regularPrice > salePrice ? (
                        <>
                          <div className="flex justify-between">
                            <span>Regular Price:</span>
                            <span className="font-medium text-gray-600 line-through">
                              {watch("currency")} {regularPrice.toFixed(2)}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span>Sale Price:</span>
                            <span className="font-medium text-green-600">
                              {watch("currency")} {salePrice.toFixed(2)}
                            </span>
                          </div>
                          <div className="flex justify-between border-t pt-2">
                            <span>Customer Saves:</span>
                            <span className="font-bold text-red-600">
                              {watch("currency")} {savings.toFixed(2)}
                            </span>
                          </div>
                          <div className="mt-2 rounded bg-green-50 p-2 text-center">
                            <span className="text-lg font-bold text-green-700">
                              {savingsPercentage}% OFF
                            </span>
                          </div>
                        </>
                      ) : regularPrice === salePrice ? (
                        <div className="text-center text-amber-600">
                          <span className="font-medium">
                            ⚠️ No discount - prices are equal
                          </span>
                        </div>
                      ) : (
                        <div className="text-center text-red-600">
                          <span className="font-medium">
                            ⚠️ Sale price is higher than regular price
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
