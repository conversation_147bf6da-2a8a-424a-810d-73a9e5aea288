"use client";

import { motion } from "framer-motion";
// import Image from "next/image";
import Link from "next/link";

const HeroSection = () => {
  return (
    <section className="relative flex h-[calc(100vh-4rem)] items-center justify-center overflow-hidden">
      <div className="container z-10 mx-auto px-4 text-center">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.7, delay: 0.2 }}
        >
          <h1 className="mb-6 text-4xl font-bold leading-tight text-white md:text-5xl lg:text-6xl">
            Kūrybiniai medijos sprendimai jūsų verslui
          </h1>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.7, delay: 0.4 }}
        >
          <p className="mx-auto mb-8 max-w-3xl text-lg text-gray-200 md:text-xl">
            Padedame prekės ženklams užmegzti prasmingus ry<PERSON> per išskirtines
            skaitmenines patirtis ir novatoriš<PERSON>ą vizualinį turinį.
          </p>
        </motion.div>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.7, delay: 0.6 }}
          className="flex flex-col justify-center gap-4 sm:flex-row"
        >
          <Link
            href="#paslaugos"
            className="rounded-full bg-orange-500 px-8 py-3 font-medium text-white transition-colors duration-300 hover:bg-orange-600"
          >
            Paslaugos
          </Link>
          <Link
            href="#kontaktai"
            className="rounded-full border-2 border-white bg-transparent px-8 py-3 font-medium text-white transition-colors duration-300 hover:bg-white/10"
          >
            Kontaktai
          </Link>
        </motion.div>
      </div>

      <motion.div
        className="fixed bottom-8 left-1/2 -translate-x-1/2"
        animate={{ y: [0, 10, 0] }}
        transition={{ repeat: Infinity, duration: 1.5 }}
      >
        <svg
          className="size-8 text-white"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M19 14l-7 7m0 0l-7-7m7 7V3"
          />
        </svg>
      </motion.div>
    </section>
  );
};

export default HeroSection;
