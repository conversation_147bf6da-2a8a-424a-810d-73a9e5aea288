import { Transaction } from "@/types/transaction";

export const mockTransactions: Transaction[] = [
  {
    id: "TXN-001",
    orderId: "ORD-001",
    userId: "CUST-001",
    amount: 299.99,
    currency: "EUR",
    status: "paid",
    method: "card",
    providerTransactionId: "pi_3N8K9L2eZvKYlo2C0123456789",
    receiptUrl: "https://pay.stripe.com/receipts/payment/CAcaFwoVYWNjdF8xTjhLOUwyZVp2S1lsbzJD",
    createdAt: "2024-01-15T10:30:00Z",
    updatedAt: "2024-01-15T10:31:00Z",
  },
  {
    id: "TXN-002",
    orderId: "ORD-002",
    userId: "CUST-002",
    amount: 270.0,
    currency: "EUR",
    status: "pending",
    method: "paypal",
    providerTransactionId: "PAYID-MXYZ123-ABC456789",
    createdAt: "2024-01-14T16:45:00Z",
    updatedAt: "2024-01-14T16:45:00Z",
  },
  {
    id: "TXN-003",
    orderId: "ORD-003",
    userId: "CUST-003",
    amount: 795.0,
    currency: "EUR",
    status: "paid",
    method: "bank-transfer",
    providerTransactionId: "BT-2024-001-789456",
    receiptUrl: "https://bank.example.com/receipts/BT-2024-001-789456",
    createdAt: "2024-01-12T09:15:00Z",
    updatedAt: "2024-01-12T09:20:00Z",
  },
  {
    id: "TXN-004",
    orderId: "ORD-004",
    userId: "CUST-004",
    amount: 465.0,
    currency: "EUR",
    status: "paid",
    method: "card",
    providerTransactionId: "pi_3N8K9L2eZvKYlo2C0987654321",
    receiptUrl: "https://pay.stripe.com/receipts/payment/CAcaFwoVYWNjdF8xTjhLOUwyZVp2S1lsbzJE",
    createdAt: "2024-01-13T14:20:00Z",
    updatedAt: "2024-01-13T14:21:00Z",
  },
  {
    id: "TXN-005",
    orderId: "ORD-005",
    userId: "CUST-005",
    amount: 260.0,
    currency: "EUR",
    status: "cancelled",
    method: "card",
    providerTransactionId: "pi_3N8K9L2eZvKYlo2C0555666777",
    createdAt: "2024-01-11T12:00:00Z",
    updatedAt: "2024-01-12T10:15:00Z",
  },
  {
    id: "TXN-006",
    orderId: "ORD-006",
    userId: "CUST-006",
    amount: 101.5,
    currency: "EUR",
    status: "refunded",
    method: "paypal",
    providerTransactionId: "PAYID-MXYZ789-DEF123456",
    refundedAmount: 101.5,
    receiptUrl: "https://paypal.com/activity/payment/PAYID-MXYZ789-DEF123456",
    createdAt: "2024-01-10T15:30:00Z",
    updatedAt: "2024-01-19T13:20:00Z",
  },
  {
    id: "TXN-007",
    orderId: "ORD-007",
    userId: "CUST-007",
    amount: 89.99,
    currency: "EUR",
    status: "failed",
    method: "card",
    providerTransactionId: "pi_3N8K9L2eZvKYlo2C0111222333",
    createdAt: "2024-01-09T11:45:00Z",
    updatedAt: "2024-01-09T11:46:00Z",
  },
  {
    id: "TXN-008",
    orderId: "ORD-008",
    userId: "CUST-008",
    amount: 1299.99,
    currency: "EUR",
    status: "paid",
    method: "bank-transfer",
    providerTransactionId: "BT-2024-002-456789",
    receiptUrl: "https://bank.example.com/receipts/BT-2024-002-456789",
    createdAt: "2024-01-08T08:30:00Z",
    updatedAt: "2024-01-08T08:35:00Z",
  },
  {
    id: "TXN-009",
    orderId: "ORD-009",
    userId: "CUST-009",
    amount: 45.50,
    currency: "EUR",
    status: "paid",
    method: "cash-on-delivery",
    createdAt: "2024-01-07T16:20:00Z",
    updatedAt: "2024-01-07T18:45:00Z",
  },
  {
    id: "TXN-010",
    orderId: "ORD-010",
    userId: "CUST-010",
    amount: 199.99,
    currency: "EUR",
    status: "pending",
    method: "card",
    providerTransactionId: "pi_3N8K9L2eZvKYlo2C0444555666",
    createdAt: "2024-01-06T13:15:00Z",
    updatedAt: "2024-01-06T13:15:00Z",
  },
];

// Transaction statistics for dashboard
export const transactionStats = {
  totalTransactions: mockTransactions.length,
  totalAmount: mockTransactions.reduce((sum, txn) => sum + txn.amount, 0),
  successfulTransactions: mockTransactions.filter(txn => txn.status === "paid").length,
  pendingTransactions: mockTransactions.filter(txn => txn.status === "pending").length,
  failedTransactions: mockTransactions.filter(txn => txn.status === "failed").length,
  refundedTransactions: mockTransactions.filter(txn => txn.status === "refunded").length,
  averageTransactionAmount: mockTransactions.reduce((sum, txn) => sum + txn.amount, 0) / mockTransactions.length,
};

// Payment method distribution
export const paymentMethodStats = {
  card: mockTransactions.filter(txn => txn.method === "card").length,
  paypal: mockTransactions.filter(txn => txn.method === "paypal").length,
  bankTransfer: mockTransactions.filter(txn => txn.method === "bank-transfer").length,
  cashOnDelivery: mockTransactions.filter(txn => txn.method === "cash-on-delivery").length,
};
