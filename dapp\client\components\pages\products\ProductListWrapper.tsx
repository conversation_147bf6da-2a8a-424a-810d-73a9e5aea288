"use client";

import React, { forwardRef, useImperativeHandle, useState } from "react";

import { Card, CardContent } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { useProducts } from "@/hooks/useProducts";
import { ProductFilters } from "@/lib/api/products";

import { ProductFilter } from "./ProductFilter";
import { ProductGrid } from "./ProductGrid";
import { ProductPagination } from "./ProductPagination";

export interface ProductListWrapperRef {
  handleRefresh: () => void;
  loading: boolean;
}

export const ProductListWrapper = forwardRef<ProductListWrapperRef>(
  (props, ref) => {
    const [filters, setFilters] = useState<ProductFilters>({});
    const { products, loading, error, updateFilters, refreshProducts, meta } =
      useProducts(filters);

    const handleFiltersChange = (newFilters: ProductFilters) => {
      setFilters(newFilters);
      updateFilters(newFilters);
    };

    const handleRefresh = () => {
      refreshProducts();
    };

    // Expose methods to parent component
    useImperativeHandle(ref, () => ({
      handleRefresh,
      loading,
    }));

    return (
      <div className="space-y-6">
        <Card>
          <CardContent className="p-6">
            <ProductFilter
              onFiltersChange={handleFiltersChange}
              onRefresh={handleRefresh}
              loading={loading}
            />

            <Separator className="my-3 mb-6" />

            <ProductGrid
              products={products}
              loading={loading}
              error={error}
              onRefresh={handleRefresh}
            />
          </CardContent>
        </Card>

        {meta && meta.pages > 1 && (
          <div className="mt-6">
            <ProductPagination
              currentPage={meta.page}
              totalPages={meta.pages}
              onPageChange={(page) => handleFiltersChange({ ...filters, page })}
            />
          </div>
        )}
      </div>
    );
  }
);

ProductListWrapper.displayName = "ProductListWrapper";
