import { useEffect, useState } from "react";

import {
  Boxes,
  Calendar,
  ExternalLink,
  Grid3X3,
  Info,
  Layers,
  Package,
  Palette,
  Ruler,
  Sparkles,
  Tag,
  Weight,
} from "lucide-react";
import {
  FieldErrors,
  UseFormRegister,
  UseFormSetValue,
  UseFormWatch,
} from "react-hook-form";

import { Category } from "@/components/pages/management/CategoryManager";
import { Color } from "@/components/pages/management/ColorManagerEnhanced";
import { Material } from "@/components/pages/management/MaterialManager";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { CollapsibleSection } from "@/components/ui/collapsible-section";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { CategoryApiService } from "@/lib/api/categoryApi";
import {
  ProductFormData,
  dimensionUnitValues,
  weightUnitValues,
} from "@/schemas/productSchema";
import { DimensionUnit } from "@/types/common";

import { FormField } from "../../../common/FormField";
import { TagInput } from "../../../common/TagInput";
import { ImportantNotice } from "../../../ui/important-notice";

type DetailsSectionProps = {
  register: UseFormRegister<ProductFormData>;
  errors: FieldErrors<ProductFormData>;
  setValue: UseFormSetValue<ProductFormData>;
  watch: UseFormWatch<ProductFormData>;
  tags: string[];
  setTags: (tags: string[]) => void;
};

export const DetailsSection = ({
  register,
  errors,
  setValue,
  watch,
  tags,
  setTags,
}: DetailsSectionProps) => {
  const watchedDimensions = watch("dimensions");
  const watchedWeight = watch("weight");

  const [hasDimensions, setHasDimensions] = useState(false);
  const [hasWeight, setHasWeight] = useState(false);
  const [categories, setCategories] = useState<Category[]>([]);
  const [materials, setMaterials] = useState<Material[]>([]);
  const [colors, setColors] = useState<Color[]>([]);
  const [loadingCategories, setLoadingCategories] = useState(true);

  // Load categories from API and materials/colors from localStorage
  useEffect(() => {
    const loadData = async () => {
      try {
        // Load categories from API
        setLoadingCategories(true);
        const fetchedCategories = await CategoryApiService.getCategories({
          isActive: true,
        });
        setCategories(fetchedCategories);
      } catch (error) {
        console.error("Error loading categories from API:", error);
        // Fallback to localStorage if API fails
        try {
          const storedCategories = localStorage.getItem("product-categories");
          if (storedCategories) {
            setCategories(JSON.parse(storedCategories));
          }
        } catch (localError) {
          console.error(
            "Error loading categories from localStorage:",
            localError
          );
        }
      } finally {
        setLoadingCategories(false);
      }

      try {
        // Load materials from localStorage (until materials API is implemented)
        const storedMaterials = localStorage.getItem("product-materials");
        if (storedMaterials) {
          setMaterials(JSON.parse(storedMaterials));
        }

        // Load colors from localStorage (until colors API is implemented)
        const storedColors = localStorage.getItem("product-colors");
        if (storedColors) {
          setColors(JSON.parse(storedColors));
        }
      } catch (error) {
        console.error("Error loading data from localStorage:", error);
      }
    };

    loadData();
  }, []);

  const handleNestedSelectChange = (
    path: keyof ProductFormData,
    field: string,
    value: string | number
  ) => {
    const currentNestedObject = watch(path) || {};
    setValue(path, {
      ...currentNestedObject,
      [field]: value,
    } as DimensionUnit);
  };

  const resetDimensions = () => {
    setValue("dimensions", undefined);
  };

  const resetWeight = () => {
    setValue("weight", undefined);
  };

  const handleTagsChange = (newTags: string[]) => {
    setTags(newTags);
    setValue("tags", newTags);
  };

  return (
    <div className="space-y-8">
      {/* Section Header */}
      <div className="flex items-center gap-3 border-b border-gray-200 pb-4">
        <div className="rounded-full bg-orange-100 p-2">
          <Package className="h-5 w-5 text-orange-600" />
        </div>
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Product Details</h2>
          <p className="text-gray-600">
            Configure product specifications and attributes
          </p>
        </div>
      </div>

      {/* Important Notice */}
      <ImportantNotice
        description="You must complete all required fields in this section before proceeding to the next step."
        requiredFields={["Category", "Material", "Product Tags", "Color"]}
        tip="Category helps organize products for customers, Material information is essential for business intelligence, Tags improve search visibility, and Color enhances product presentation and customer choice."
        variant="amber"
      />

      {/* Category Card */}
      <Card className="border-l-4 border-l-blue-500">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Layers className="h-5 w-5 text-blue-600" />
            Category
            <Badge variant="destructive" className="text-xs">
              Required
            </Badge>
          </CardTitle>
          <p className="text-sm text-gray-600">
            Choose the main category for this product
          </p>
        </CardHeader>
        <CardContent>
          <FormField
            id="category"
            label=""
            error={errors.category?.message}
            optional={false}
          >
            <Select
              onValueChange={(value) => setValue("category", value)}
              value={watch("category")}
            >
              <SelectTrigger className="border-2 p-4 text-lg focus:border-blue-500">
                <SelectValue placeholder="Select a category..." />
              </SelectTrigger>
              <SelectContent>
                {loadingCategories ? (
                  <SelectItem value="loading" disabled>
                    Loading categories...
                  </SelectItem>
                ) : categories.length === 0 ? (
                  <SelectItem value="no-categories" disabled>
                    No categories available
                  </SelectItem>
                ) : (
                  categories.map((category) => (
                    <SelectItem key={category.id} value={category.name}>
                      <div className="flex items-center gap-2">
                        {category.icon && (
                          <span className="text-sm">{category.icon}</span>
                        )}
                        <Layers className="h-4 w-4" />
                        <span className="font-medium">{category.name}</span>
                      </div>
                    </SelectItem>
                  ))
                )}
                <SelectItem value="other">
                  <div className="flex items-center gap-2">
                    <Sparkles className="h-4 w-4" />
                    <span className="font-medium">Other (Custom)</span>
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
            {watch("category") === "other" && (
              <Input
                className="mt-3 border-2 focus:border-blue-500"
                id="custom-category"
                {...register("category")}
                placeholder="Enter custom category name"
              />
            )}
          </FormField>

          <div className="mt-3 rounded-lg bg-blue-50 p-3">
            <div className="flex items-start gap-2">
              <ExternalLink className="mt-0.5 h-4 w-4 text-blue-600" />
              <div className="text-sm text-blue-800">
                <strong>Tip:</strong> Need to add new categories?{" "}
                <a
                  href="/admin/products/categories"
                  target="_blank"
                  className="font-medium underline hover:no-underline"
                >
                  Manage categories here
                </a>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Material Card */}
      <Card className="border-l-4 border-l-green-500">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Palette className="h-5 w-5 text-green-600" />
            Material
            <Badge variant="destructive" className="text-xs">
              Required
            </Badge>
          </CardTitle>
          <p className="text-sm text-gray-600">
            Specify the material composition of this product
          </p>
        </CardHeader>
        <CardContent>
          <FormField
            id="material"
            label=""
            error={errors.material?.message}
            optional={false}
          >
            <Select
              onValueChange={(value) => setValue("material", value)}
              value={watch("material")}
            >
              <SelectTrigger className="border-2 p-4 text-lg focus:border-green-500">
                <SelectValue placeholder="Select a material..." />
              </SelectTrigger>
              <SelectContent>
                {materials.length === 0 ? (
                  <SelectItem value="no-materials" disabled>
                    No materials available
                  </SelectItem>
                ) : (
                  materials.map((material) => (
                    <SelectItem key={material.id} value={material.name}>
                      <div className="flex items-center gap-2">
                        <Palette className="h-4 w-4" />
                        <span className="font-medium">{material.name}</span>
                      </div>
                    </SelectItem>
                  ))
                )}
                <SelectItem value="other">
                  <div className="flex items-center gap-2">
                    <Sparkles className="h-4 w-4" />
                    <span className="font-medium">Other (Custom)</span>
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
            {watch("material") === "other" && (
              <Input
                className="mt-3 border-2 focus:border-green-500"
                id="custom-material"
                {...register("material")}
                placeholder="Enter custom material name"
              />
            )}
          </FormField>

          <div className="mt-3 rounded-lg bg-green-50 p-3">
            <div className="flex items-start gap-2">
              <ExternalLink className="mt-0.5 h-4 w-4 text-green-600" />
              <div className="text-sm text-green-800">
                <strong>Tip:</strong> Need to add new materials?{" "}
                <a
                  href="/admin/products/materials"
                  target="_blank"
                  className="font-medium underline hover:no-underline"
                >
                  Manage materials here
                </a>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Color Card */}
      <Card className="border-l-4 border-l-pink-500">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Palette className="h-5 w-5 text-pink-600" />
            Color
            <Badge variant="destructive" className="text-xs">
              Required
            </Badge>
          </CardTitle>
          <p className="text-sm text-gray-600">
            Choose the primary color for this product
          </p>
        </CardHeader>
        <CardContent>
          <FormField
            id="color"
            label=""
            error={errors.color?.message}
            optional={false}
          >
            <Select
              onValueChange={(value) => setValue("color", value)}
              value={watch("color")}
            >
              <SelectTrigger className="border-2 p-4 text-lg focus:border-pink-500">
                <SelectValue placeholder="Select a color..." />
              </SelectTrigger>
              <SelectContent>
                {colors.length === 0 ? (
                  <SelectItem value="no-colors" disabled>
                    <div className="flex items-center gap-2 text-gray-500">
                      <Palette className="h-4 w-4" />
                      <span>No colors available</span>
                    </div>
                  </SelectItem>
                ) : (
                  colors
                    .filter((color) => color.isActive)
                    .map((color) => (
                      <SelectItem key={color.id} value={color.name}>
                        <div className="flex items-center gap-2">
                          <div
                            className="h-4 w-4 rounded border border-gray-300"
                            style={{ backgroundColor: color.hexCode }}
                          />
                          <span className="font-medium">{color.name}</span>
                          <span className="text-xs text-gray-500">
                            {color.hexCode}
                          </span>
                        </div>
                      </SelectItem>
                    ))
                )}
                <SelectItem value="other">
                  <div className="flex items-center gap-2">
                    <Sparkles className="h-4 w-4" />
                    <span className="font-medium">Other (Custom)</span>
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </FormField>

          <div className="mt-3 rounded-lg bg-pink-50 p-3">
            <div className="flex items-start gap-2">
              <ExternalLink className="mt-0.5 h-4 w-4 text-pink-600" />
              <div className="text-sm text-pink-800">
                <strong>Tip:</strong> Need to add new colors?{" "}
                <a
                  href="/admin/products/catalog-settings?tab=colors"
                  target="_blank"
                  className="font-medium underline hover:no-underline"
                >
                  Manage colors here
                </a>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Product Tags Card */}
      <Card className="border-l-4 border-l-indigo-500">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Tag className="h-5 w-5 text-indigo-600" />
            Product Tags
            <Badge variant="destructive" className="text-xs">
              Required
            </Badge>
          </CardTitle>
          <p className="text-sm text-gray-600">
            Add searchable tags to help customers find your product
          </p>
        </CardHeader>
        <CardContent>
          <FormField
            id="tags"
            label=""
            error={errors.tags?.message as string}
            optional={false}
          >
            <TagInput
              tags={tags}
              setTags={handleTagsChange}
              placeholder="Type tag and press Enter..."
            />
          </FormField>

          <div className="mt-3 rounded-lg border border-indigo-200 bg-indigo-50 p-3">
            <div className="flex items-start gap-2">
              <Tag className="mt-0.5 h-4 w-4 text-indigo-600" />
              <div className="text-sm text-indigo-800">
                <strong>SEO Tip:</strong> Use relevant keywords as tags to
                improve search visibility. Click the × to remove tags.
              </div>
            </div>
          </div>

          {/* Tag Examples */}
          {tags.length === 0 && (
            <div className="mt-3 rounded-lg border border-gray-200 bg-gray-50 p-3">
              <div className="flex items-start gap-2">
                <Sparkles className="mt-0.5 h-4 w-4 text-gray-600" />
                <div className="text-sm text-gray-700">
                  <strong>Examples:</strong> wireless, bluetooth, waterproof,
                  premium, eco-friendly, bestseller
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Dimensions Card */}
      <Card className="border-l-4 border-l-purple-500">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Ruler className="h-5 w-5 text-purple-600" />
            Dimensions
            <Badge variant="secondary" className="text-xs">
              Optional
            </Badge>
          </CardTitle>
          <p className="text-sm text-gray-600">
            Add physical dimensions for shipping calculations
          </p>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Dimensions Toggle */}
            <div className="flex items-center space-x-3">
              <Switch
                id="dimensions-toggle"
                checked={hasDimensions}
                onCheckedChange={(checked) => {
                  setHasDimensions(checked);
                  if (!checked) resetDimensions();
                }}
              />
              <Label
                htmlFor="dimensions-toggle"
                className="text-base font-medium"
              >
                Add product dimensions
              </Label>
            </div>

            {/* Dimensions Section */}
            {hasDimensions && (
              <div className="space-y-4 rounded-lg border border-purple-200 bg-purple-50 p-4">
                <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-4">
                  <FormField
                    id="dimensions.width"
                    label="Width"
                    error={errors.dimensions?.width?.message}
                    className="col-span-1"
                  >
                    <Input
                      id="dimensions.width"
                      type="number"
                      step="0.01"
                      {...register("dimensions.width", { valueAsNumber: true })}
                      placeholder="0.00"
                      className="border-2 focus:border-purple-500"
                    />
                  </FormField>

                  <FormField
                    id="dimensions.height"
                    label="Height"
                    error={errors.dimensions?.height?.message}
                    className="col-span-1"
                  >
                    <Input
                      id="dimensions.height"
                      type="number"
                      step="0.01"
                      {...register("dimensions.height", {
                        valueAsNumber: true,
                      })}
                      placeholder="0.00"
                      className="border-2 focus:border-purple-500"
                    />
                  </FormField>

                  <FormField
                    id="dimensions.depth"
                    label="Depth"
                    error={errors.dimensions?.depth?.message}
                    className="col-span-1"
                  >
                    <Input
                      id="dimensions.depth"
                      type="number"
                      step="0.01"
                      {...register("dimensions.depth", { valueAsNumber: true })}
                      placeholder="0.00"
                      className="border-2 focus:border-purple-500"
                    />
                  </FormField>

                  <FormField
                    id="dimensions.unit"
                    label="Unit"
                    error={errors.dimensions?.unit?.message}
                    className="col-span-1"
                  >
                    <Select
                      onValueChange={(value) =>
                        handleNestedSelectChange("dimensions", "unit", value)
                      }
                      value={watchedDimensions?.unit}
                    >
                      <SelectTrigger className="border-2 focus:border-purple-500">
                        <SelectValue placeholder="Select unit" />
                      </SelectTrigger>
                      <SelectContent>
                        {dimensionUnitValues.map((unit) => (
                          <SelectItem key={unit} value={unit}>
                            <div className="flex items-center gap-2">
                              <Ruler className="h-4 w-4" />
                              <span className="font-medium">{unit}</span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </FormField>
                </div>

                <div className="rounded-lg border border-purple-300 bg-white p-3">
                  <div className="flex items-start gap-2">
                    <Info className="mt-0.5 h-4 w-4 text-purple-600" />
                    <div className="text-sm text-purple-800">
                      <strong>Shipping Tip:</strong> Accurate dimensions help
                      calculate shipping costs and prevent delivery issues.
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Weight Card */}
      <Card className="border-l-4 border-l-teal-500">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Weight className="h-5 w-5 text-teal-600" />
            Weight
            <Badge variant="secondary" className="text-xs">
              Optional
            </Badge>
          </CardTitle>
          <p className="text-sm text-gray-600">
            Add product weight for shipping calculations
          </p>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Weight Toggle */}
            <div className="flex items-center space-x-3">
              <Switch
                id="weight-toggle"
                checked={hasWeight}
                onCheckedChange={(checked) => {
                  setHasWeight(checked);
                  if (!checked) resetWeight();
                }}
              />
              <Label htmlFor="weight-toggle" className="text-base font-medium">
                Add product weight
              </Label>
            </div>

            {/* Weight Section */}
            {hasWeight && (
              <div className="space-y-4 rounded-lg border border-teal-200 bg-teal-50 p-4">
                <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                  <FormField
                    id="weight.value"
                    label="Weight Value"
                    error={errors.weight?.value?.message}
                  >
                    <Input
                      id="weight.value"
                      type="number"
                      step="0.01"
                      {...register("weight.value", { valueAsNumber: true })}
                      placeholder="0.00"
                      className="border-2 focus:border-teal-500"
                    />
                  </FormField>

                  <FormField
                    id="weight.unit"
                    label="Weight Unit"
                    error={errors.weight?.unit?.message}
                  >
                    <Select
                      onValueChange={(value) =>
                        handleNestedSelectChange("weight", "unit", value)
                      }
                      value={watchedWeight?.unit}
                    >
                      <SelectTrigger className="border-2 focus:border-teal-500">
                        <SelectValue placeholder="Select unit" />
                      </SelectTrigger>
                      <SelectContent>
                        {weightUnitValues.map((unit) => (
                          <SelectItem key={unit} value={unit}>
                            <div className="flex items-center gap-2">
                              <Weight className="h-4 w-4" />
                              <span className="font-medium">{unit}</span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </FormField>
                </div>

                <div className="rounded-lg border border-teal-300 bg-white p-3">
                  <div className="flex items-start gap-2">
                    <Info className="mt-0.5 h-4 w-4 text-teal-600" />
                    <div className="text-sm text-teal-800">
                      <strong>Shipping Tip:</strong> Accurate weight helps
                      calculate shipping costs and prevents carrier surcharges.
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Year Made - Collapsible */}
      <CollapsibleSection
        title="Year Made"
        description="Specify the year this product was manufactured"
        icon={<Calendar className="h-5 w-5 text-amber-600" />}
        borderColor="border-l-amber-500"
        defaultOpen={false}
        isOptional={true}
      >
        <FormField
          id="yearMade"
          label=""
          error={errors.yearMade?.message}
          optional={true}
        >
          <Input
            id="yearMade"
            type="number"
            min="1900"
            max={new Date().getFullYear() + 1}
            {...register("yearMade", { valueAsNumber: true })}
            placeholder={new Date().getFullYear().toString()}
            className="border-2 p-4 text-lg focus:border-amber-500"
          />
        </FormField>

        <div className="mt-3 rounded-lg border border-amber-200 bg-amber-50 p-3">
          <div className="flex items-start gap-2">
            <Calendar className="mt-0.5 h-4 w-4 text-amber-600" />
            <div className="text-sm text-amber-800">
              <strong>Note:</strong> Manufacturing year helps customers
              understand product age and can affect warranty coverage.
            </div>
          </div>
        </div>
      </CollapsibleSection>
    </div>
  );
};
