"use client";

import React from "react";

import {
  Calendar,
  Edit,
  Eye,
  Mail,
  MapPin,
  MoreHorizontal,
  Phone,
  ShoppingBag,
} from "lucide-react";
import Image from "next/image";
import { useRouter } from "next/navigation";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

type Customer = {
  id: string;
  name: string;
  email: string;
  avatar: string;
  phone: string;
  location: string;
  joinedAt: string;
  lastOrderAt: string | null;
  totalOrders: number;
  totalSpent: number;
  status: "active" | "inactive";
  customerType: "regular" | "premium" | "vip";
  tags: string[];
};

type CustomerRowProps = {
  customer: Customer;
};

export const CustomerRow = ({ customer }: CustomerRowProps) => {
  const router = useRouter();

  const handleViewDetails = () => {
    router.push(`/admin/customers/${customer.id}`);
  };

  const handleEditCustomer = () => {
    console.log("Edit customer:", customer.id);
  };

  const handleViewOrders = () => {
    router.push(`/admin/orders/list?customer=${customer.id}`);
  };

  const handleSendEmail = () => {
    console.log("Send email to:", customer.email);
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return "Never";
    return new Date(dateString).toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "active":
        return <Badge className="bg-green-100 text-green-700">Active</Badge>;
      case "inactive":
        return <Badge className="bg-gray-100 text-gray-700">Inactive</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const getTypeBadge = (type: string) => {
    switch (type) {
      case "vip":
        return <Badge className="bg-purple-100 text-purple-700">VIP</Badge>;
      case "premium":
        return <Badge className="bg-blue-100 text-blue-700">Premium</Badge>;
      case "regular":
        return <Badge className="bg-gray-100 text-gray-700">Regular</Badge>;
      default:
        return <Badge variant="secondary">{type}</Badge>;
    }
  };

  return (
    <tr className="border-b transition-colors hover:bg-gray-50/50">
      {/* Customer Info */}
      <td className="p-4">
        <div className="flex items-center gap-3">
          <div className="relative">
            <Image
              src={customer.avatar}
              alt={customer.name}
              width={40}
              height={40}
              className="rounded-full object-cover"
            />
            {customer.status === "active" && (
              <div className="absolute -bottom-1 -right-1 h-3 w-3 rounded-full border-2 border-white bg-green-500" />
            )}
          </div>
          <div>
            <button
              onClick={handleViewDetails}
              className="text-left font-medium text-gray-900 transition-colors hover:text-blue-600 hover:underline"
            >
              {customer.name}
            </button>
            <div className="text-xs text-gray-500">ID: {customer.id}</div>
            {customer.tags.length > 0 && (
              <div className="mt-1 flex gap-1">
                {customer.tags.slice(0, 2).map((tag) => (
                  <Badge
                    key={tag}
                    variant="outline"
                    className="px-1 py-0 text-xs"
                  >
                    {tag}
                  </Badge>
                ))}
                {customer.tags.length > 2 && (
                  <Badge variant="outline" className="px-1 py-0 text-xs">
                    +{customer.tags.length - 2}
                  </Badge>
                )}
              </div>
            )}
          </div>
        </div>
      </td>

      {/* Contact Info */}
      <td className="p-4">
        <div className="space-y-1">
          <div className="flex items-center gap-2 text-sm">
            <Mail className="h-3 w-3 text-gray-400" />
            <span className="text-gray-900">{customer.email}</span>
          </div>
          <div className="flex items-center gap-2 text-sm">
            <Phone className="h-3 w-3 text-gray-400" />
            <span className="text-gray-600">{customer.phone}</span>
          </div>
          <div className="flex items-center gap-2 text-sm">
            <MapPin className="h-3 w-3 text-gray-400" />
            <span className="text-gray-600">{customer.location}</span>
          </div>
        </div>
      </td>

      {/* Status */}
      <td className="p-4 text-center">{getStatusBadge(customer.status)}</td>

      {/* Type */}
      <td className="p-4 text-center">{getTypeBadge(customer.customerType)}</td>

      {/* Orders */}
      <td className="p-4 text-center">
        <div className="flex items-center justify-center gap-1">
          <ShoppingBag className="h-4 w-4 text-gray-400" />
          <span className="font-medium">{customer.totalOrders}</span>
        </div>
      </td>

      {/* Total Spent */}
      <td className="p-4 text-right">
        <div className="font-medium text-gray-900">
          {formatCurrency(customer.totalSpent)}
        </div>
      </td>

      {/* Last Order */}
      <td className="p-4 text-center">
        <div className="flex items-center justify-center gap-1 text-sm">
          <Calendar className="h-3 w-3 text-gray-400" />
          <span
            className={customer.lastOrderAt ? "text-gray-900" : "text-gray-500"}
          >
            {formatDate(customer.lastOrderAt)}
          </span>
        </div>
      </td>

      {/* Actions */}
      <td className="p-4 text-center">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={handleViewDetails}>
              <Eye className="mr-2 h-4 w-4" />
              View Details
            </DropdownMenuItem>
            <DropdownMenuItem onClick={handleEditCustomer}>
              <Edit className="mr-2 h-4 w-4" />
              Edit Customer
            </DropdownMenuItem>
            <DropdownMenuItem onClick={handleViewOrders}>
              <ShoppingBag className="mr-2 h-4 w-4" />
              View Orders
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={handleSendEmail}>
              <Mail className="mr-2 h-4 w-4" />
              Send Email
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </td>
    </tr>
  );
};
