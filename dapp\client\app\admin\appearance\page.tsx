"use client";

import { useEffect, useState } from "react";

import {
  Brush,
  Eye,
  Layout,
  Monitor,
  Palette,
  Save,
  Settings,
  Smartphone,
  Tablet,
  Type,
  Upload,
} from "lucide-react";
import { useSearchParams } from "next/navigation";

import { PageHeaderWrapper } from "@/components/common/PageHeaderWrapper";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Slider } from "@/components/ui/slider";
import { Switch } from "@/components/ui/switch";
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";

export default function AppearanceSettings() {
  const [activeTab, setActiveTab] = useState("theme");
  const [isLoading, setIsLoading] = useState(false);
  const [previewMode, setPreviewMode] = useState("desktop");
  const searchParams = useSearchParams();

  // Handle URL parameters for direct tab navigation
  useEffect(() => {
    const tab = searchParams.get("tab");
    if (
      tab &&
      [
        "theme",
        "layout",
        "colors",
        "typography",
        "homepage",
        "mobile",
      ].includes(tab)
    ) {
      setActiveTab(tab);
    }
  }, [searchParams]);

  // Theme settings state
  const [themeSettings, setThemeSettings] = useState({
    selectedTheme: "modern",
    customThemeEnabled: false,
    darkModeEnabled: true,
    animationsEnabled: true,
    transitionSpeed: "normal",
  });

  // Layout settings state
  const [layoutSettings, setLayoutSettings] = useState({
    containerWidth: "1200",
    headerStyle: "fixed",
    footerStyle: "standard",
    sidebarEnabled: false,
    breadcrumbsEnabled: true,
    productGridColumns: "4",
    productCardStyle: "modern",
  });

  // Color settings state
  const [colorSettings, setColorSettings] = useState({
    primaryColor: "#3B82F6",
    secondaryColor: "#10B981",
    accentColor: "#F59E0B",
    backgroundColor: "#FFFFFF",
    textColor: "#1F2937",
    linkColor: "#3B82F6",
    buttonColor: "#3B82F6",
    borderColor: "#E5E7EB",
  });

  // Typography settings state
  const [typographySettings, setTypographySettings] = useState({
    headingFont: "Inter",
    bodyFont: "Inter",
    fontSize: "16",
    lineHeight: "1.6",
    headingWeight: "600",
    bodyWeight: "400",
    letterSpacing: "normal",
  });

  // Homepage settings state
  const [homepageSettings, setHomepageSettings] = useState({
    heroSectionEnabled: true,
    heroTitle: "Welcome to Our Store",
    heroSubtitle: "Discover amazing products at great prices",
    heroButtonText: "Shop Now",
    heroBackgroundImage: "",
    featuredProductsEnabled: true,
    featuredProductsTitle: "Featured Products",
    categoriesEnabled: true,
    categoriesTitle: "Shop by Category",
    testimonialsEnabled: false,
    newsletterEnabled: true,
  });

  // Mobile settings state
  const [mobileSettings, setMobileSettings] = useState({
    mobileMenuStyle: "hamburger",
    mobileProductColumns: "2",
    mobileHeaderFixed: true,
    mobileSearchEnabled: true,
    mobileCartEnabled: true,
    touchOptimized: true,
    swipeGesturesEnabled: true,
  });

  const handleThemeSettingChange = (
    field: string,
    value: string | boolean | number
  ) => {
    setThemeSettings((prev) => ({ ...prev, [field]: value }));
  };

  const handleLayoutSettingChange = (
    field: string,
    value: string | boolean | number
  ) => {
    setLayoutSettings((prev) => ({ ...prev, [field]: value }));
  };

  const handleColorSettingChange = (field: string, value: string) => {
    setColorSettings((prev) => ({ ...prev, [field]: value }));
  };

  const handleTypographySettingChange = (
    field: string,
    value: string | number
  ) => {
    setTypographySettings((prev) => ({ ...prev, [field]: value }));
  };

  const handleHomepageSettingChange = (
    field: string,
    value: string | boolean
  ) => {
    setHomepageSettings((prev) => ({ ...prev, [field]: value }));
  };

  const handleMobileSettingChange = (
    field: string,
    value: string | boolean
  ) => {
    setMobileSettings((prev) => ({ ...prev, [field]: value }));
  };

  const handleSaveSettings = async (section: string) => {
    setIsLoading(true);
    try {
      // TODO: Implement actual API call
      console.log(`Saving ${section} settings`);
      await new Promise((resolve) => setTimeout(resolve, 1000));
    } catch (error) {
      console.error(`Error saving ${section} settings:`, error);
    } finally {
      setIsLoading(false);
    }
  };

  const quickActions = [
    { label: "Preview Store", icon: Eye, action: () => console.log("Preview") },
    {
      label: "Reset Theme",
      icon: Settings,
      action: () => console.log("Reset"),
    },
    {
      label: "Export Theme",
      icon: Upload,
      action: () => console.log("Export"),
    },
  ];

  // Available themes
  const themes = [
    {
      id: "modern",
      name: "Modern",
      description: "Clean and contemporary design",
    },
    {
      id: "classic",
      name: "Classic",
      description: "Traditional e-commerce layout",
    },
    { id: "minimal", name: "Minimal", description: "Simple and elegant" },
    { id: "bold", name: "Bold", description: "Vibrant and eye-catching" },
    { id: "custom", name: "Custom", description: "Create your own theme" },
  ];

  return (
    <>
      <PageHeaderWrapper
        title="Appearance"
        description="Customize your store's visual design, layout, and user experience"
      >
        <div className="flex gap-2">
          {quickActions.map((action, index) => (
            <Button
              key={index}
              variant="outline"
              size="sm"
              onClick={action.action}
              className="hidden sm:flex"
            >
              <action.icon className="mr-2 h-4 w-4" />
              {action.label}
            </Button>
          ))}
          <Button
            size="sm"
            onClick={() => handleSaveSettings("all")}
            disabled={isLoading}
          >
            <Save className="mr-2 h-4 w-4" />
            {isLoading ? "Saving..." : "Save Changes"}
          </Button>
        </div>
      </PageHeaderWrapper>

      <div className="container mx-auto mt-6 space-y-6">
        {/* Preview Mode Selector */}
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium">Preview Mode:</span>
                <div className="flex gap-1">
                  <Button
                    variant={previewMode === "desktop" ? "default" : "outline"}
                    size="sm"
                    onClick={() => setPreviewMode("desktop")}
                  >
                    <Monitor className="mr-1 h-4 w-4" />
                    Desktop
                  </Button>
                  <Button
                    variant={previewMode === "tablet" ? "default" : "outline"}
                    size="sm"
                    onClick={() => setPreviewMode("tablet")}
                  >
                    <Tablet className="mr-1 h-4 w-4" />
                    Tablet
                  </Button>
                  <Button
                    variant={previewMode === "mobile" ? "default" : "outline"}
                    size="sm"
                    onClick={() => setPreviewMode("mobile")}
                  >
                    <Smartphone className="mr-1 h-4 w-4" />
                    Mobile
                  </Button>
                </div>
              </div>
              <Badge variant="secondary">Live Preview</Badge>
            </div>
          </CardContent>
        </Card>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-6">
            <TabsTrigger value="theme">Theme</TabsTrigger>
            <TabsTrigger value="layout">Layout</TabsTrigger>
            <TabsTrigger value="colors">Colors</TabsTrigger>
            <TabsTrigger value="typography">Typography</TabsTrigger>
            <TabsTrigger value="homepage">Homepage</TabsTrigger>
            <TabsTrigger value="mobile">Mobile</TabsTrigger>
          </TabsList>

          {/* Theme Tab */}
          <TabsContent value="theme" className="space-y-6">
            <div className="grid gap-6 lg:grid-cols-3">
              {/* Theme Selection */}
              <div className="lg:col-span-2">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Brush className="h-5 w-5 text-purple-600" />
                      Theme Selection
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid gap-4 md:grid-cols-2">
                      {themes.map((theme) => (
                        <div
                          key={theme.id}
                          className={`cursor-pointer rounded-lg border-2 p-4 transition-all ${
                            themeSettings.selectedTheme === theme.id
                              ? "border-blue-500 bg-blue-50"
                              : "border-gray-200 hover:border-gray-300"
                          }`}
                          onClick={() =>
                            handleThemeSettingChange("selectedTheme", theme.id)
                          }
                        >
                          <div className="mb-2 flex items-center justify-between">
                            <h3 className="font-semibold">{theme.name}</h3>
                            {themeSettings.selectedTheme === theme.id && (
                              <Badge className="bg-blue-500">Active</Badge>
                            )}
                          </div>
                          <p className="text-sm text-gray-600">
                            {theme.description}
                          </p>

                          {/* Theme Preview */}
                          <div className="mt-3 rounded border bg-white p-2">
                            <div className="mb-1 h-2 w-full rounded bg-gray-200"></div>
                            <div className="mb-1 h-1 w-3/4 rounded bg-gray-300"></div>
                            <div className="flex gap-1">
                              <div className="h-4 w-4 rounded bg-blue-400"></div>
                              <div className="h-4 w-4 rounded bg-green-400"></div>
                              <div className="h-4 w-4 rounded bg-yellow-400"></div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>

                    {themeSettings.selectedTheme === "custom" && (
                      <Alert className="mt-4">
                        <Settings className="h-4 w-4" />
                        <AlertDescription>
                          Custom theme selected. Use the Colors and Typography
                          tabs to customize your design.
                        </AlertDescription>
                      </Alert>
                    )}
                  </CardContent>
                </Card>
              </div>

              {/* Theme Options */}
              <div>
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Settings className="h-5 w-5 text-blue-600" />
                      Theme Options
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="font-medium">Dark Mode</div>
                        <div className="text-sm text-gray-500">
                          Enable dark theme variant
                        </div>
                      </div>
                      <Switch
                        checked={themeSettings.darkModeEnabled}
                        onCheckedChange={(checked) =>
                          handleThemeSettingChange("darkModeEnabled", checked)
                        }
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <div className="font-medium">Animations</div>
                        <div className="text-sm text-gray-500">
                          Enable smooth transitions
                        </div>
                      </div>
                      <Switch
                        checked={themeSettings.animationsEnabled}
                        onCheckedChange={(checked) =>
                          handleThemeSettingChange("animationsEnabled", checked)
                        }
                      />
                    </div>

                    <div>
                      <Label htmlFor="transitionSpeed">Transition Speed</Label>
                      <Select
                        value={themeSettings.transitionSpeed}
                        onValueChange={(value) =>
                          handleThemeSettingChange("transitionSpeed", value)
                        }
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="slow">Slow</SelectItem>
                          <SelectItem value="normal">Normal</SelectItem>
                          <SelectItem value="fast">Fast</SelectItem>
                          <SelectItem value="none">No Transitions</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <div className="font-medium">Custom Theme</div>
                        <div className="text-sm text-gray-500">
                          Enable advanced customization
                        </div>
                      </div>
                      <Switch
                        checked={themeSettings.customThemeEnabled}
                        onCheckedChange={(checked) =>
                          handleThemeSettingChange(
                            "customThemeEnabled",
                            checked
                          )
                        }
                      />
                    </div>

                    <div className="border-t pt-4">
                      <Button
                        onClick={() => handleSaveSettings("theme")}
                        disabled={isLoading}
                        className="w-full"
                      >
                        <Save className="mr-2 h-4 w-4" />
                        {isLoading ? "Saving..." : "Save Theme Settings"}
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>

          {/* Layout Tab */}
          <TabsContent value="layout" className="space-y-6">
            <div className="grid gap-6 lg:grid-cols-2">
              {/* Layout Structure */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Layout className="h-5 w-5 text-blue-600" />
                    Layout Structure
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="containerWidth">Container Width (px)</Label>
                    <Select
                      value={layoutSettings.containerWidth}
                      onValueChange={(value) =>
                        handleLayoutSettingChange("containerWidth", value)
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="1140">1140px (Bootstrap)</SelectItem>
                        <SelectItem value="1200">1200px (Standard)</SelectItem>
                        <SelectItem value="1280">1280px (Wide)</SelectItem>
                        <SelectItem value="1440">
                          1440px (Extra Wide)
                        </SelectItem>
                        <SelectItem value="full">Full Width</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="headerStyle">Header Style</Label>
                    <Select
                      value={layoutSettings.headerStyle}
                      onValueChange={(value) =>
                        handleLayoutSettingChange("headerStyle", value)
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="fixed">Fixed Header</SelectItem>
                        <SelectItem value="static">Static Header</SelectItem>
                        <SelectItem value="sticky">Sticky Header</SelectItem>
                        <SelectItem value="transparent">
                          Transparent Header
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="footerStyle">Footer Style</Label>
                    <Select
                      value={layoutSettings.footerStyle}
                      onValueChange={(value) =>
                        handleLayoutSettingChange("footerStyle", value)
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="standard">
                          Standard Footer
                        </SelectItem>
                        <SelectItem value="minimal">Minimal Footer</SelectItem>
                        <SelectItem value="extended">
                          Extended Footer
                        </SelectItem>
                        <SelectItem value="sticky">Sticky Footer</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">Sidebar</div>
                      <div className="text-sm text-gray-500">
                        Enable sidebar navigation
                      </div>
                    </div>
                    <Switch
                      checked={layoutSettings.sidebarEnabled}
                      onCheckedChange={(checked) =>
                        handleLayoutSettingChange("sidebarEnabled", checked)
                      }
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">Breadcrumbs</div>
                      <div className="text-sm text-gray-500">
                        Show navigation breadcrumbs
                      </div>
                    </div>
                    <Switch
                      checked={layoutSettings.breadcrumbsEnabled}
                      onCheckedChange={(checked) =>
                        handleLayoutSettingChange("breadcrumbsEnabled", checked)
                      }
                    />
                  </div>
                </CardContent>
              </Card>

              {/* Product Display */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Settings className="h-5 w-5 text-green-600" />
                    Product Display
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="productGridColumns">
                      Grid Columns (Desktop)
                    </Label>
                    <Select
                      value={layoutSettings.productGridColumns}
                      onValueChange={(value) =>
                        handleLayoutSettingChange("productGridColumns", value)
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="2">2 Columns</SelectItem>
                        <SelectItem value="3">3 Columns</SelectItem>
                        <SelectItem value="4">4 Columns</SelectItem>
                        <SelectItem value="5">5 Columns</SelectItem>
                        <SelectItem value="6">6 Columns</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="productCardStyle">Product Card Style</Label>
                    <Select
                      value={layoutSettings.productCardStyle}
                      onValueChange={(value) =>
                        handleLayoutSettingChange("productCardStyle", value)
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="modern">Modern Cards</SelectItem>
                        <SelectItem value="classic">Classic Cards</SelectItem>
                        <SelectItem value="minimal">Minimal Cards</SelectItem>
                        <SelectItem value="overlay">Overlay Style</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="border-t pt-4">
                    <div className="mb-3 text-sm font-medium text-gray-700">
                      Layout Preview
                    </div>
                    <div className="rounded border bg-gray-50 p-3">
                      <div className="mb-2 h-4 w-full rounded bg-blue-200"></div>
                      <div className="grid grid-cols-4 gap-2">
                        <div className="h-8 rounded bg-white"></div>
                        <div className="h-8 rounded bg-white"></div>
                        <div className="h-8 rounded bg-white"></div>
                        <div className="h-8 rounded bg-white"></div>
                      </div>
                      <div className="mt-2 h-3 w-full rounded bg-gray-300"></div>
                    </div>
                  </div>

                  <div className="flex justify-end">
                    <Button
                      onClick={() => handleSaveSettings("layout")}
                      disabled={isLoading}
                    >
                      <Save className="mr-2 h-4 w-4" />
                      {isLoading ? "Saving..." : "Save Layout Settings"}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Colors Tab */}
          <TabsContent value="colors" className="space-y-6">
            <div className="grid gap-6 lg:grid-cols-2">
              {/* Primary Colors */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Palette className="h-5 w-5 text-purple-600" />
                    Primary Colors
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="primaryColor">Primary Color</Label>
                    <div className="flex gap-2">
                      <Input
                        id="primaryColor"
                        value={colorSettings.primaryColor}
                        onChange={(e) =>
                          handleColorSettingChange(
                            "primaryColor",
                            e.target.value
                          )
                        }
                        placeholder="#3B82F6"
                      />
                      <div
                        className="h-10 w-10 cursor-pointer rounded border"
                        style={{ backgroundColor: colorSettings.primaryColor }}
                        onClick={() => {
                          // TODO: Open color picker
                          console.log("Open color picker");
                        }}
                      ></div>
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="secondaryColor">Secondary Color</Label>
                    <div className="flex gap-2">
                      <Input
                        id="secondaryColor"
                        value={colorSettings.secondaryColor}
                        onChange={(e) =>
                          handleColorSettingChange(
                            "secondaryColor",
                            e.target.value
                          )
                        }
                        placeholder="#10B981"
                      />
                      <div
                        className="h-10 w-10 cursor-pointer rounded border"
                        style={{
                          backgroundColor: colorSettings.secondaryColor,
                        }}
                      ></div>
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="accentColor">Accent Color</Label>
                    <div className="flex gap-2">
                      <Input
                        id="accentColor"
                        value={colorSettings.accentColor}
                        onChange={(e) =>
                          handleColorSettingChange(
                            "accentColor",
                            e.target.value
                          )
                        }
                        placeholder="#F59E0B"
                      />
                      <div
                        className="h-10 w-10 cursor-pointer rounded border"
                        style={{ backgroundColor: colorSettings.accentColor }}
                      ></div>
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="backgroundColor">Background Color</Label>
                    <div className="flex gap-2">
                      <Input
                        id="backgroundColor"
                        value={colorSettings.backgroundColor}
                        onChange={(e) =>
                          handleColorSettingChange(
                            "backgroundColor",
                            e.target.value
                          )
                        }
                        placeholder="#FFFFFF"
                      />
                      <div
                        className="h-10 w-10 cursor-pointer rounded border"
                        style={{
                          backgroundColor: colorSettings.backgroundColor,
                        }}
                      ></div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Text & UI Colors */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Type className="h-5 w-5 text-blue-600" />
                    Text & UI Colors
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="textColor">Text Color</Label>
                    <div className="flex gap-2">
                      <Input
                        id="textColor"
                        value={colorSettings.textColor}
                        onChange={(e) =>
                          handleColorSettingChange("textColor", e.target.value)
                        }
                        placeholder="#1F2937"
                      />
                      <div
                        className="h-10 w-10 cursor-pointer rounded border"
                        style={{ backgroundColor: colorSettings.textColor }}
                      ></div>
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="linkColor">Link Color</Label>
                    <div className="flex gap-2">
                      <Input
                        id="linkColor"
                        value={colorSettings.linkColor}
                        onChange={(e) =>
                          handleColorSettingChange("linkColor", e.target.value)
                        }
                        placeholder="#3B82F6"
                      />
                      <div
                        className="h-10 w-10 cursor-pointer rounded border"
                        style={{ backgroundColor: colorSettings.linkColor }}
                      ></div>
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="buttonColor">Button Color</Label>
                    <div className="flex gap-2">
                      <Input
                        id="buttonColor"
                        value={colorSettings.buttonColor}
                        onChange={(e) =>
                          handleColorSettingChange(
                            "buttonColor",
                            e.target.value
                          )
                        }
                        placeholder="#3B82F6"
                      />
                      <div
                        className="h-10 w-10 cursor-pointer rounded border"
                        style={{ backgroundColor: colorSettings.buttonColor }}
                      ></div>
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="borderColor">Border Color</Label>
                    <div className="flex gap-2">
                      <Input
                        id="borderColor"
                        value={colorSettings.borderColor}
                        onChange={(e) =>
                          handleColorSettingChange(
                            "borderColor",
                            e.target.value
                          )
                        }
                        placeholder="#E5E7EB"
                      />
                      <div
                        className="h-10 w-10 cursor-pointer rounded border"
                        style={{ backgroundColor: colorSettings.borderColor }}
                      ></div>
                    </div>
                  </div>

                  <div className="border-t pt-4">
                    <div className="mb-3 text-sm font-medium text-gray-700">
                      Color Preview
                    </div>
                    <div
                      className="rounded border p-3"
                      style={{ backgroundColor: colorSettings.backgroundColor }}
                    >
                      <h3
                        style={{ color: colorSettings.textColor }}
                        className="mb-2 font-semibold"
                      >
                        Sample Heading
                      </h3>
                      <p
                        style={{ color: colorSettings.textColor }}
                        className="mb-2 text-sm"
                      >
                        This is sample text with a{" "}
                        <span style={{ color: colorSettings.linkColor }}>
                          sample link
                        </span>
                        .
                      </p>
                      <button
                        style={{
                          backgroundColor: colorSettings.buttonColor,
                          color: "white",
                        }}
                        className="rounded px-3 py-1 text-sm"
                      >
                        Sample Button
                      </button>
                    </div>
                  </div>

                  <div className="flex justify-end">
                    <Button
                      onClick={() => handleSaveSettings("colors")}
                      disabled={isLoading}
                    >
                      <Save className="mr-2 h-4 w-4" />
                      {isLoading ? "Saving..." : "Save Color Settings"}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Typography Tab */}
          <TabsContent value="typography" className="space-y-6">
            <div className="grid gap-6 lg:grid-cols-2">
              {/* Font Settings */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Type className="h-5 w-5 text-blue-600" />
                    Font Settings
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="headingFont">Heading Font</Label>
                    <Select
                      value={typographySettings.headingFont}
                      onValueChange={(value) =>
                        handleTypographySettingChange("headingFont", value)
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Inter">Inter</SelectItem>
                        <SelectItem value="Roboto">Roboto</SelectItem>
                        <SelectItem value="Open Sans">Open Sans</SelectItem>
                        <SelectItem value="Lato">Lato</SelectItem>
                        <SelectItem value="Poppins">Poppins</SelectItem>
                        <SelectItem value="Montserrat">Montserrat</SelectItem>
                        <SelectItem value="Playfair Display">
                          Playfair Display
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="bodyFont">Body Font</Label>
                    <Select
                      value={typographySettings.bodyFont}
                      onValueChange={(value) =>
                        handleTypographySettingChange("bodyFont", value)
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Inter">Inter</SelectItem>
                        <SelectItem value="Roboto">Roboto</SelectItem>
                        <SelectItem value="Open Sans">Open Sans</SelectItem>
                        <SelectItem value="Lato">Lato</SelectItem>
                        <SelectItem value="Source Sans Pro">
                          Source Sans Pro
                        </SelectItem>
                        <SelectItem value="System UI">System UI</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="fontSize">Base Font Size (px)</Label>
                    <div className="flex items-center space-x-4">
                      <Slider
                        value={[parseInt(typographySettings.fontSize)]}
                        onValueChange={(value) =>
                          handleTypographySettingChange(
                            "fontSize",
                            value[0].toString()
                          )
                        }
                        max={24}
                        min={12}
                        step={1}
                        className="flex-1"
                      />
                      <span className="w-12 text-sm">
                        {typographySettings.fontSize}px
                      </span>
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="lineHeight">Line Height</Label>
                    <Select
                      value={typographySettings.lineHeight}
                      onValueChange={(value) =>
                        handleTypographySettingChange("lineHeight", value)
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="1.2">1.2 (Tight)</SelectItem>
                        <SelectItem value="1.4">1.4 (Snug)</SelectItem>
                        <SelectItem value="1.6">1.6 (Normal)</SelectItem>
                        <SelectItem value="1.8">1.8 (Relaxed)</SelectItem>
                        <SelectItem value="2.0">2.0 (Loose)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </CardContent>
              </Card>

              {/* Font Weights & Spacing */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Settings className="h-5 w-5 text-green-600" />
                    Font Weights & Spacing
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="headingWeight">Heading Weight</Label>
                    <Select
                      value={typographySettings.headingWeight}
                      onValueChange={(value) =>
                        handleTypographySettingChange("headingWeight", value)
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="400">400 (Normal)</SelectItem>
                        <SelectItem value="500">500 (Medium)</SelectItem>
                        <SelectItem value="600">600 (Semi Bold)</SelectItem>
                        <SelectItem value="700">700 (Bold)</SelectItem>
                        <SelectItem value="800">800 (Extra Bold)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="bodyWeight">Body Weight</Label>
                    <Select
                      value={typographySettings.bodyWeight}
                      onValueChange={(value) =>
                        handleTypographySettingChange("bodyWeight", value)
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="300">300 (Light)</SelectItem>
                        <SelectItem value="400">400 (Normal)</SelectItem>
                        <SelectItem value="500">500 (Medium)</SelectItem>
                        <SelectItem value="600">600 (Semi Bold)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="letterSpacing">Letter Spacing</Label>
                    <Select
                      value={typographySettings.letterSpacing}
                      onValueChange={(value) =>
                        handleTypographySettingChange("letterSpacing", value)
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="tight">Tight (-0.025em)</SelectItem>
                        <SelectItem value="normal">Normal (0em)</SelectItem>
                        <SelectItem value="wide">Wide (0.025em)</SelectItem>
                        <SelectItem value="wider">Wider (0.05em)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="border-t pt-4">
                    <div className="mb-3 text-sm font-medium text-gray-700">
                      Typography Preview
                    </div>
                    <div className="space-y-2">
                      <h1
                        style={{
                          fontFamily: typographySettings.headingFont,
                          fontWeight: typographySettings.headingWeight,
                          fontSize: `${parseInt(typographySettings.fontSize) * 2}px`,
                          lineHeight: typographySettings.lineHeight,
                        }}
                      >
                        Sample Heading
                      </h1>
                      <p
                        style={{
                          fontFamily: typographySettings.bodyFont,
                          fontWeight: typographySettings.bodyWeight,
                          fontSize: `${typographySettings.fontSize}px`,
                          lineHeight: typographySettings.lineHeight,
                        }}
                      >
                        This is sample body text that demonstrates the selected
                        typography settings.
                      </p>
                    </div>
                  </div>

                  <div className="flex justify-end">
                    <Button
                      onClick={() => handleSaveSettings("typography")}
                      disabled={isLoading}
                    >
                      <Save className="mr-2 h-4 w-4" />
                      {isLoading ? "Saving..." : "Save Typography Settings"}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Homepage Tab */}
          <TabsContent value="homepage" className="space-y-6">
            <div className="grid gap-6 lg:grid-cols-2">
              {/* Hero Section */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Layout className="h-5 w-5 text-purple-600" />
                    Hero Section
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">Enable Hero Section</div>
                      <div className="text-sm text-gray-500">
                        Show hero banner on homepage
                      </div>
                    </div>
                    <Switch
                      checked={homepageSettings.heroSectionEnabled}
                      onCheckedChange={(checked) =>
                        handleHomepageSettingChange(
                          "heroSectionEnabled",
                          checked
                        )
                      }
                    />
                  </div>

                  {homepageSettings.heroSectionEnabled && (
                    <>
                      <div>
                        <Label htmlFor="heroTitle">Hero Title</Label>
                        <Input
                          id="heroTitle"
                          value={homepageSettings.heroTitle}
                          onChange={(e) =>
                            handleHomepageSettingChange(
                              "heroTitle",
                              e.target.value
                            )
                          }
                          placeholder="Welcome to Our Store"
                        />
                      </div>

                      <div>
                        <Label htmlFor="heroSubtitle">Hero Subtitle</Label>
                        <Textarea
                          id="heroSubtitle"
                          value={homepageSettings.heroSubtitle}
                          onChange={(e) =>
                            handleHomepageSettingChange(
                              "heroSubtitle",
                              e.target.value
                            )
                          }
                          placeholder="Discover amazing products at great prices"
                          rows={2}
                        />
                      </div>

                      <div>
                        <Label htmlFor="heroButtonText">Button Text</Label>
                        <Input
                          id="heroButtonText"
                          value={homepageSettings.heroButtonText}
                          onChange={(e) =>
                            handleHomepageSettingChange(
                              "heroButtonText",
                              e.target.value
                            )
                          }
                          placeholder="Shop Now"
                        />
                      </div>

                      <div>
                        <Label htmlFor="heroBackgroundImage">
                          Background Image URL
                        </Label>
                        <Input
                          id="heroBackgroundImage"
                          value={homepageSettings.heroBackgroundImage}
                          onChange={(e) =>
                            handleHomepageSettingChange(
                              "heroBackgroundImage",
                              e.target.value
                            )
                          }
                          placeholder="https://example.com/hero-bg.jpg"
                        />
                        <Button
                          variant="outline"
                          size="sm"
                          className="mt-2 w-full"
                        >
                          <Upload className="mr-2 h-4 w-4" />
                          Upload Image
                        </Button>
                      </div>
                    </>
                  )}
                </CardContent>
              </Card>

              {/* Homepage Sections */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Settings className="h-5 w-5 text-blue-600" />
                    Homepage Sections
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">Featured Products</div>
                      <div className="text-sm text-gray-500">
                        Show featured products section
                      </div>
                    </div>
                    <Switch
                      checked={homepageSettings.featuredProductsEnabled}
                      onCheckedChange={(checked) =>
                        handleHomepageSettingChange(
                          "featuredProductsEnabled",
                          checked
                        )
                      }
                    />
                  </div>

                  {homepageSettings.featuredProductsEnabled && (
                    <div>
                      <Label htmlFor="featuredProductsTitle">
                        Section Title
                      </Label>
                      <Input
                        id="featuredProductsTitle"
                        value={homepageSettings.featuredProductsTitle}
                        onChange={(e) =>
                          handleHomepageSettingChange(
                            "featuredProductsTitle",
                            e.target.value
                          )
                        }
                        placeholder="Featured Products"
                      />
                    </div>
                  )}

                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">Categories</div>
                      <div className="text-sm text-gray-500">
                        Show categories section
                      </div>
                    </div>
                    <Switch
                      checked={homepageSettings.categoriesEnabled}
                      onCheckedChange={(checked) =>
                        handleHomepageSettingChange(
                          "categoriesEnabled",
                          checked
                        )
                      }
                    />
                  </div>

                  {homepageSettings.categoriesEnabled && (
                    <div>
                      <Label htmlFor="categoriesTitle">Section Title</Label>
                      <Input
                        id="categoriesTitle"
                        value={homepageSettings.categoriesTitle}
                        onChange={(e) =>
                          handleHomepageSettingChange(
                            "categoriesTitle",
                            e.target.value
                          )
                        }
                        placeholder="Shop by Category"
                      />
                    </div>
                  )}

                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">Testimonials</div>
                      <div className="text-sm text-gray-500">
                        Show customer testimonials
                      </div>
                    </div>
                    <Switch
                      checked={homepageSettings.testimonialsEnabled}
                      onCheckedChange={(checked) =>
                        handleHomepageSettingChange(
                          "testimonialsEnabled",
                          checked
                        )
                      }
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">Newsletter Signup</div>
                      <div className="text-sm text-gray-500">
                        Show newsletter subscription
                      </div>
                    </div>
                    <Switch
                      checked={homepageSettings.newsletterEnabled}
                      onCheckedChange={(checked) =>
                        handleHomepageSettingChange(
                          "newsletterEnabled",
                          checked
                        )
                      }
                    />
                  </div>

                  <div className="flex justify-end">
                    <Button
                      onClick={() => handleSaveSettings("homepage")}
                      disabled={isLoading}
                    >
                      <Save className="mr-2 h-4 w-4" />
                      {isLoading ? "Saving..." : "Save Homepage Settings"}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Mobile Tab */}
          <TabsContent value="mobile" className="space-y-6">
            <div className="grid gap-6 lg:grid-cols-2">
              {/* Mobile Navigation */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Smartphone className="h-5 w-5 text-blue-600" />
                    Mobile Navigation
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="mobileMenuStyle">Menu Style</Label>
                    <Select
                      value={mobileSettings.mobileMenuStyle}
                      onValueChange={(value) =>
                        handleMobileSettingChange("mobileMenuStyle", value)
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="hamburger">
                          Hamburger Menu
                        </SelectItem>
                        <SelectItem value="bottom-tabs">Bottom Tabs</SelectItem>
                        <SelectItem value="slide-out">
                          Slide Out Menu
                        </SelectItem>
                        <SelectItem value="full-screen">
                          Full Screen Menu
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">Fixed Header</div>
                      <div className="text-sm text-gray-500">
                        Keep header visible while scrolling
                      </div>
                    </div>
                    <Switch
                      checked={mobileSettings.mobileHeaderFixed}
                      onCheckedChange={(checked) =>
                        handleMobileSettingChange("mobileHeaderFixed", checked)
                      }
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">Search Bar</div>
                      <div className="text-sm text-gray-500">
                        Show search in mobile header
                      </div>
                    </div>
                    <Switch
                      checked={mobileSettings.mobileSearchEnabled}
                      onCheckedChange={(checked) =>
                        handleMobileSettingChange(
                          "mobileSearchEnabled",
                          checked
                        )
                      }
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">Cart Icon</div>
                      <div className="text-sm text-gray-500">
                        Show cart icon in mobile header
                      </div>
                    </div>
                    <Switch
                      checked={mobileSettings.mobileCartEnabled}
                      onCheckedChange={(checked) =>
                        handleMobileSettingChange("mobileCartEnabled", checked)
                      }
                    />
                  </div>
                </CardContent>
              </Card>

              {/* Mobile Experience */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Settings className="h-5 w-5 text-green-600" />
                    Mobile Experience
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="mobileProductColumns">
                      Product Grid Columns
                    </Label>
                    <Select
                      value={mobileSettings.mobileProductColumns}
                      onValueChange={(value) =>
                        handleMobileSettingChange("mobileProductColumns", value)
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="1">1 Column</SelectItem>
                        <SelectItem value="2">2 Columns</SelectItem>
                        <SelectItem value="3">3 Columns</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">Touch Optimized</div>
                      <div className="text-sm text-gray-500">
                        Larger touch targets for mobile
                      </div>
                    </div>
                    <Switch
                      checked={mobileSettings.touchOptimized}
                      onCheckedChange={(checked) =>
                        handleMobileSettingChange("touchOptimized", checked)
                      }
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">Swipe Gestures</div>
                      <div className="text-sm text-gray-500">
                        Enable swipe navigation
                      </div>
                    </div>
                    <Switch
                      checked={mobileSettings.swipeGesturesEnabled}
                      onCheckedChange={(checked) =>
                        handleMobileSettingChange(
                          "swipeGesturesEnabled",
                          checked
                        )
                      }
                    />
                  </div>

                  <div className="border-t pt-4">
                    <div className="mb-3 text-sm font-medium text-gray-700">
                      Mobile Preview
                    </div>
                    <div className="mx-auto w-32 rounded-lg border bg-gray-100 p-2">
                      <div className="mb-1 h-3 w-full rounded bg-blue-200"></div>
                      <div className="grid grid-cols-2 gap-1">
                        <div className="h-6 rounded bg-white"></div>
                        <div className="h-6 rounded bg-white"></div>
                      </div>
                      <div className="mt-1 h-2 w-full rounded bg-gray-300"></div>
                    </div>
                  </div>

                  <div className="flex justify-end">
                    <Button
                      onClick={() => handleSaveSettings("mobile")}
                      disabled={isLoading}
                    >
                      <Save className="mr-2 h-4 w-4" />
                      {isLoading ? "Saving..." : "Save Mobile Settings"}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </>
  );
}
