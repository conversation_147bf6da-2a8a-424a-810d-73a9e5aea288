import { Dispatch, SetStateAction } from "react";

import {
  <PERSON>Errors,
  UseFormRegister,
  UseFormSetValue,
  UseFormWatch,
} from "react-hook-form";

import { ProductFormData } from "@/schemas/productSchema";
import { SectionId } from "@/types/form-section";

import { AdvancedSection } from "./AdvancedSection";
import { AvailabilitySection } from "./AvailabilitySection";
import { BasicInfoSection } from "./BasicInfoSection";
import { DetailsSection } from "./DetailsSection";
import { InventorySection } from "./InventorySection";
import { MediaSection } from "./MediaSection";
import { PricingSection } from "./PricingSection";
import { SEOSection } from "./SEOSection";
import { ShippingSection } from "./ShippingSection";
import { WarrantySection } from "./WarrantySection";

type FormSectionRendererProps = {
  currentSection: SectionId;
  register: UseFormRegister<ProductFormData>;
  errors: FieldErrors<ProductFormData>;
  setValue: UseFormSetValue<ProductFormData>;
  watch: UseFormWatch<ProductFormData>;
  tags: string[];
  setTags: Dispatch<SetStateAction<string[]>>;
  images: { key: string; url: string }[];
  setImages: Dispatch<SetStateAction<{ key: string; url: string }[]>>;
  imageAltTexts: string[];
  setImageAltTexts: Dispatch<SetStateAction<string[]>>;
};

/**
 * Renders the appropriate form section based on the current section ID
 */
export const FormSectionRenderer = ({
  currentSection,
  register,
  errors,
  setValue,
  watch,
  tags,
  setTags,
  images,
  setImages,
  imageAltTexts,
  setImageAltTexts,
}: FormSectionRendererProps) => {
  // Render the appropriate section based on currentSection
  switch (currentSection) {
    case "basic-info":
      return (
        <BasicInfoSection
          register={register}
          errors={errors}
          setValue={setValue}
          watch={watch}
        />
      );

    case "pricing":
      return (
        <PricingSection
          register={register}
          errors={errors}
          setValue={setValue}
          watch={watch}
        />
      );

    case "inventory":
      return (
        <InventorySection
          register={register}
          errors={errors}
          setValue={setValue}
          watch={watch}
        />
      );

    case "details":
      return (
        <DetailsSection
          register={register}
          errors={errors}
          setValue={setValue}
          watch={watch}
          tags={tags}
          setTags={setTags}
        />
      );

    case "media":
      return (
        <MediaSection
          register={register}
          errors={errors}
          setValue={setValue}
          watch={watch}
          images={images}
          setImages={setImages}
          imageAltTexts={imageAltTexts}
          setImageAltTexts={setImageAltTexts}
        />
      );

    case "shipping":
      return (
        <ShippingSection
          register={register}
          errors={errors}
          setValue={setValue}
          watch={watch}
        />
      );

    case "availability":
      return (
        <AvailabilitySection
          register={register}
          errors={errors}
          setValue={setValue}
          watch={watch}
        />
      );

    case "seo":
      return (
        <SEOSection
          register={register}
          errors={errors}
          setValue={setValue}
          watch={watch}
        />
      );

    case "warranty":
      return (
        <WarrantySection
          register={register}
          errors={errors}
          setValue={setValue}
          watch={watch}
        />
      );

    case "advanced":
      return (
        <AdvancedSection
          register={register}
          errors={errors}
          setValue={setValue}
          watch={watch}
        />
      );

    default:
      return <div>Section not implemented</div>;
  }
};
