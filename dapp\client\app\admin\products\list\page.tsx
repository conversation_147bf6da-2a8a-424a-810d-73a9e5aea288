"use client";

import { useRef } from "react";

import { PageHeaderWrapper } from "@/components/common/PageHeaderWrapper";
import { ProductListActions } from "@/components/pages/products/ProductListActions";
import { ProductListWrapper } from "@/components/pages/products/ProductListWrapper";

export default function AdminProductsList() {
  const wrapperRef = useRef<{ handleRefresh: () => void; loading: boolean }>(
    null
  );

  const handleRefresh = () => {
    wrapperRef.current?.handleRefresh();
  };

  return (
    <>
      <PageHeaderWrapper
        title="Products"
        description="Browse and manage your product catalog"
      >
        <ProductListActions
          onRefresh={handleRefresh}
          loading={wrapperRef.current?.loading || false}
        />
      </PageHeaderWrapper>

      <div className="container mx-auto mt-6">
        <ProductListWrapper ref={wrapperRef} />
      </div>
    </>
  );
}
