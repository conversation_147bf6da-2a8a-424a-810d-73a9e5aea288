"use client";

import { AlertCircle, RefreshCw } from "lucide-react";

import { Alert, AlertDescription } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";

import { ProductCard } from "./ProductCard";

interface ProductGridProps {
  products?: unknown[];
  loading?: boolean;
  error?: string | null;
  onRefresh?: () => void;
}

export const ProductGrid = ({
  products = [],
  loading = false,
  error = null,
  onRefresh,
}: ProductGridProps) => {
  if (loading) {
    return (
      <section className="grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5">
        {Array.from({ length: 8 }).map((_, index) => (
          <div key={index} className="w-full">
            <Skeleton className="h-64 w-full rounded-lg" />
            <div className="mt-4 space-y-2">
              <Skeleton className="h-4 w-3/4" />
              <Skeleton className="h-4 w-1/2" />
              <Skeleton className="h-6 w-1/4" />
            </div>
          </div>
        ))}
      </section>
    );
  }

  if (error) {
    return (
      <Alert className="mx-auto max-w-md">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription className="flex items-center justify-between">
          <span>Failed to load products: {error}</span>
          <Button
            variant="outline"
            size="sm"
            onClick={onRefresh}
            className="ml-2"
          >
            <RefreshCw className="mr-1 h-4 w-4" />
            Retry
          </Button>
        </AlertDescription>
      </Alert>
    );
  }

  if (!products || products.length === 0) {
    return (
      <div className="py-12 text-center">
        <p className="text-lg text-gray-500">No products found</p>
        <Button variant="outline" onClick={onRefresh} className="mt-4">
          <RefreshCw className="mr-2 h-4 w-4" />
          Refresh
        </Button>
      </div>
    );
  }

  return (
    <section className="grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5">
      {products.map((product, index) => (
        <ProductCard
          key={(product as any)?._id || (product as any)?.id || index}
          product={product as any}
          index={index}
          onDelete={onRefresh}
        />
      ))}
    </section>
  );
};
