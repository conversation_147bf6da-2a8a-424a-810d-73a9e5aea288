// schemas/commonSchemas.ts
import { z } from "zod";

export const currencyUnitSchema = z.enum(["EUR"]);

export const dimensionUnitSchema = z.enum(["mm", "cm"]);

export const weightUnitSchema = z.enum(["g", "kg"]);

export const ageRestrictionSchema = z.enum(["none", "18+", "21+"]);

export const conditionSchema = z.enum([
  "new",
  "like-new",
  "excellent",
  "good",
  "fair",
  "used",
  "refurbished",
  "vintage",
  "antique",
  "damaged",
]);

export const shippingStatusSchema = z.enum([
  "not-shipped",
  "shipped",
  "in-transit",
  "delivered",
  "delayed",
]);
