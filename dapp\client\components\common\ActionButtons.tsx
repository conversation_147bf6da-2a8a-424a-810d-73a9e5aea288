"use client";

import React from "react";

import clsx from "clsx";

export type ActionButton = {
  label: string;
  onClick: () => void;
  icon?: React.ReactNode;
  variant?: "primary" | "secondary";
};

type ActionButtonsProps = {
  buttons: ActionButton[];
  className?: string;
};

export const ActionButtons: React.FC<ActionButtonsProps> = ({
  buttons,
  className,
}) => {
  return (
    <div className={clsx("flex gap-2", className)}>
      {buttons.map((btn, index) => (
        <button
          key={index}
          onClick={btn.onClick}
          className={clsx(
            "flex items-center gap-2 rounded px-4 py-2 text-sm font-medium transition-colors",
            {
              "bg-blue-600 text-white hover:bg-blue-700":
                btn.variant === "primary",
              "border text-gray-600 hover:bg-gray-50":
                btn.variant !== "primary",
            }
          )}
        >
          {btn.icon && <span className="text-sm">{btn.icon}</span>}

          {btn.label}
        </button>
      ))}
    </div>
  );
};
