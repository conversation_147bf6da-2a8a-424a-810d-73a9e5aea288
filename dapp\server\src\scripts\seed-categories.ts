import "dotenv/config";
import mongoose from "mongoose";

import { config } from "../config/load-env.config";
import { Category } from "../models/category.model";

/**
 * Script to seed initial categories to the database
 */
async function seedCategories() {
  try {
    // Connect to MongoDB
    console.log("Connecting to MongoDB...");
    await mongoose.connect(config.db.connection, {
      dbName: config.db.name,
      retryWrites: true,
      w: "majority",
    });
    console.log("Connected to MongoDB");

    // Check if categories already exist
    const existingCategories = await Category.countDocuments();
    if (existingCategories > 0) {
      console.log(
        `Found ${existingCategories} existing categories. Skipping seed.`
      );
      return;
    }

    // Initial categories data matching the frontend mock data
    const initialCategories = [
      {
        name: "Electronics",
        description:
          "Electronic devices, gadgets, and accessories for modern living",
        slug: "electronics",
        icon: "📱",
        color: "#3B82F6",
        isActive: true,
        productCount: 0,
        sortOrder: 1,
      },
      {
        name: "Clothing & Fashion",
        description:
          "Trendy clothing, shoes, and fashion accessories for all occasions",
        slug: "clothing-fashion",
        icon: "👕",
        color: "#EF4444",
        isActive: true,
        productCount: 0,
        sortOrder: 2,
      },
      {
        name: "Home & Garden",
        description: "Everything you need to make your house a beautiful home",
        slug: "home-garden",
        icon: "🏠",
        color: "#10B981",
        isActive: true,
        productCount: 0,
        sortOrder: 3,
      },
      {
        name: "Sports & Outdoors",
        description:
          "Gear and equipment for sports, fitness, and outdoor adventures",
        slug: "sports-outdoors",
        icon: "⚽",
        color: "#F59E0B",
        isActive: true,
        productCount: 0,
        sortOrder: 4,
      },
      {
        name: "Books & Media",
        description: "Books, movies, music, and educational materials",
        slug: "books-media",
        icon: "📚",
        color: "#8B5CF6",
        isActive: true,
        productCount: 0,
        sortOrder: 5,
      },
      {
        name: "Health & Beauty",
        description: "Personal care, cosmetics, and wellness products",
        slug: "health-beauty",
        icon: "💄",
        color: "#EC4899",
        isActive: true,
        productCount: 0,
        sortOrder: 6,
      },
      {
        name: "Toys & Games",
        description: "Fun toys, games, and entertainment for all ages",
        slug: "toys-games",
        icon: "🎮",
        color: "#06B6D4",
        isActive: true,
        productCount: 0,
        sortOrder: 7,
      },
      {
        name: "Automotive",
        description: "Car parts, accessories, and automotive tools",
        slug: "automotive",
        icon: "🚗",
        color: "#6B7280",
        isActive: true,
        productCount: 0,
        sortOrder: 8,
      },
      {
        name: "Food & Beverages",
        description: "Gourmet foods, snacks, and specialty beverages",
        slug: "food-beverages",
        icon: "🍕",
        color: "#F97316",
        isActive: true,
        productCount: 0,
        sortOrder: 9,
      },
      {
        name: "Pet Supplies",
        description:
          "Everything your furry friends need to stay happy and healthy",
        slug: "pet-supplies",
        icon: "🐕",
        color: "#84CC16",
        isActive: true,
        productCount: 0,
        sortOrder: 10,
      },
    ];

    // Insert categories
    console.log("Seeding categories...");
    const createdCategories = await Category.insertMany(initialCategories);
    console.log(`Successfully created ${createdCategories.length} categories`);

    // Display created categories
    createdCategories.forEach((category) => {
      console.log(`- ${category.name} (${category.slug})`);
    });
  } catch (error) {
    console.error("Error seeding categories:", error);
  } finally {
    // Close the database connection
    await mongoose.connection.close();
    console.log("Database connection closed");
  }
}

// Run the script
seedCategories();

export { seedCategories };
