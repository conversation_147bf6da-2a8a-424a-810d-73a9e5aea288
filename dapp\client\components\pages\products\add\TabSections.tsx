import { useState } from "react";

import { ChevronDown, ChevronUp } from "lucide-react";

import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { FormSection, SectionId } from "@/types/form-section";

type SectionNavigatorProps = {
  sections: FormSection[];
  currentSection: SectionId;
  onSectionChange: (sectionId: SectionId) => void;
};

export const SectionNavigator: React.FC<SectionNavigatorProps> = ({
  sections,
  currentSection,
  onSectionChange,
}) => {
  const [isCollapsed, setIsCollapsed] = useState(false);

  const handleSectionClick = (sectionId: SectionId) => {
    onSectionChange(sectionId);
  };

  return (
    <div className="sticky top-4 h-fit w-full rounded-lg border bg-white p-4 shadow-sm">
      <div className="flex items-center justify-between">
        <h2 className="text-lg font-semibold">Form Sections</h2>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsCollapsed(!isCollapsed)}
          className="p-1"
        >
          {isCollapsed ? <ChevronDown size={20} /> : <ChevronUp size={20} />}
        </Button>
      </div>

      {!isCollapsed && (
        <div className="mt-4 flex flex-col space-y-1">
          {sections.map((section) => (
            <button
              key={section.id}
              onClick={() => handleSectionClick(section.id as SectionId)}
              className={cn(
                "flex items-center rounded-md px-3 py-2 text-left text-sm transition-colors",
                currentSection === section.id
                  ? "bg-blue-100 text-blue-800"
                  : section.isCompleted
                    ? "text-gray-800 hover:bg-gray-100"
                    : "text-gray-500 hover:bg-gray-100",
                section.isOptional && "italic"
              )}
            >
              <span className="mr-2">{section.icon}</span>
              <span>{section.title}</span>
              {section.isCompleted && (
                <span className="ml-auto text-xs font-medium text-green-500">
                  ✓
                </span>
              )}
            </button>
          ))}
        </div>
      )}
    </div>
  );
};
