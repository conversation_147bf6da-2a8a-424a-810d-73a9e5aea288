"use client";

import { MapPin, Truck, User } from "lucide-react";

import { Card } from "@/components/ui/card";

import { InfoSection } from "./InfoSection";

export const CustomerInfoSection = () => {
  return (
    <section className="flex flex-wrap gap-4">
      <Card className="w-[calc(33.333%-1.0rem)] rounded-lg border border-gray-200 bg-white p-5">
        <InfoSection
          icon={<User className="size-5" />}
          title="Customer"
          rows={[
            { label: "Name", value: "John Alexander" },
            { label: "Email", value: "<EMAIL>" },
            { label: "Tel.", value: "+998 99 22123456" },
          ]}
          linkText="View profile"
          linkAction={() => {}}
        />
      </Card>

      <Card className="w-[calc(33.333%-1.0rem)] rounded-lg border border-gray-200 bg-white p-5">
        <InfoSection
          icon={<Truck className="size-5" />}
          title="Payment & shipping"
          rows={[
            { label: "Shipping", value: "Fargo express" },
            { label: "Payment", value: "Card card" },
            { label: "Status", value: "Pending" },
          ]}
          linkText="Download"
          linkAction={() => {}}
        />
      </Card>

      <Card className="w-1/3 rounded-lg border border-gray-200 bg-white p-5">
        <InfoSection
          icon={<MapPin className="size-5" />}
          title="Deliver to"
          rows={[
            { label: "City", value: "Tashkent, Uzbekistan" },
            { label: "Street", value: "Beruniy 369" },
            { label: "Address", value: "Block A, House 123, Floor 2" },
          ]}
          linkText="Open map"
          linkAction={() => {}}
        />
      </Card>
    </section>
  );
};
