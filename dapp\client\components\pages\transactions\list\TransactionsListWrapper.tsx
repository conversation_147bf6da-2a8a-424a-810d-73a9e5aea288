"use client";

import React from "react";

import { Card, CardContent } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";

import { TransactionsFilter } from "./TransactionsFilter";
import { TransactionsTable } from "./TransactionsTable";
import { TransactionsStats } from "./TransactionsStats";

export const TransactionsListWrapper = () => {
  return (
    <div className="space-y-6">
      {/* Statistics Overview */}
      <TransactionsStats />

      {/* Transactions List */}
      <Card>
        <CardContent className="p-6">
          <TransactionsFilter />

          <Separator className="my-6" />

          <TransactionsTable />
        </CardContent>
      </Card>

      {/* Pagination would go here */}
      <div className="flex items-center justify-between">
        <p className="text-sm text-gray-500">
          Showing 1-10 of 10 transactions
        </p>
        <div className="flex items-center gap-2">
          {/* Pagination component would be added here */}
        </div>
      </div>
    </div>
  );
};
