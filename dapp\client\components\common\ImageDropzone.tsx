import { useCallback } from "react";

import { UploadCloud } from "lucide-react";
import { useDropzone } from "react-dropzone";

export function ImageDropzone({
  onFilesAdded,
  disabled,
  maxFiles = 4,
}: {
  onFilesAdded: (files: File[]) => void;
  disabled?: boolean;
  maxFiles?: number;
}) {
  const onDrop = useCallback(
    (acceptedFiles: File[]) => {
      if (acceptedFiles.length > 0) {
        onFilesAdded(acceptedFiles.slice(0, maxFiles));
      }
    },
    [onFilesAdded, maxFiles]
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    multiple: true,
    maxSize: 4 * 1024 * 1024, // 4MB
    accept: { "image/*": [] },
    disabled,
  });

  return (
    <div className="space-y-2">
      {/* <label className="text-sm font-medium">Images</label> */}

      <div
        {...getRootProps()}
        className={`flex cursor-pointer flex-col items-center justify-center rounded-md border-2 border-dashed p-6 transition-all ${
          isDragActive ? "border-blue-400 bg-blue-50" : "border-gray-300"
        }`}
      >
        <input {...getInputProps()} />
        <UploadCloud className="mb-2 size-8 text-gray-400" />
        <p className="text-center text-sm text-gray-700">
          <span className="font-medium">Choose file(s)</span> or drag and drop
        </p>
        <p className="text-xs text-gray-500">
          Images up to 4MB, max {maxFiles}
        </p>

        <button
          type="button"
          className="mt-3 rounded bg-green-500 px-4 py-1 text-sm font-medium text-white hover:bg-green-600"
        >
          Choose File(s)
        </button>
      </div>

      <p className="text-xs text-gray-500">
        First image will be the main product image.
      </p>
    </div>
  );
}
