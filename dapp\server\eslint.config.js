import pluginJs from "@eslint/js";
import prettier from "eslint-config-prettier";
import prettierPlugin from "eslint-plugin-prettier";
import globals from "globals";
import tseslint from "typescript-eslint";

/** @type {import('eslint').Linter.Config[]} */
export default [
  { files: ["**/*.{js,mjs,cjs,ts}"] },
  {
    languageOptions: {
      globals: {
        ...globals.node,
        // Add any project-specific global variables
      },
      parserOptions: {
        ecmaVersion: "latest",
        sourceType: "module",
      },
    },
  },
  pluginJs.configs.recommended,
  ...tseslint.configs.recommended,
  prettier,
  { plugins: { prettier: prettierPlugin } },
  {
    rules: {
      "no-undef": "off",
      "@typescript-eslint/no-unused-vars": ["warn"],
      "import/order": "off",
      "consistent-return": "warn",
      "no-console": ["warn", { allow: ["warn", "error", "info"] }],
      "no-duplicate-imports": ["error", { includeExports: true }],
    },
  },
];
