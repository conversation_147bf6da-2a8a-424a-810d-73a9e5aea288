/**
 * Test script to verify API connection
 * This can be used to test the API endpoints before integrating with the UI
 */
import {
  checkApiHealth,
  createProduct,
  deleteProduct,
  getProducts,
} from "./api/products";

export async function testApiConnection() {
  console.log("🔍 Testing API connection...");

  try {
    // Test API health
    const isHealthy = await checkApiHealth();
    console.log(`API Health: ${isHealthy ? "✅ Healthy" : "❌ Unhealthy"}`);

    if (!isHealthy) {
      console.log(
        "❌ API is not responding. Make sure the backend server is running on the correct port."
      );
      return false;
    }

    // Test products endpoint
    console.log("📦 Testing products endpoint...");
    const response = await getProducts({ limit: 5 });

    console.log(`Products Response:`, {
      success: response.success,
      dataLength: response.data?.length || 0,
      meta: response.meta,
    });

    if (response.data && response.data.length > 0) {
      console.log("Sample product:", response.data[0]);
    }

    console.log("✅ API connection test completed successfully!");
    return true;
  } catch (error) {
    console.error("❌ API connection test failed:", error);
    return false;
  }
}

export async function testDeleteFunctionality() {
  console.log("🗑️ Testing delete functionality...");

  try {
    // First create a test product
    console.log("Creating test product...");
    const testProduct = {
      name: "Test Product for Deletion",
      price: 99.99,
      currency: "USD",
      category: "Test",
      brand: "Test Brand",
      stock: 1,
      status: "draft",
      description: "This is a test product that will be deleted",
    };

    const createResponse = await createProduct(testProduct);
    console.log("✅ Test product created:", createResponse.data);

    const productId = createResponse.data._id || createResponse.data.id;

    if (!productId) {
      console.error("❌ No product ID returned from create");
      return false;
    }

    // Wait a moment
    await new Promise((resolve) => setTimeout(resolve, 1000));

    // Now delete the test product
    console.log(`Deleting test product with ID: ${productId}`);
    const deleteResponse = await deleteProduct(productId);
    console.log("✅ Test product deleted:", deleteResponse);

    console.log("✅ Delete functionality test completed successfully!");
    return true;
  } catch (error) {
    console.error("❌ Delete functionality test failed:", error);
    return false;
  }
}

export async function testAddProductFunctionality() {
  console.log("➕ Testing add product functionality...");

  try {
    // Create a comprehensive test product
    const testProduct = {
      name: "Test Product - " + new Date().toISOString().slice(0, 19),
      description:
        "This is a comprehensive test product created via the frontend API integration.",
      price: 299.99,
      currency: "USD",
      category: "Electronics",
      brand: "Test Brand",
      stock: 50,
      status: "in-stock",
      mainImage:
        "https://via.placeholder.com/400x400/0066cc/ffffff?text=Test+Product",
      images: [
        "https://via.placeholder.com/400x400/0066cc/ffffff?text=Test+Product+1",
        "https://via.placeholder.com/400x400/00cc66/ffffff?text=Test+Product+2",
      ],
      tags: ["test", "electronics", "api-test"],
      weight: 1.5,
      dimensions: {
        length: 10,
        width: 8,
        height: 3,
      },
      sku: "TEST-" + Date.now(),
      condition: "new",
      warranty: "1 year manufacturer warranty",
      features: ["Feature 1", "Feature 2", "Feature 3"],
      specifications: {
        color: "Blue",
        material: "Plastic",
        model: "TEST-2024",
      },
    };

    console.log("Creating test product with data:", testProduct);

    const createResponse = await createProduct(testProduct);
    console.log("✅ Test product created successfully:", createResponse.data);

    const productId = createResponse.data._id || createResponse.data.id;

    if (productId) {
      console.log(`✅ Product created with ID: ${productId}`);
      console.log(`🔗 You can view it at: /admin/products/${productId}`);
    }

    console.log("✅ Add product functionality test completed successfully!");
    return createResponse.data;
  } catch (error) {
    console.error("❌ Add product functionality test failed:", error);
    return false;
  }
}

// Export for use in browser console
if (typeof window !== "undefined") {
  (window as any).testApiConnection = testApiConnection;
  (window as any).testDeleteFunctionality = testDeleteFunctionality;
  (window as any).testAddProductFunctionality = testAddProductFunctionality;
}
