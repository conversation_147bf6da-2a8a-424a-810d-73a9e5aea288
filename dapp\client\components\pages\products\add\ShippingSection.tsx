import React from "react";

import {
  Building,
  Globe,
  Home,
  MapPin,
  Navigation,
  Package,
  Ruler,
  Truck,
  Weight,
} from "lucide-react";
import {
  FieldErrors,
  UseFormRegister,
  UseFormSetValue,
  UseFormWatch,
} from "react-hook-form";

import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ImportantNotice } from "@/components/ui/important-notice";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import {
  ProductFormData,
  dimensionUnitValues,
  shippingClassValues,
  shippingTimeValues,
  weightUnitValues,
} from "@/schemas/productSchema";

import { FormField } from "../../../common/FormField";
import { CollapsibleSection } from "../../../ui/collapsible-section";

// Helper function to format shipping time labels
const formatShippingTimeLabel = (timeValue: string): string => {
  const formatMap: Record<string, string> = {
    "same-day": "Same Day Delivery",
    "1-2-business-days": "1-2 Business Days",
    "2-3-business-days": "2-3 Business Days",
    "3-5-business-days": "3-5 Business Days",
    "5-7-business-days": "5-7 Business Days",
    "7-10-business-days": "7-10 Business Days",
    "10-14-business-days": "10-14 Business Days",
    "2-3-weeks": "2-3 Weeks",
    "3-4-weeks": "3-4 Weeks",
    "4-6-weeks": "4-6 Weeks",
  };
  return formatMap[timeValue] || timeValue;
};

type ShippingSectionProps = {
  register: UseFormRegister<ProductFormData>;
  errors: FieldErrors<ProductFormData>;
  setValue: UseFormSetValue<ProductFormData>;
  watch: UseFormWatch<ProductFormData>;
};

/**
 * Shipping section of the product form
 * Handles shipping location, cost, time, and free shipping option
 */
export const ShippingSection = ({
  register,
  errors,
  setValue,
  watch,
}: ShippingSectionProps) => {
  const freeShipping = watch("freeShipping");
  const requiresShipping = watch("requiresShipping");
  const productType = watch("productType");

  const handleFreeShippingChange = (checked: boolean) => {
    setValue("freeShipping", checked);
    if (checked) {
      setValue("shippingCost", 0);
    }
  };

  const handleRequiresShippingChange = (checked: boolean) => {
    setValue("requiresShipping", checked);
    if (!checked) {
      setValue("freeShipping", false);
      setValue("shippingCost", 0);
    }
  };

  // Auto-set requiresShipping based on product type
  React.useEffect(() => {
    if (productType === "digital" || productType === "service") {
      setValue("requiresShipping", false);
    } else {
      setValue("requiresShipping", true);
    }
  }, [productType, setValue]);

  return (
    <div className="space-y-8">
      {/* Section Header */}
      <div className="flex items-center gap-3 border-b border-gray-200 pb-4">
        <div className="rounded-full bg-blue-100 p-2">
          <Truck className="h-5 w-5 text-blue-600" />
        </div>
        <div>
          <h2 className="text-2xl font-bold text-gray-900">
            Shipping & Delivery
          </h2>
          <p className="text-gray-600">
            Configure shipping options and delivery details
          </p>
        </div>
      </div>

      {/* Important Notice - Only show when shipping is required */}
      {requiresShipping && (
        <ImportantNotice
          description="You must complete all required shipping information before proceeding to the next step."
          requiredFields={
            freeShipping
              ? ["Shipping Class", "Estimated Shipping Time"]
              : ["Shipping Class", "Shipping Cost", "Estimated Shipping Time"]
          }
          tip={
            freeShipping
              ? "Shipping class and estimated delivery time help customers understand when to expect their orders."
              : "Shipping class, cost, and estimated delivery time help customers make informed purchasing decisions."
          }
          variant="blue"
        />
      )}

      {/* Shipping Requirements Card */}
      <Card className="border-l-4 border-l-blue-500">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Package className="h-5 w-5 text-blue-600" />
            Shipping Requirements
            <Badge variant="secondary" className="text-xs">
              Optional
            </Badge>
          </CardTitle>
          <p className="text-sm text-gray-600">
            Configure whether this product requires physical shipping
          </p>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center space-x-3">
              <Switch
                id="requires-shipping-toggle"
                checked={requiresShipping}
                onCheckedChange={handleRequiresShippingChange}
                disabled={
                  productType === "digital" || productType === "service"
                }
              />
              <Label
                htmlFor="requires-shipping-toggle"
                className="text-base font-medium"
              >
                This product requires shipping
              </Label>
            </div>

            {(productType === "digital" || productType === "service") && (
              <div className="rounded-lg border border-green-200 bg-green-50 p-3">
                <div className="text-sm text-green-800">
                  <strong>Note:</strong>{" "}
                  {productType === "digital" ? "Digital products" : "Services"}{" "}
                  typically don&apos;t require physical shipping.
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {requiresShipping && (
        <>
          {/* Shipping Class & Pricing Card */}
          <Card className="border-l-4 border-l-purple-500">
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2 text-lg">
                <Truck className="h-5 w-5 text-purple-600" />
                Shipping Class & Pricing
                <Badge variant="destructive" className="text-xs">
                  Required
                </Badge>
              </CardTitle>
              <p className="text-sm text-gray-600">
                Configure shipping service level and costs
              </p>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* Shipping Class */}
                <FormField
                  id="shippingClass"
                  label="Shipping Class"
                  error={errors.shippingClass?.message}
                  optional={false}
                >
                  <Select
                    onValueChange={(value) =>
                      setValue(
                        "shippingClass",
                        value as (typeof shippingClassValues)[number]
                      )
                    }
                    value={watch("shippingClass")}
                  >
                    <SelectTrigger className="border-2 focus:border-purple-500">
                      <SelectValue placeholder="Select shipping class..." />
                    </SelectTrigger>
                    <SelectContent>
                      {shippingClassValues.map((shippingClass) => (
                        <SelectItem key={shippingClass} value={shippingClass}>
                          <div className="flex items-center gap-2">
                            <Truck className="h-4 w-4" />
                            <span className="font-medium capitalize">
                              {shippingClass.replace("-", " ")}
                            </span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </FormField>

                {/* Free Shipping Toggle */}
                <div className="flex items-center space-x-3">
                  <Switch
                    id="free-shipping-toggle"
                    checked={freeShipping}
                    onCheckedChange={handleFreeShippingChange}
                  />
                  <Label
                    htmlFor="free-shipping-toggle"
                    className="text-base font-medium"
                  >
                    Free Shipping
                  </Label>
                </div>

                {/* Shipping Cost & Time */}
                <div className="grid gap-4 md:grid-cols-2">
                  {!freeShipping && (
                    <FormField
                      id="shippingCost"
                      label="Shipping Cost"
                      error={errors.shippingCost?.message}
                      optional={false}
                    >
                      <Input
                        id="shippingCost"
                        type="number"
                        step="0.01"
                        min="0"
                        {...register("shippingCost", { valueAsNumber: true })}
                        placeholder="0.00"
                        className="border-2 focus:border-purple-500"
                      />
                    </FormField>
                  )}

                  <FormField
                    id="shippingTime"
                    label="Estimated Shipping Time"
                    error={errors.shippingTime?.message}
                    optional={false}
                  >
                    <Select
                      onValueChange={(value) =>
                        setValue(
                          "shippingTime",
                          value as (typeof shippingTimeValues)[number]
                        )
                      }
                      value={watch("shippingTime")}
                    >
                      <SelectTrigger className="border-2 focus:border-purple-500">
                        <SelectValue placeholder="Select delivery timeframe..." />
                      </SelectTrigger>
                      <SelectContent>
                        {shippingTimeValues.map((timeOption) => (
                          <SelectItem key={timeOption} value={timeOption}>
                            <div className="flex items-center gap-2">
                              <Navigation className="h-4 w-4" />
                              <span className="font-medium">
                                {formatShippingTimeLabel(timeOption)}
                              </span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </FormField>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Shipping Address - Collapsible */}
          <CollapsibleSection
            title="Shipping Origin Address"
            description="Where will this product ship from?"
            icon={<MapPin className="h-5 w-5 text-green-600" />}
            borderColor="border-l-green-500"
            defaultOpen={false}
            isOptional={true}
          >
            <div className="space-y-4">
              {/* Street Address */}
              <FormField
                id="address.street"
                label="Street Address"
                optional={true}
              >
                <Input
                  id="address.street"
                  {...register("address.street")}
                  placeholder="123 Main Street"
                  className="border-2 focus:border-green-500"
                />
              </FormField>

              {/* City, State, Postal Code */}
              <div className="grid gap-4 md:grid-cols-3">
                <FormField id="address.city" label="City" optional={true}>
                  <Input
                    id="address.city"
                    {...register("address.city")}
                    placeholder="New York"
                    className="border-2 focus:border-green-500"
                  />
                </FormField>

                <FormField
                  id="address.state"
                  label="State/Province"
                  optional={true}
                >
                  <Input
                    id="address.state"
                    {...register("address.state")}
                    placeholder="NY"
                    className="border-2 focus:border-green-500"
                  />
                </FormField>

                <FormField
                  id="address.postalCode"
                  label="Postal Code"
                  optional={true}
                >
                  <Input
                    id="address.postalCode"
                    {...register("address.postalCode")}
                    placeholder="10001"
                    className="border-2 focus:border-green-500"
                  />
                </FormField>
              </div>

              {/* Country */}
              <FormField id="address.country" label="Country" optional={true}>
                <Input
                  id="address.country"
                  {...register("address.country")}
                  placeholder="United States"
                  className="border-2 focus:border-green-500"
                />
              </FormField>

              {/* Legacy Location Field */}
              <FormField
                id="location"
                label="Quick Location (Legacy)"
                error={errors.location?.message}
                optional={true}
              >
                <Input
                  id="location"
                  {...register("location")}
                  placeholder="e.g., New York, USA"
                  className="border-2 focus:border-green-500"
                />
                <p className="mt-1 text-xs text-gray-500">
                  Simple location description (will be replaced by address
                  above)
                </p>
              </FormField>
            </div>
          </CollapsibleSection>
        </>
      )}
    </div>
  );
};
