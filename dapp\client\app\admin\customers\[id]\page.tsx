import { PageHeaderWrapper } from "@/components/common/PageHeaderWrapper";
import { CustomerDetailsWrapper } from "@/components/pages/customers/details/CustomerDetailsWrapper";

type Props = {
  params: {
    id: string;
  };
};

export default function AdminCustomerDetails({ params }: Props) {
  return (
    <>
      <PageHeaderWrapper
        title={`Customer Details`}
        description="View and manage customer information and order history"
      />

      <div className="container mx-auto mt-6">
        <CustomerDetailsWrapper customerId={params.id} />
      </div>
    </>
  );
}
