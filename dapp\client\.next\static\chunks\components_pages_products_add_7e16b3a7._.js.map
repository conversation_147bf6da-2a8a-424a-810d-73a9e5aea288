{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/pages/products/add/AvailabilitySection.tsx"], "sourcesContent": ["import { useState } from \"react\";\r\n\r\nimport { format } from \"date-fns\";\r\nimport {\r\n  Alert<PERSON>ircle,\r\n  CalendarIcon,\r\n  Calendar as CalendarL<PERSON><PERSON>,\r\n  CheckCircle,\r\n  Clock,\r\n  Eye,\r\n  EyeOff,\r\n  Globe,\r\n  Info,\r\n  Timer,\r\n  Users,\r\n  Zap,\r\n} from \"lucide-react\";\r\nimport {\r\n  FieldErrors,\r\n  UseFormRegister,\r\n  UseFormSetValue,\r\n  UseFormWatch,\r\n} from \"react-hook-form\";\r\n\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Calendar } from \"@/components/ui/calendar\";\r\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\r\nimport { Label } from \"@/components/ui/label\";\r\nimport {\r\n  Popover,\r\n  PopoverContent,\r\n  PopoverTrigger,\r\n} from \"@/components/ui/popover\";\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from \"@/components/ui/select\";\r\nimport { Switch } from \"@/components/ui/switch\";\r\nimport { ProductFormData } from \"@/schemas/productSchema\";\r\n\r\nimport { FormField } from \"../../../common/FormField\";\r\nimport { CollapsibleSection } from \"../../../ui/collapsible-section\";\r\n\r\ntype AvailabilitySectionProps = {\r\n  register: UseFormRegister<ProductFormData>;\r\n  errors: FieldErrors<ProductFormData>;\r\n  setValue: UseFormSetValue<ProductFormData>;\r\n  watch: UseFormWatch<ProductFormData>;\r\n};\r\n\r\n/**\r\n * Availability section of the product form\r\n * Handles product status, age restrictions, and availability dates\r\n */\r\nexport const AvailabilitySection = ({\r\n  register,\r\n  errors,\r\n  setValue,\r\n  watch,\r\n}: AvailabilitySectionProps) => {\r\n  const [availableFromOpen, setAvailableFromOpen] = useState(false);\r\n  const [availableUntilOpen, setAvailableUntilOpen] = useState(false);\r\n\r\n  const availableFrom = watch(\"availableFrom\");\r\n  const availableUntil = watch(\"availableUntil\");\r\n  const isPublished = watch(\"isPublished\");\r\n  const featured = watch(\"featured\");\r\n  const sticky = watch(\"sticky\");\r\n\r\n  // Calculate availability status\r\n  const getAvailabilityStatus = () => {\r\n    const now = new Date();\r\n    const fromDate = availableFrom ? new Date(availableFrom) : null;\r\n    const untilDate = availableUntil ? new Date(availableUntil) : null;\r\n\r\n    if (fromDate && fromDate > now)\r\n      return { status: \"scheduled\", color: \"blue\", icon: Timer };\r\n    if (untilDate && untilDate < now)\r\n      return { status: \"expired\", color: \"red\", icon: AlertCircle };\r\n    if (isPublished)\r\n      return { status: \"live\", color: \"green\", icon: CheckCircle };\r\n    return { status: \"draft\", color: \"gray\", icon: EyeOff };\r\n  };\r\n\r\n  const availabilityStatus = getAvailabilityStatus();\r\n\r\n  return (\r\n    <div className=\"space-y-8\">\r\n      {/* Section Header */}\r\n      <div className=\"flex items-center gap-3 border-b border-gray-200 pb-4\">\r\n        <div className=\"rounded-full bg-green-100 p-2\">\r\n          <Clock className=\"h-5 w-5 text-green-600\" />\r\n        </div>\r\n        <div>\r\n          <h2 className=\"text-2xl font-bold text-gray-900\">\r\n            Availability & Launch\r\n          </h2>\r\n          <p className=\"text-gray-600\">\r\n            Configure when and how your product becomes available\r\n          </p>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Availability Status Card */}\r\n      <Card className=\"border-l-4 border-l-green-500\">\r\n        <CardHeader className=\"pb-3\">\r\n          <CardTitle className=\"flex items-center gap-2 text-lg\">\r\n            <CheckCircle className=\"h-5 w-5 text-green-600\" />\r\n            Publication Status\r\n            <Badge variant=\"secondary\" className=\"text-xs\">\r\n              Optional\r\n            </Badge>\r\n          </CardTitle>\r\n          <p className=\"text-sm text-gray-600\">\r\n            Control whether your product is live and visible to customers\r\n          </p>\r\n        </CardHeader>\r\n        <CardContent>\r\n          <div className=\"space-y-4\">\r\n            {/* Published Toggle */}\r\n            <div className=\"flex items-center space-x-3\">\r\n              <Switch\r\n                id=\"is-published\"\r\n                checked={isPublished}\r\n                onCheckedChange={(checked) => setValue(\"isPublished\", checked)}\r\n              />\r\n              <Label htmlFor=\"is-published\" className=\"text-base font-medium\">\r\n                Publish this product immediately\r\n              </Label>\r\n            </div>\r\n\r\n            {/* Current Status Display */}\r\n            <div\r\n              className={`rounded-lg border p-3 ${\r\n                availabilityStatus.color === \"green\"\r\n                  ? \"border-green-200 bg-green-50\"\r\n                  : availabilityStatus.color === \"blue\"\r\n                    ? \"border-blue-200 bg-blue-50\"\r\n                    : availabilityStatus.color === \"red\"\r\n                      ? \"border-red-200 bg-red-50\"\r\n                      : \"border-gray-200 bg-gray-50\"\r\n              }`}\r\n            >\r\n              <div className=\"flex items-center gap-2\">\r\n                <availabilityStatus.icon\r\n                  className={`h-4 w-4 ${\r\n                    availabilityStatus.color === \"green\"\r\n                      ? \"text-green-600\"\r\n                      : availabilityStatus.color === \"blue\"\r\n                        ? \"text-blue-600\"\r\n                        : availabilityStatus.color === \"red\"\r\n                          ? \"text-red-600\"\r\n                          : \"text-gray-600\"\r\n                  }`}\r\n                />\r\n                <span\r\n                  className={`font-medium ${\r\n                    availabilityStatus.color === \"green\"\r\n                      ? \"text-green-900\"\r\n                      : availabilityStatus.color === \"blue\"\r\n                        ? \"text-blue-900\"\r\n                        : availabilityStatus.color === \"red\"\r\n                          ? \"text-red-900\"\r\n                          : \"text-gray-900\"\r\n                  }`}\r\n                >\r\n                  {availabilityStatus.status === \"live\" && \"Product is Live\"}\r\n                  {availabilityStatus.status === \"draft\" &&\r\n                    \"Product is in Draft\"}\r\n                  {availabilityStatus.status === \"scheduled\" &&\r\n                    \"Product is Scheduled\"}\r\n                  {availabilityStatus.status === \"expired\" &&\r\n                    \"Product has Expired\"}\r\n                </span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n\r\n      {/* Launch Schedule - Collapsible */}\r\n      <CollapsibleSection\r\n        title=\"Launch Schedule\"\r\n        description=\"Schedule when your product becomes available and when to remove it\"\r\n        icon={<CalendarLucide className=\"h-5 w-5 text-blue-600\" />}\r\n        borderColor=\"border-l-blue-500\"\r\n        defaultOpen={false}\r\n        isOptional={true}\r\n      >\r\n        <div className=\"space-y-4\">\r\n          {/* Available From Date */}\r\n          <FormField\r\n            id=\"availableFrom\"\r\n            label=\"Launch Date (Available From)\"\r\n            error={errors.availableFrom?.message}\r\n            optional={true}\r\n          >\r\n            <Popover\r\n              open={availableFromOpen}\r\n              onOpenChange={setAvailableFromOpen}\r\n            >\r\n              <PopoverTrigger asChild>\r\n                <Button\r\n                  variant=\"outline\"\r\n                  className=\"w-full justify-start border-2 text-left font-normal focus:border-blue-500\"\r\n                >\r\n                  <CalendarIcon className=\"mr-2 h-4 w-4\" />\r\n                  {availableFrom\r\n                    ? format(new Date(availableFrom), \"PPP\")\r\n                    : \"Select launch date\"}\r\n                </Button>\r\n              </PopoverTrigger>\r\n              <PopoverContent className=\"w-auto p-0\">\r\n                <Calendar\r\n                  mode=\"single\"\r\n                  selected={availableFrom ? new Date(availableFrom) : undefined}\r\n                  onSelect={(date) => {\r\n                    setValue(\r\n                      \"availableFrom\",\r\n                      date ? date.toISOString() : undefined\r\n                    );\r\n                    setAvailableFromOpen(false);\r\n                  }}\r\n                  initialFocus\r\n                />\r\n              </PopoverContent>\r\n            </Popover>\r\n          </FormField>\r\n\r\n          {/* Available Until Date */}\r\n          <FormField\r\n            id=\"availableUntil\"\r\n            label=\"End Date (Available Until)\"\r\n            error={errors.availableUntil?.message}\r\n            optional={true}\r\n          >\r\n            <Popover\r\n              open={availableUntilOpen}\r\n              onOpenChange={setAvailableUntilOpen}\r\n            >\r\n              <PopoverTrigger asChild>\r\n                <Button\r\n                  variant=\"outline\"\r\n                  className=\"w-full justify-start border-2 text-left font-normal focus:border-blue-500\"\r\n                >\r\n                  <CalendarIcon className=\"mr-2 h-4 w-4\" />\r\n                  {availableUntil\r\n                    ? format(new Date(availableUntil), \"PPP\")\r\n                    : \"Select end date\"}\r\n                </Button>\r\n              </PopoverTrigger>\r\n              <PopoverContent className=\"w-auto p-0\">\r\n                <Calendar\r\n                  mode=\"single\"\r\n                  selected={\r\n                    availableUntil ? new Date(availableUntil) : undefined\r\n                  }\r\n                  onSelect={(date) => {\r\n                    setValue(\r\n                      \"availableUntil\",\r\n                      date ? date.toISOString() : undefined\r\n                    );\r\n                    setAvailableUntilOpen(false);\r\n                  }}\r\n                  initialFocus\r\n                />\r\n              </PopoverContent>\r\n            </Popover>\r\n          </FormField>\r\n\r\n          {/* Schedule Tips */}\r\n          <div className=\"rounded-lg border border-blue-200 bg-blue-50 p-3\">\r\n            <div className=\"flex items-start gap-2\">\r\n              <Timer className=\"mt-0.5 h-4 w-4 text-blue-600\" />\r\n              <div className=\"text-sm text-blue-800\">\r\n                <strong>Scheduling Tip:</strong> Use launch dates for product\r\n                releases, seasonal items, or limited-time offers.\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </CollapsibleSection>\r\n\r\n      {/* Availability Summary Card */}\r\n      <Card className=\"border-green-200 bg-gradient-to-r from-green-50 to-blue-50\">\r\n        <CardHeader>\r\n          <CardTitle className=\"flex items-center gap-2 text-green-800\">\r\n            <Globe className=\"h-5 w-5\" />\r\n            Availability Summary\r\n          </CardTitle>\r\n        </CardHeader>\r\n        <CardContent>\r\n          <div className=\"grid gap-4 md:grid-cols-3\">\r\n            <div className=\"flex items-center gap-3\">\r\n              <div className=\"rounded-full bg-green-100 p-2\">\r\n                <CheckCircle className=\"h-4 w-4 text-green-600\" />\r\n              </div>\r\n              <div>\r\n                <h4 className=\"font-medium text-green-900\">Status</h4>\r\n                <p className=\"text-sm text-green-700\">\r\n                  {isPublished ? \"Published\" : \"Draft\"}\r\n                </p>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"flex items-center gap-3\">\r\n              <div className=\"rounded-full bg-blue-100 p-2\">\r\n                <CalendarLucide className=\"h-4 w-4 text-blue-600\" />\r\n              </div>\r\n              <div>\r\n                <h4 className=\"font-medium text-blue-900\">Launch Date</h4>\r\n                <p className=\"text-sm text-blue-700\">\r\n                  {availableFrom\r\n                    ? format(new Date(availableFrom), \"MMM dd, yyyy\")\r\n                    : \"Immediate\"}\r\n                </p>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"flex items-center gap-3\">\r\n              <div className=\"rounded-full bg-purple-100 p-2\">\r\n                <Zap className=\"h-4 w-4 text-purple-600\" />\r\n              </div>\r\n              <div>\r\n                <h4 className=\"font-medium text-purple-900\">Marketing</h4>\r\n                <p className=\"text-sm text-purple-700\">\r\n                  {featured || sticky\r\n                    ? `${featured ? \"Featured\" : \"\"} ${sticky ? \"Sticky\" : \"\"}`.trim()\r\n                    : \"Standard\"}\r\n                </p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAqBA;AACA;AACA;AACA;AACA;AACA;AAYA;AAGA;AACA;;;;;;;;;;;;;;;AAaO,MAAM,sBAAsB,CAAC,EAClC,QAAQ,EACR,MAAM,EACN,QAAQ,EACR,KAAK,EACoB;;IACzB,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IAE7D,MAAM,gBAAgB,MAAM;IAC5B,MAAM,iBAAiB,MAAM;IAC7B,MAAM,cAAc,MAAM;IAC1B,MAAM,WAAW,MAAM;IACvB,MAAM,SAAS,MAAM;IAErB,gCAAgC;IAChC,MAAM,wBAAwB;QAC5B,MAAM,MAAM,IAAI;QAChB,MAAM,WAAW,gBAAgB,IAAI,KAAK,iBAAiB;QAC3D,MAAM,YAAY,iBAAiB,IAAI,KAAK,kBAAkB;QAE9D,IAAI,YAAY,WAAW,KACzB,OAAO;YAAE,QAAQ;YAAa,OAAO;YAAQ,MAAM,2RAAA,CAAA,QAAK;QAAC;QAC3D,IAAI,aAAa,YAAY,KAC3B,OAAO;YAAE,QAAQ;YAAW,OAAO;YAAO,MAAM,2SAAA,CAAA,cAAW;QAAC;QAC9D,IAAI,aACF,OAAO;YAAE,QAAQ;YAAQ,OAAO;YAAS,MAAM,kTAAA,CAAA,cAAW;QAAC;QAC7D,OAAO;YAAE,QAAQ;YAAS,OAAO;YAAQ,MAAM,iSAAA,CAAA,SAAM;QAAC;IACxD;IAEA,MAAM,qBAAqB;IAE3B,qBACE,4TAAC;QAAI,WAAU;;0BAEb,4TAAC;gBAAI,WAAU;;kCACb,4TAAC;wBAAI,WAAU;kCACb,cAAA,4TAAC,2RAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;;;;;;kCAEnB,4TAAC;;0CACC,4TAAC;gCAAG,WAAU;0CAAmC;;;;;;0CAGjD,4TAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;;;;;;;0BAOjC,4TAAC,4HAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,4TAAC,4HAAA,CAAA,aAAU;wBAAC,WAAU;;0CACpB,4TAAC,4HAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,4TAAC,kTAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;oCAA2B;kDAElD,4TAAC,6HAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAY,WAAU;kDAAU;;;;;;;;;;;;0CAIjD,4TAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAIvC,4TAAC,4HAAA,CAAA,cAAW;kCACV,cAAA,4TAAC;4BAAI,WAAU;;8CAEb,4TAAC;oCAAI,WAAU;;sDACb,4TAAC,8HAAA,CAAA,SAAM;4CACL,IAAG;4CACH,SAAS;4CACT,iBAAiB,CAAC,UAAY,SAAS,eAAe;;;;;;sDAExD,4TAAC,6HAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAe,WAAU;sDAAwB;;;;;;;;;;;;8CAMlE,4TAAC;oCACC,WAAW,CAAC,sBAAsB,EAChC,mBAAmB,KAAK,KAAK,UACzB,iCACA,mBAAmB,KAAK,KAAK,SAC3B,+BACA,mBAAmB,KAAK,KAAK,QAC3B,6BACA,8BACR;8CAEF,cAAA,4TAAC;wCAAI,WAAU;;0DACb,4TAAC,mBAAmB,IAAI;gDACtB,WAAW,CAAC,QAAQ,EAClB,mBAAmB,KAAK,KAAK,UACzB,mBACA,mBAAmB,KAAK,KAAK,SAC3B,kBACA,mBAAmB,KAAK,KAAK,QAC3B,iBACA,iBACR;;;;;;0DAEJ,4TAAC;gDACC,WAAW,CAAC,YAAY,EACtB,mBAAmB,KAAK,KAAK,UACzB,mBACA,mBAAmB,KAAK,KAAK,SAC3B,kBACA,mBAAmB,KAAK,KAAK,QAC3B,iBACA,iBACR;;oDAED,mBAAmB,MAAM,KAAK,UAAU;oDACxC,mBAAmB,MAAM,KAAK,WAC7B;oDACD,mBAAmB,MAAM,KAAK,eAC7B;oDACD,mBAAmB,MAAM,KAAK,aAC7B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASd,4TAAC,8IAAA,CAAA,qBAAkB;gBACjB,OAAM;gBACN,aAAY;gBACZ,oBAAM,4TAAC,iSAAA,CAAA,WAAc;oBAAC,WAAU;;;;;;gBAChC,aAAY;gBACZ,aAAa;gBACb,YAAY;0BAEZ,cAAA,4TAAC;oBAAI,WAAU;;sCAEb,4TAAC,qIAAA,CAAA,YAAS;4BACR,IAAG;4BACH,OAAM;4BACN,OAAO,OAAO,aAAa,EAAE;4BAC7B,UAAU;sCAEV,cAAA,4TAAC,+HAAA,CAAA,UAAO;gCACN,MAAM;gCACN,cAAc;;kDAEd,4TAAC,+HAAA,CAAA,iBAAc;wCAAC,OAAO;kDACrB,cAAA,4TAAC,8HAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,WAAU;;8DAEV,4TAAC,qSAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;gDACvB,gBACG,CAAA,GAAA,gNAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,gBAAgB,SAChC;;;;;;;;;;;;kDAGR,4TAAC,+HAAA,CAAA,iBAAc;wCAAC,WAAU;kDACxB,cAAA,4TAAC,gIAAA,CAAA,WAAQ;4CACP,MAAK;4CACL,UAAU,gBAAgB,IAAI,KAAK,iBAAiB;4CACpD,UAAU,CAAC;gDACT,SACE,iBACA,OAAO,KAAK,WAAW,KAAK;gDAE9B,qBAAqB;4CACvB;4CACA,YAAY;;;;;;;;;;;;;;;;;;;;;;sCAOpB,4TAAC,qIAAA,CAAA,YAAS;4BACR,IAAG;4BACH,OAAM;4BACN,OAAO,OAAO,cAAc,EAAE;4BAC9B,UAAU;sCAEV,cAAA,4TAAC,+HAAA,CAAA,UAAO;gCACN,MAAM;gCACN,cAAc;;kDAEd,4TAAC,+HAAA,CAAA,iBAAc;wCAAC,OAAO;kDACrB,cAAA,4TAAC,8HAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,WAAU;;8DAEV,4TAAC,qSAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;gDACvB,iBACG,CAAA,GAAA,gNAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,iBAAiB,SACjC;;;;;;;;;;;;kDAGR,4TAAC,+HAAA,CAAA,iBAAc;wCAAC,WAAU;kDACxB,cAAA,4TAAC,gIAAA,CAAA,WAAQ;4CACP,MAAK;4CACL,UACE,iBAAiB,IAAI,KAAK,kBAAkB;4CAE9C,UAAU,CAAC;gDACT,SACE,kBACA,OAAO,KAAK,WAAW,KAAK;gDAE9B,sBAAsB;4CACxB;4CACA,YAAY;;;;;;;;;;;;;;;;;;;;;;sCAOpB,4TAAC;4BAAI,WAAU;sCACb,cAAA,4TAAC;gCAAI,WAAU;;kDACb,4TAAC,2RAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,4TAAC;wCAAI,WAAU;;0DACb,4TAAC;0DAAO;;;;;;4CAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS1C,4TAAC,4HAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,4TAAC,4HAAA,CAAA,aAAU;kCACT,cAAA,4TAAC,4HAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,4TAAC,2RAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;;;;;kCAIjC,4TAAC,4HAAA,CAAA,cAAW;kCACV,cAAA,4TAAC;4BAAI,WAAU;;8CACb,4TAAC;oCAAI,WAAU;;sDACb,4TAAC;4CAAI,WAAU;sDACb,cAAA,4TAAC,kTAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;;;;;;sDAEzB,4TAAC;;8DACC,4TAAC;oDAAG,WAAU;8DAA6B;;;;;;8DAC3C,4TAAC;oDAAE,WAAU;8DACV,cAAc,cAAc;;;;;;;;;;;;;;;;;;8CAKnC,4TAAC;oCAAI,WAAU;;sDACb,4TAAC;4CAAI,WAAU;sDACb,cAAA,4TAAC,iSAAA,CAAA,WAAc;gDAAC,WAAU;;;;;;;;;;;sDAE5B,4TAAC;;8DACC,4TAAC;oDAAG,WAAU;8DAA4B;;;;;;8DAC1C,4TAAC;oDAAE,WAAU;8DACV,gBACG,CAAA,GAAA,gNAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,gBAAgB,kBAChC;;;;;;;;;;;;;;;;;;8CAKV,4TAAC;oCAAI,WAAU;;sDACb,4TAAC;4CAAI,WAAU;sDACb,cAAA,4TAAC,uRAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;;;;;;sDAEjB,4TAAC;;8DACC,4TAAC;oDAAG,WAAU;8DAA8B;;;;;;8DAC5C,4TAAC;oDAAE,WAAU;8DACV,YAAY,SACT,GAAG,WAAW,aAAa,GAAG,CAAC,EAAE,SAAS,WAAW,IAAI,CAAC,IAAI,KAC9D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAStB;GA3Ra;KAAA", "debugId": null}}, {"offset": {"line": 666, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/pages/products/add/InventorySection.tsx"], "sourcesContent": ["import {\r\n  Alert<PERSON><PERSON>gle,\r\n  Archive,\r\n  BarChart3,\r\n  CheckCircle,\r\n  Clock,\r\n  Eye,\r\n  Package,\r\n  RefreshCw,\r\n  Shield,\r\n  ShoppingCart,\r\n  Star,\r\n  TrendingUp,\r\n  Truck,\r\n} from \"lucide-react\";\r\nimport {\r\n  FieldErrors,\r\n  UseFormRegister,\r\n  UseFormSetValue,\r\n  UseFormWatch,\r\n} from \"react-hook-form\";\r\n\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\r\nimport { Checkbox } from \"@/components/ui/checkbox\";\r\nimport { CollapsibleSection } from \"@/components/ui/collapsible-section\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Label } from \"@/components/ui/label\";\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from \"@/components/ui/select\";\r\nimport { Switch } from \"@/components/ui/switch\";\r\nimport {\r\n  ProductFormData,\r\n  conditionValues,\r\n  stockManagementValues,\r\n} from \"@/schemas/productSchema\";\r\nimport { Condition } from \"@/types/common\";\r\n\r\nimport { FormField } from \"../../../common/FormField\";\r\nimport { ImportantNotice } from \"../../../ui/important-notice\";\r\n\r\ntype InventorySectionProps = {\r\n  register: UseFormRegister<ProductFormData>;\r\n  errors: FieldErrors<ProductFormData>;\r\n  setValue: UseFormSetValue<ProductFormData>;\r\n  watch: UseFormWatch<ProductFormData>;\r\n};\r\n\r\nexport const InventorySection: React.FC<InventorySectionProps> = ({\r\n  register,\r\n  errors,\r\n  setValue,\r\n  watch,\r\n}) => {\r\n  const stockQuantity = watch(\"stock\");\r\n  const stockManagement = watch(\"stockManagement\");\r\n  const trackQuantity = watch(\"trackQuantity\");\r\n  const lowStockThreshold = watch(\"lowStockThreshold\");\r\n\r\n  // Calculate stock status\r\n  const getStockStatus = () => {\r\n    if (!stockQuantity || stockQuantity === 0)\r\n      return { status: \"out-of-stock\", color: \"red\", icon: AlertTriangle };\r\n    if (lowStockThreshold && stockQuantity <= lowStockThreshold)\r\n      return { status: \"low-stock\", color: \"yellow\", icon: AlertTriangle };\r\n    return { status: \"in-stock\", color: \"green\", icon: CheckCircle };\r\n  };\r\n\r\n  const stockStatus = getStockStatus();\r\n\r\n  return (\r\n    <div className=\"space-y-8\">\r\n      {/* Section Header */}\r\n      <div className=\"flex items-center gap-3 border-b border-gray-200 pb-4\">\r\n        <div className=\"rounded-full bg-purple-100 p-2\">\r\n          <Package className=\"h-5 w-5 text-purple-600\" />\r\n        </div>\r\n        <div>\r\n          <h2 className=\"text-2xl font-bold text-gray-900\">\r\n            Inventory Management\r\n          </h2>\r\n          <p className=\"text-gray-600\">\r\n            Track stock levels and manage inventory settings\r\n          </p>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Important Notice */}\r\n      <ImportantNotice\r\n        description=\"You must complete all required fields in this section before proceeding to the next step.\"\r\n        requiredFields={[\"Product Condition\", \"Stock Quantity\"]}\r\n        tip=\"Proper inventory tracking helps manage stock levels and provides accurate product information to customers.\"\r\n        variant=\"amber\"\r\n      />\r\n\r\n      {/* Product Condition Card */}\r\n      <Card className=\"border-l-4 border-l-orange-500\">\r\n        <CardHeader className=\"pb-3\">\r\n          <CardTitle className=\"flex items-center gap-2 text-lg\">\r\n            <Star className=\"h-5 w-5 text-orange-600\" />\r\n            Product Condition\r\n            <Badge variant=\"destructive\" className=\"text-xs\">\r\n              Required\r\n            </Badge>\r\n          </CardTitle>\r\n          <p className=\"text-sm text-gray-600\">\r\n            Specify the condition of the product being sold\r\n          </p>\r\n        </CardHeader>\r\n        <CardContent>\r\n          <FormField\r\n            id=\"condition\"\r\n            label=\"\"\r\n            error={errors.condition?.message}\r\n            optional={false}\r\n          >\r\n            <Select\r\n              onValueChange={(value) =>\r\n                setValue(\"condition\", value as Condition)\r\n              }\r\n              defaultValue={watch(\"condition\")}\r\n            >\r\n              <SelectTrigger className=\"border-2 p-4 text-lg focus:border-orange-500\">\r\n                <SelectValue placeholder=\"Select product condition...\" />\r\n              </SelectTrigger>\r\n              <SelectContent>\r\n                {conditionValues.map((condition) => (\r\n                  <SelectItem key={condition} value={condition}>\r\n                    <div className=\"flex items-center gap-2\">\r\n                      {condition === \"new\" && (\r\n                        <Star className=\"h-4 w-4 text-green-500\" />\r\n                      )}\r\n                      {condition === \"used\" && (\r\n                        <RefreshCw className=\"h-4 w-4 text-blue-500\" />\r\n                      )}\r\n                      {condition === \"refurbished\" && (\r\n                        <Shield className=\"h-4 w-4 text-purple-500\" />\r\n                      )}\r\n                      {condition === \"damaged\" && (\r\n                        <AlertTriangle className=\"h-4 w-4 text-red-500\" />\r\n                      )}\r\n                      {condition === \"vintage\" && (\r\n                        <Clock className=\"h-4 w-4 text-amber-500\" />\r\n                      )}\r\n                      {condition === \"antique\" && (\r\n                        <Clock className=\"h-4 w-4 text-amber-600\" />\r\n                      )}\r\n                      {condition === \"like-new\" && (\r\n                        <Star className=\"h-4 w-4 text-green-400\" />\r\n                      )}\r\n                      {condition === \"excellent\" && (\r\n                        <Star className=\"h-4 w-4 text-blue-500\" />\r\n                      )}\r\n                      {condition === \"good\" && (\r\n                        <CheckCircle className=\"h-4 w-4 text-green-500\" />\r\n                      )}\r\n                      {condition === \"fair\" && (\r\n                        <AlertTriangle className=\"h-4 w-4 text-yellow-500\" />\r\n                      )}\r\n                      <span className=\"font-medium capitalize\">\r\n                        {condition.replace(\"-\", \" \")}\r\n                      </span>\r\n                    </div>\r\n                  </SelectItem>\r\n                ))}\r\n              </SelectContent>\r\n            </Select>\r\n          </FormField>\r\n\r\n          {/* Condition Description */}\r\n          {watch(\"condition\") && (\r\n            <div className=\"mt-3 rounded-lg border border-orange-200 bg-orange-50 p-3\">\r\n              <div className=\"flex items-start gap-2\">\r\n                {watch(\"condition\") === \"new\" && (\r\n                  <Star className=\"mt-0.5 h-4 w-4 text-green-600\" />\r\n                )}\r\n                {watch(\"condition\") === \"used\" && (\r\n                  <RefreshCw className=\"mt-0.5 h-4 w-4 text-blue-600\" />\r\n                )}\r\n                {watch(\"condition\") === \"refurbished\" && (\r\n                  <Shield className=\"mt-0.5 h-4 w-4 text-purple-600\" />\r\n                )}\r\n                {watch(\"condition\") === \"damaged\" && (\r\n                  <AlertTriangle className=\"mt-0.5 h-4 w-4 text-red-600\" />\r\n                )}\r\n                {watch(\"condition\") === \"vintage\" && (\r\n                  <Clock className=\"mt-0.5 h-4 w-4 text-amber-600\" />\r\n                )}\r\n                {watch(\"condition\") === \"antique\" && (\r\n                  <Clock className=\"mt-0.5 h-4 w-4 text-amber-700\" />\r\n                )}\r\n                {watch(\"condition\") === \"like-new\" && (\r\n                  <Star className=\"mt-0.5 h-4 w-4 text-green-500\" />\r\n                )}\r\n                {watch(\"condition\") === \"excellent\" && (\r\n                  <Star className=\"mt-0.5 h-4 w-4 text-blue-600\" />\r\n                )}\r\n                {watch(\"condition\") === \"good\" && (\r\n                  <CheckCircle className=\"mt-0.5 h-4 w-4 text-green-600\" />\r\n                )}\r\n                {watch(\"condition\") === \"fair\" && (\r\n                  <AlertTriangle className=\"mt-0.5 h-4 w-4 text-yellow-600\" />\r\n                )}\r\n                <div className=\"text-sm text-orange-800\">\r\n                  <strong>\r\n                    {watch(\"condition\") === \"new\" && \"New:\"}\r\n                    {watch(\"condition\") === \"like-new\" && \"Like New:\"}\r\n                    {watch(\"condition\") === \"excellent\" && \"Excellent:\"}\r\n                    {watch(\"condition\") === \"good\" && \"Good:\"}\r\n                    {watch(\"condition\") === \"fair\" && \"Fair:\"}\r\n                    {watch(\"condition\") === \"used\" && \"Used:\"}\r\n                    {watch(\"condition\") === \"refurbished\" && \"Refurbished:\"}\r\n                    {watch(\"condition\") === \"vintage\" && \"Vintage:\"}\r\n                    {watch(\"condition\") === \"antique\" && \"Antique:\"}\r\n                    {watch(\"condition\") === \"damaged\" && \"Damaged:\"}\r\n                  </strong>\r\n                  <span className=\"ml-1\">\r\n                    {watch(\"condition\") === \"new\" &&\r\n                      \"Brand new, unused item in original packaging with all accessories\"}\r\n                    {watch(\"condition\") === \"like-new\" &&\r\n                      \"Barely used item in excellent condition, appears almost new\"}\r\n                    {watch(\"condition\") === \"excellent\" &&\r\n                      \"Very good condition with minimal signs of use, fully functional\"}\r\n                    {watch(\"condition\") === \"good\" &&\r\n                      \"Good condition with some signs of use but works perfectly\"}\r\n                    {watch(\"condition\") === \"fair\" &&\r\n                      \"Shows wear and use but still functional, may have minor issues\"}\r\n                    {watch(\"condition\") === \"used\" &&\r\n                      \"Previously owned item, may show signs of wear but fully functional\"}\r\n                    {watch(\"condition\") === \"refurbished\" &&\r\n                      \"Restored to working condition, tested and certified with warranty\"}\r\n                    {watch(\"condition\") === \"vintage\" &&\r\n                      \"Older item with historical value, condition varies by age\"}\r\n                    {watch(\"condition\") === \"antique\" &&\r\n                      \"Very old item with historical significance, condition varies\"}\r\n                    {watch(\"condition\") === \"damaged\" &&\r\n                      \"Item has visible damage but may still be functional - sold as-is\"}\r\n                  </span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          )}\r\n        </CardContent>\r\n      </Card>\r\n\r\n      {/* Stock Quantity Card */}\r\n      <Card className=\"border-l-4 border-l-blue-500\">\r\n        <CardHeader className=\"pb-3\">\r\n          <CardTitle className=\"flex items-center gap-2 text-lg\">\r\n            <BarChart3 className=\"h-5 w-5 text-blue-600\" />\r\n            Stock Quantity\r\n            <Badge variant=\"destructive\" className=\"text-xs\">\r\n              Required\r\n            </Badge>\r\n          </CardTitle>\r\n          <p className=\"text-sm text-gray-600\">\r\n            Current number of items available for sale\r\n          </p>\r\n        </CardHeader>\r\n        <CardContent>\r\n          <div className=\"space-y-4\">\r\n            <FormField\r\n              id=\"stock\"\r\n              label=\"\"\r\n              error={errors.stock?.message}\r\n              optional={false}\r\n            >\r\n              <Input\r\n                id=\"stock\"\r\n                type=\"number\"\r\n                min=\"0\"\r\n                {...register(\"stock\", {\r\n                  setValueAs: (value) => {\r\n                    if (value === \"\" || value === null || value === undefined) {\r\n                      return undefined;\r\n                    }\r\n                    const numValue = parseInt(value, 10);\r\n                    return isNaN(numValue) ? undefined : numValue;\r\n                  },\r\n                })}\r\n                placeholder=\"Enter stock quantity\"\r\n                className=\"border-2 p-4 text-lg focus:border-blue-500\"\r\n              />\r\n            </FormField>\r\n\r\n            {/* Stock Status Display */}\r\n            {stockQuantity !== undefined && (\r\n              <div\r\n                className={`rounded-lg border p-3 ${\r\n                  stockStatus.color === \"green\"\r\n                    ? \"border-green-200 bg-green-50\"\r\n                    : stockStatus.color === \"yellow\"\r\n                      ? \"border-yellow-200 bg-yellow-50\"\r\n                      : \"border-red-200 bg-red-50\"\r\n                }`}\r\n              >\r\n                <div className=\"flex items-center gap-2\">\r\n                  <stockStatus.icon\r\n                    className={`h-4 w-4 ${\r\n                      stockStatus.color === \"green\"\r\n                        ? \"text-green-600\"\r\n                        : stockStatus.color === \"yellow\"\r\n                          ? \"text-yellow-600\"\r\n                          : \"text-red-600\"\r\n                    }`}\r\n                  />\r\n                  <span\r\n                    className={`font-medium ${\r\n                      stockStatus.color === \"green\"\r\n                        ? \"text-green-900\"\r\n                        : stockStatus.color === \"yellow\"\r\n                          ? \"text-yellow-900\"\r\n                          : \"text-red-900\"\r\n                    }`}\r\n                  >\r\n                    {stockStatus.status === \"in-stock\" &&\r\n                      `In Stock (${stockQuantity} units)`}\r\n                    {stockStatus.status === \"low-stock\" &&\r\n                      `Low Stock Warning (${stockQuantity} units remaining)`}\r\n                    {stockStatus.status === \"out-of-stock\" && \"Out of Stock\"}\r\n                  </span>\r\n                </div>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n\r\n      {/* Stock Management Settings - Collapsible */}\r\n      <CollapsibleSection\r\n        title=\"Stock Management\"\r\n        description=\"Configure how inventory is tracked and managed\"\r\n        icon={<RefreshCw className=\"h-5 w-5 text-green-600\" />}\r\n        borderColor=\"border-l-green-500\"\r\n        defaultOpen={false}\r\n        isOptional={true}\r\n      >\r\n        <div className=\"space-y-4\">\r\n          {/* Stock Management Type */}\r\n          <FormField\r\n            id=\"stockManagement\"\r\n            label=\"Stock Management Type\"\r\n            optional={true}\r\n          >\r\n            <Select\r\n              onValueChange={(value) =>\r\n                setValue(\r\n                  \"stockManagement\",\r\n                  value as \"track\" | \"no-track\" | \"backorder\"\r\n                )\r\n              }\r\n              value={stockManagement}\r\n            >\r\n              <SelectTrigger className=\"border-2 focus:border-green-500\">\r\n                <SelectValue placeholder=\"Select stock management...\" />\r\n              </SelectTrigger>\r\n              <SelectContent>\r\n                {stockManagementValues.map((type) => (\r\n                  <SelectItem key={type} value={type}>\r\n                    <div className=\"flex items-center gap-2\">\r\n                      {type === \"track\" && <BarChart3 className=\"h-4 w-4\" />}\r\n                      {type === \"no-track\" && <Eye className=\"h-4 w-4\" />}\r\n                      {type === \"backorder\" && <Clock className=\"h-4 w-4\" />}\r\n                      <span className=\"font-medium capitalize\">\r\n                        {type === \"no-track\" ? \"Don't Track\" : type}\r\n                      </span>\r\n                    </div>\r\n                  </SelectItem>\r\n                ))}\r\n              </SelectContent>\r\n            </Select>\r\n          </FormField>\r\n\r\n          {/* Track Quantity Toggle */}\r\n          <div className=\"flex items-center space-x-3\">\r\n            <Switch\r\n              id=\"track-quantity\"\r\n              checked={trackQuantity}\r\n              onCheckedChange={(checked) => setValue(\"trackQuantity\", checked)}\r\n            />\r\n            <Label htmlFor=\"track-quantity\" className=\"text-base font-medium\">\r\n              Track quantity changes\r\n            </Label>\r\n          </div>\r\n\r\n          {/* Low Stock Threshold */}\r\n          {trackQuantity && (\r\n            <FormField\r\n              id=\"lowStockThreshold\"\r\n              label=\"Low Stock Alert Threshold\"\r\n              optional={true}\r\n            >\r\n              <Input\r\n                id=\"lowStockThreshold\"\r\n                type=\"number\"\r\n                min=\"0\"\r\n                {...register(\"lowStockThreshold\", {\r\n                  setValueAs: (value) => {\r\n                    if (value === \"\" || value === null || value === undefined) {\r\n                      return undefined;\r\n                    }\r\n                    const numValue = parseInt(value, 10);\r\n                    return isNaN(numValue) ? undefined : numValue;\r\n                  },\r\n                })}\r\n                placeholder=\"Alert when stock reaches this level\"\r\n                className=\"border-2 focus:border-green-500\"\r\n              />\r\n            </FormField>\r\n          )}\r\n        </div>\r\n      </CollapsibleSection>\r\n\r\n      {/* Advanced Inventory Options - Collapsible */}\r\n      <CollapsibleSection\r\n        title=\"Advanced Options\"\r\n        description=\"Additional inventory and sales settings\"\r\n        icon={<Archive className=\"h-5 w-5 text-purple-600\" />}\r\n        borderColor=\"border-l-purple-500\"\r\n        defaultOpen={false}\r\n        isOptional={true}\r\n      >\r\n        <div className=\"space-y-4\">\r\n          {/* Backorder Allowed */}\r\n          <div className=\"flex items-center space-x-3\">\r\n            <Switch\r\n              id=\"backorder-allowed\"\r\n              checked={watch(\"backorderAllowed\")}\r\n              onCheckedChange={(checked) =>\r\n                setValue(\"backorderAllowed\", checked)\r\n              }\r\n            />\r\n            <Label\r\n              htmlFor=\"backorder-allowed\"\r\n              className=\"text-base font-medium\"\r\n            >\r\n              Allow backorders when out of stock\r\n            </Label>\r\n          </div>\r\n\r\n          {/* Sold Individually */}\r\n          <div className=\"flex items-center space-x-3\">\r\n            <Switch\r\n              id=\"sold-individually\"\r\n              checked={watch(\"soldIndividually\")}\r\n              onCheckedChange={(checked) =>\r\n                setValue(\"soldIndividually\", checked)\r\n              }\r\n            />\r\n            <Label\r\n              htmlFor=\"sold-individually\"\r\n              className=\"text-base font-medium\"\r\n            >\r\n              Sold individually (limit 1 per order)\r\n            </Label>\r\n          </div>\r\n\r\n          {/* Published Status */}\r\n          <div className=\"flex items-center space-x-3\">\r\n            <Switch\r\n              id=\"is-published\"\r\n              checked={watch(\"isPublished\")}\r\n              onCheckedChange={(checked) => setValue(\"isPublished\", checked)}\r\n            />\r\n            <Label htmlFor=\"is-published\" className=\"text-base font-medium\">\r\n              Publish this product immediately\r\n            </Label>\r\n          </div>\r\n        </div>\r\n      </CollapsibleSection>\r\n\r\n      {/* Inventory Summary Card */}\r\n      <Card className=\"border-blue-200 bg-gradient-to-r from-blue-50 to-purple-50\">\r\n        <CardHeader>\r\n          <CardTitle className=\"flex items-center gap-2 text-blue-800\">\r\n            <TrendingUp className=\"h-5 w-5\" />\r\n            Inventory Summary\r\n          </CardTitle>\r\n        </CardHeader>\r\n        <CardContent>\r\n          <div className=\"grid gap-4 md:grid-cols-3\">\r\n            <div className=\"flex items-center gap-3\">\r\n              <div className=\"rounded-full bg-blue-100 p-2\">\r\n                <Package className=\"h-4 w-4 text-blue-600\" />\r\n              </div>\r\n              <div>\r\n                <h4 className=\"font-medium text-blue-900\">Current Stock</h4>\r\n                <p className=\"text-sm text-blue-700\">\r\n                  {stockQuantity || 0} units available\r\n                </p>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"flex items-center gap-3\">\r\n              <div className=\"rounded-full bg-purple-100 p-2\">\r\n                <BarChart3 className=\"h-4 w-4 text-purple-600\" />\r\n              </div>\r\n              <div>\r\n                <h4 className=\"font-medium text-purple-900\">Management</h4>\r\n                <p className=\"text-sm text-purple-700\">\r\n                  {stockManagement || \"Not set\"}\r\n                </p>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"flex items-center gap-3\">\r\n              <div className=\"rounded-full bg-green-100 p-2\">\r\n                <CheckCircle className=\"h-4 w-4 text-green-600\" />\r\n              </div>\r\n              <div>\r\n                <h4 className=\"font-medium text-green-900\">Status</h4>\r\n                <p className=\"text-sm text-green-700\">\r\n                  {watch(\"isPublished\") ? \"Published\" : \"Draft\"}\r\n                </p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAsBA;AACA;AAEA;AACA;AACA;AACA;AAOA;AACA;AAOA;AACA;;;;;;;;;;;;;AASO,MAAM,mBAAoD,CAAC,EAChE,QAAQ,EACR,MAAM,EACN,QAAQ,EACR,KAAK,EACN;IACC,MAAM,gBAAgB,MAAM;IAC5B,MAAM,kBAAkB,MAAM;IAC9B,MAAM,gBAAgB,MAAM;IAC5B,MAAM,oBAAoB,MAAM;IAEhC,yBAAyB;IACzB,MAAM,iBAAiB;QACrB,IAAI,CAAC,iBAAiB,kBAAkB,GACtC,OAAO;YAAE,QAAQ;YAAgB,OAAO;YAAO,MAAM,+SAAA,CAAA,gBAAa;QAAC;QACrE,IAAI,qBAAqB,iBAAiB,mBACxC,OAAO;YAAE,QAAQ;YAAa,OAAO;YAAU,MAAM,+SAAA,CAAA,gBAAa;QAAC;QACrE,OAAO;YAAE,QAAQ;YAAY,OAAO;YAAS,MAAM,kTAAA,CAAA,cAAW;QAAC;IACjE;IAEA,MAAM,cAAc;IAEpB,qBACE,4TAAC;QAAI,WAAU;;0BAEb,4TAAC;gBAAI,WAAU;;kCACb,4TAAC;wBAAI,WAAU;kCACb,cAAA,4TAAC,+RAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;;;;;;kCAErB,4TAAC;;0CACC,4TAAC;gCAAG,WAAU;0CAAmC;;;;;;0CAGjD,4TAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;;;;;;;0BAOjC,4TAAC,2IAAA,CAAA,kBAAe;gBACd,aAAY;gBACZ,gBAAgB;oBAAC;oBAAqB;iBAAiB;gBACvD,KAAI;gBACJ,SAAQ;;;;;;0BAIV,4TAAC,4HAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,4TAAC,4HAAA,CAAA,aAAU;wBAAC,WAAU;;0CACpB,4TAAC,4HAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,4TAAC,yRAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAA4B;kDAE5C,4TAAC,6HAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAc,WAAU;kDAAU;;;;;;;;;;;;0CAInD,4TAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAIvC,4TAAC,4HAAA,CAAA,cAAW;;0CACV,4TAAC,qIAAA,CAAA,YAAS;gCACR,IAAG;gCACH,OAAM;gCACN,OAAO,OAAO,SAAS,EAAE;gCACzB,UAAU;0CAEV,cAAA,4TAAC,8HAAA,CAAA,SAAM;oCACL,eAAe,CAAC,QACd,SAAS,aAAa;oCAExB,cAAc,MAAM;;sDAEpB,4TAAC,8HAAA,CAAA,gBAAa;4CAAC,WAAU;sDACvB,cAAA,4TAAC,8HAAA,CAAA,cAAW;gDAAC,aAAY;;;;;;;;;;;sDAE3B,4TAAC,8HAAA,CAAA,gBAAa;sDACX,2HAAA,CAAA,kBAAe,CAAC,GAAG,CAAC,CAAC,0BACpB,4TAAC,8HAAA,CAAA,aAAU;oDAAiB,OAAO;8DACjC,cAAA,4TAAC;wDAAI,WAAU;;4DACZ,cAAc,uBACb,4TAAC,yRAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DAEjB,cAAc,wBACb,4TAAC,uSAAA,CAAA,YAAS;gEAAC,WAAU;;;;;;4DAEtB,cAAc,+BACb,4TAAC,6RAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;4DAEnB,cAAc,2BACb,4TAAC,+SAAA,CAAA,gBAAa;gEAAC,WAAU;;;;;;4DAE1B,cAAc,2BACb,4TAAC,2RAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;4DAElB,cAAc,2BACb,4TAAC,2RAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;4DAElB,cAAc,4BACb,4TAAC,yRAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DAEjB,cAAc,6BACb,4TAAC,yRAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DAEjB,cAAc,wBACb,4TAAC,kTAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;4DAExB,cAAc,wBACb,4TAAC,+SAAA,CAAA,gBAAa;gEAAC,WAAU;;;;;;0EAE3B,4TAAC;gEAAK,WAAU;0EACb,UAAU,OAAO,CAAC,KAAK;;;;;;;;;;;;mDAjCb;;;;;;;;;;;;;;;;;;;;;4BA2CxB,MAAM,8BACL,4TAAC;gCAAI,WAAU;0CACb,cAAA,4TAAC;oCAAI,WAAU;;wCACZ,MAAM,iBAAiB,uBACtB,4TAAC,yRAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAEjB,MAAM,iBAAiB,wBACtB,4TAAC,uSAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAEtB,MAAM,iBAAiB,+BACtB,4TAAC,6RAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAEnB,MAAM,iBAAiB,2BACtB,4TAAC,+SAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;wCAE1B,MAAM,iBAAiB,2BACtB,4TAAC,2RAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAElB,MAAM,iBAAiB,2BACtB,4TAAC,2RAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAElB,MAAM,iBAAiB,4BACtB,4TAAC,yRAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAEjB,MAAM,iBAAiB,6BACtB,4TAAC,yRAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAEjB,MAAM,iBAAiB,wBACtB,4TAAC,kTAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;wCAExB,MAAM,iBAAiB,wBACtB,4TAAC,+SAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;sDAE3B,4TAAC;4CAAI,WAAU;;8DACb,4TAAC;;wDACE,MAAM,iBAAiB,SAAS;wDAChC,MAAM,iBAAiB,cAAc;wDACrC,MAAM,iBAAiB,eAAe;wDACtC,MAAM,iBAAiB,UAAU;wDACjC,MAAM,iBAAiB,UAAU;wDACjC,MAAM,iBAAiB,UAAU;wDACjC,MAAM,iBAAiB,iBAAiB;wDACxC,MAAM,iBAAiB,aAAa;wDACpC,MAAM,iBAAiB,aAAa;wDACpC,MAAM,iBAAiB,aAAa;;;;;;;8DAEvC,4TAAC;oDAAK,WAAU;;wDACb,MAAM,iBAAiB,SACtB;wDACD,MAAM,iBAAiB,cACtB;wDACD,MAAM,iBAAiB,eACtB;wDACD,MAAM,iBAAiB,UACtB;wDACD,MAAM,iBAAiB,UACtB;wDACD,MAAM,iBAAiB,UACtB;wDACD,MAAM,iBAAiB,iBACtB;wDACD,MAAM,iBAAiB,aACtB;wDACD,MAAM,iBAAiB,aACtB;wDACD,MAAM,iBAAiB,aACtB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUhB,4TAAC,4HAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,4TAAC,4HAAA,CAAA,aAAU;wBAAC,WAAU;;0CACpB,4TAAC,4HAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,4TAAC,ySAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAA0B;kDAE/C,4TAAC,6HAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAc,WAAU;kDAAU;;;;;;;;;;;;0CAInD,4TAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAIvC,4TAAC,4HAAA,CAAA,cAAW;kCACV,cAAA,4TAAC;4BAAI,WAAU;;8CACb,4TAAC,qIAAA,CAAA,YAAS;oCACR,IAAG;oCACH,OAAM;oCACN,OAAO,OAAO,KAAK,EAAE;oCACrB,UAAU;8CAEV,cAAA,4TAAC,6HAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,MAAK;wCACL,KAAI;wCACH,GAAG,SAAS,SAAS;4CACpB,YAAY,CAAC;gDACX,IAAI,UAAU,MAAM,UAAU,QAAQ,UAAU,WAAW;oDACzD,OAAO;gDACT;gDACA,MAAM,WAAW,SAAS,OAAO;gDACjC,OAAO,MAAM,YAAY,YAAY;4CACvC;wCACF,EAAE;wCACF,aAAY;wCACZ,WAAU;;;;;;;;;;;gCAKb,kBAAkB,2BACjB,4TAAC;oCACC,WAAW,CAAC,sBAAsB,EAChC,YAAY,KAAK,KAAK,UAClB,iCACA,YAAY,KAAK,KAAK,WACpB,mCACA,4BACN;8CAEF,cAAA,4TAAC;wCAAI,WAAU;;0DACb,4TAAC,YAAY,IAAI;gDACf,WAAW,CAAC,QAAQ,EAClB,YAAY,KAAK,KAAK,UAClB,mBACA,YAAY,KAAK,KAAK,WACpB,oBACA,gBACN;;;;;;0DAEJ,4TAAC;gDACC,WAAW,CAAC,YAAY,EACtB,YAAY,KAAK,KAAK,UAClB,mBACA,YAAY,KAAK,KAAK,WACpB,oBACA,gBACN;;oDAED,YAAY,MAAM,KAAK,cACtB,CAAC,UAAU,EAAE,cAAc,OAAO,CAAC;oDACpC,YAAY,MAAM,KAAK,eACtB,CAAC,mBAAmB,EAAE,cAAc,iBAAiB,CAAC;oDACvD,YAAY,MAAM,KAAK,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUxD,4TAAC,8IAAA,CAAA,qBAAkB;gBACjB,OAAM;gBACN,aAAY;gBACZ,oBAAM,4TAAC,uSAAA,CAAA,YAAS;oBAAC,WAAU;;;;;;gBAC3B,aAAY;gBACZ,aAAa;gBACb,YAAY;0BAEZ,cAAA,4TAAC;oBAAI,WAAU;;sCAEb,4TAAC,qIAAA,CAAA,YAAS;4BACR,IAAG;4BACH,OAAM;4BACN,UAAU;sCAEV,cAAA,4TAAC,8HAAA,CAAA,SAAM;gCACL,eAAe,CAAC,QACd,SACE,mBACA;gCAGJ,OAAO;;kDAEP,4TAAC,8HAAA,CAAA,gBAAa;wCAAC,WAAU;kDACvB,cAAA,4TAAC,8HAAA,CAAA,cAAW;4CAAC,aAAY;;;;;;;;;;;kDAE3B,4TAAC,8HAAA,CAAA,gBAAa;kDACX,2HAAA,CAAA,wBAAqB,CAAC,GAAG,CAAC,CAAC,qBAC1B,4TAAC,8HAAA,CAAA,aAAU;gDAAY,OAAO;0DAC5B,cAAA,4TAAC;oDAAI,WAAU;;wDACZ,SAAS,yBAAW,4TAAC,ySAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;wDACzC,SAAS,4BAAc,4TAAC,uRAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;wDACtC,SAAS,6BAAe,4TAAC,2RAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;sEAC1C,4TAAC;4DAAK,WAAU;sEACb,SAAS,aAAa,gBAAgB;;;;;;;;;;;;+CAN5B;;;;;;;;;;;;;;;;;;;;;sCAgBzB,4TAAC;4BAAI,WAAU;;8CACb,4TAAC,8HAAA,CAAA,SAAM;oCACL,IAAG;oCACH,SAAS;oCACT,iBAAiB,CAAC,UAAY,SAAS,iBAAiB;;;;;;8CAE1D,4TAAC,6HAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAiB,WAAU;8CAAwB;;;;;;;;;;;;wBAMnE,+BACC,4TAAC,qIAAA,CAAA,YAAS;4BACR,IAAG;4BACH,OAAM;4BACN,UAAU;sCAEV,cAAA,4TAAC,6HAAA,CAAA,QAAK;gCACJ,IAAG;gCACH,MAAK;gCACL,KAAI;gCACH,GAAG,SAAS,qBAAqB;oCAChC,YAAY,CAAC;wCACX,IAAI,UAAU,MAAM,UAAU,QAAQ,UAAU,WAAW;4CACzD,OAAO;wCACT;wCACA,MAAM,WAAW,SAAS,OAAO;wCACjC,OAAO,MAAM,YAAY,YAAY;oCACvC;gCACF,EAAE;gCACF,aAAY;gCACZ,WAAU;;;;;;;;;;;;;;;;;;;;;;0BAQpB,4TAAC,8IAAA,CAAA,qBAAkB;gBACjB,OAAM;gBACN,aAAY;gBACZ,oBAAM,4TAAC,+RAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;gBACzB,aAAY;gBACZ,aAAa;gBACb,YAAY;0BAEZ,cAAA,4TAAC;oBAAI,WAAU;;sCAEb,4TAAC;4BAAI,WAAU;;8CACb,4TAAC,8HAAA,CAAA,SAAM;oCACL,IAAG;oCACH,SAAS,MAAM;oCACf,iBAAiB,CAAC,UAChB,SAAS,oBAAoB;;;;;;8CAGjC,4TAAC,6HAAA,CAAA,QAAK;oCACJ,SAAQ;oCACR,WAAU;8CACX;;;;;;;;;;;;sCAMH,4TAAC;4BAAI,WAAU;;8CACb,4TAAC,8HAAA,CAAA,SAAM;oCACL,IAAG;oCACH,SAAS,MAAM;oCACf,iBAAiB,CAAC,UAChB,SAAS,oBAAoB;;;;;;8CAGjC,4TAAC,6HAAA,CAAA,QAAK;oCACJ,SAAQ;oCACR,WAAU;8CACX;;;;;;;;;;;;sCAMH,4TAAC;4BAAI,WAAU;;8CACb,4TAAC,8HAAA,CAAA,SAAM;oCACL,IAAG;oCACH,SAAS,MAAM;oCACf,iBAAiB,CAAC,UAAY,SAAS,eAAe;;;;;;8CAExD,4TAAC,6HAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAe,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;;;;;;0BAQtE,4TAAC,4HAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,4TAAC,4HAAA,CAAA,aAAU;kCACT,cAAA,4TAAC,4HAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,4TAAC,ySAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;;;;;kCAItC,4TAAC,4HAAA,CAAA,cAAW;kCACV,cAAA,4TAAC;4BAAI,WAAU;;8CACb,4TAAC;oCAAI,WAAU;;sDACb,4TAAC;4CAAI,WAAU;sDACb,cAAA,4TAAC,+RAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;;;;;;sDAErB,4TAAC;;8DACC,4TAAC;oDAAG,WAAU;8DAA4B;;;;;;8DAC1C,4TAAC;oDAAE,WAAU;;wDACV,iBAAiB;wDAAE;;;;;;;;;;;;;;;;;;;8CAK1B,4TAAC;oCAAI,WAAU;;sDACb,4TAAC;4CAAI,WAAU;sDACb,cAAA,4TAAC,ySAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;;;;;;sDAEvB,4TAAC;;8DACC,4TAAC;oDAAG,WAAU;8DAA8B;;;;;;8DAC5C,4TAAC;oDAAE,WAAU;8DACV,mBAAmB;;;;;;;;;;;;;;;;;;8CAK1B,4TAAC;oCAAI,WAAU;;sDACb,4TAAC;4CAAI,WAAU;sDACb,cAAA,4TAAC,kTAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;;;;;;sDAEzB,4TAAC;;8DACC,4TAAC;oDAAG,WAAU;8DAA6B;;;;;;8DAC3C,4TAAC;oDAAE,WAAU;8DACV,MAAM,iBAAiB,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxD;KAzda", "debugId": null}}, {"offset": {"line": 1733, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/pages/products/add/ImageUploadSection.tsx"], "sourcesContent": ["import { UploadDropzone, generateReactHel<PERSON> } from \"@uploadthing/react\";\r\n\r\nimport { useState } from \"react\";\r\n\r\nimport { X } from \"lucide-react\";\r\nimport Image from \"next/image\";\r\nimport { toast } from \"sonner\";\r\n\r\nimport type { OurFileRouter } from \"@/app/api/uploadthing/core\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Skeleton } from \"@/components/ui/skeleton\";\r\nimport { deleteImageFromServer } from \"@/lib/uploadthing/utils\";\r\n\r\nconst { useUploadThing } = generateReactHelpers<OurFileRouter>();\r\n\r\ntype ImageUploadSectionProps = {\r\n  images: { key: string; url: string }[];\r\n  setImages: React.Dispatch<\r\n    React.SetStateAction<{ key: string; url: string }[]>\r\n  >;\r\n  imageAltTexts: string[];\r\n  setImageAltTexts: React.Dispatch<React.SetStateAction<string[]>>;\r\n  maxImages?: number;\r\n};\r\n\r\nexport const ImageUploadSection: React.FC<ImageUploadSectionProps> = ({\r\n  images,\r\n  setImages,\r\n  imageAltTexts,\r\n  setImageAltTexts,\r\n  maxImages = 4,\r\n}) => {\r\n  const [dropzoneKey, setDropzoneKey] = useState(0);\r\n  const [uploadingCount, setUploadingCount] = useState(0);\r\n\r\n  const [loadedImages, setLoadedImages] = useState<Record<string, boolean>>({});\r\n  const [deletingImages, setDeletingImages] = useState<string[]>([]);\r\n\r\n  // Handle alt text changes\r\n  const handleAltTextChange = (index: number, value: string) => {\r\n    const newAltTexts = [...imageAltTexts];\r\n    newAltTexts[index] = value;\r\n    setImageAltTexts(newAltTexts);\r\n  };\r\n\r\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n  const { startUpload } = useUploadThing(\"imageUploader\");\r\n  type UploadResponse = Awaited<ReturnType<typeof startUpload>>;\r\n\r\n  const handleBeforeUploadBegin = (files: File[]) => {\r\n    const total = images.length + files.length;\r\n\r\n    if (total > maxImages) {\r\n      toast.error(\r\n        `You can only upload up to ${maxImages} images. You already have ${images.length}.`\r\n      );\r\n\r\n      setDropzoneKey((prev) => prev + 1);\r\n\r\n      throw new Error(\"Too many images\");\r\n    }\r\n\r\n    setUploadingCount(files.length);\r\n\r\n    return files;\r\n  };\r\n\r\n  const handleClientUploadComplete = (res: UploadResponse) => {\r\n    setUploadingCount(0);\r\n\r\n    if (!res || res.length === 0) {\r\n      toast.error(\"Image upload failed. Received no file data.\");\r\n\r\n      return;\r\n    }\r\n\r\n    const newImages = res.map((file) => ({\r\n      key: file.key,\r\n      url: file.ufsUrl ?? \"\",\r\n    }));\r\n\r\n    const updatedImages = [...images, ...newImages];\r\n\r\n    if (updatedImages.length > maxImages) {\r\n      toast.error(`You can only upload up to ${maxImages} images.`);\r\n\r\n      setDropzoneKey((prev) => prev + 1);\r\n\r\n      return;\r\n    }\r\n\r\n    setImages(updatedImages);\r\n\r\n    // Add empty alt texts for new images\r\n    const newAltTexts = [...imageAltTexts];\r\n    for (let i = 0; i < newImages.length; i++) {\r\n      newAltTexts.push(\"\");\r\n    }\r\n    setImageAltTexts(newAltTexts);\r\n\r\n    toast.success(\"Image uploaded successfully!\");\r\n  };\r\n\r\n  const handleUploadError = (error: Error) => {\r\n    console.error(\"Upload failed:\", error);\r\n\r\n    setDropzoneKey((prev) => prev + 1);\r\n\r\n    setUploadingCount(0);\r\n\r\n    if (error.message.includes(\"FileSizeMismatch\")) {\r\n      toast.error(\"File is too large. Maximum allowed size exceeded.\");\r\n    } else if (error.message === \"Too many images\") {\r\n      toast.error(`You can only upload up to ${maxImages} images.`);\r\n    } else {\r\n      toast.error(\"Image upload failed. Please try a different file.\");\r\n    }\r\n  };\r\n\r\n  const removeImage = async (index: number) => {\r\n    const imageToRemove = images[index];\r\n\r\n    setDeletingImages((prev) => [...prev, imageToRemove.key]);\r\n\r\n    setTimeout(async () => {\r\n      setLoadedImages((prev) => {\r\n        const updated = { ...prev };\r\n        delete updated[imageToRemove.key];\r\n\r\n        return updated;\r\n      });\r\n\r\n      setImages((prev) => prev.filter((_, i) => i !== index));\r\n\r\n      // Also remove the corresponding alt text\r\n      setImageAltTexts((prev) => prev.filter((_, i) => i !== index));\r\n\r\n      try {\r\n        const deleted = await deleteImageFromServer(imageToRemove.key);\r\n        if (!deleted) {\r\n          toast.error(\"Image removed from form but server deletion failed.\");\r\n        } else {\r\n          toast.success(\"Image removed.\");\r\n        }\r\n      } catch (e) {\r\n        console.error(\"Failed to delete image from server:\", e);\r\n        toast.error(\"An error occurred while deleting the image.\");\r\n      }\r\n\r\n      setDeletingImages((prev) =>\r\n        prev.filter((key) => key !== imageToRemove.key)\r\n      );\r\n    }, 300);\r\n  };\r\n\r\n  return (\r\n    <div className=\"w-full space-y-4\">\r\n      {/* Images with Alt Text Inputs */}\r\n      {images.length > 0 && (\r\n        <div className=\"space-y-4\">\r\n          <h4 className=\"text-sm font-medium text-gray-700\">\r\n            Uploaded Images & Alt Text\r\n          </h4>\r\n          <div className=\"grid gap-4 md:grid-cols-2\">\r\n            {images.map((image, i) => (\r\n              <div\r\n                key={image.key}\r\n                className={`rounded-lg border p-4 transition-all duration-300 ${\r\n                  deletingImages.includes(image.key)\r\n                    ? \"scale-95 opacity-50\"\r\n                    : \"scale-100 opacity-100\"\r\n                }`}\r\n              >\r\n                {/* Image Preview */}\r\n                <div className=\"group relative mb-3 aspect-square overflow-hidden rounded border\">\r\n                  {!loadedImages[image.key] && (\r\n                    <Skeleton className=\"absolute inset-0 size-full\" />\r\n                  )}\r\n\r\n                  <Image\r\n                    src={image.url}\r\n                    alt={imageAltTexts[i] || `Product image ${i + 1}`}\r\n                    fill\r\n                    sizes=\"(max-width: 768px) 100vw, 200px\"\r\n                    className=\"object-cover transition-opacity duration-500\"\r\n                    style={{\r\n                      opacity: loadedImages[image.key] ? 1 : 0,\r\n                    }}\r\n                    onLoad={() =>\r\n                      setLoadedImages((prev) => ({\r\n                        ...prev,\r\n                        [image.key]: true,\r\n                      }))\r\n                    }\r\n                    quality={85}\r\n                  />\r\n\r\n                  {/* Image Badge */}\r\n                  <div className=\"absolute left-2 top-2 rounded bg-black/70 px-2 py-1 text-xs text-white\">\r\n                    {i === 0 ? \"Main\" : `#${i + 1}`}\r\n                  </div>\r\n\r\n                  {/* Remove Button */}\r\n                  <button\r\n                    type=\"button\"\r\n                    onClick={() => removeImage(i)}\r\n                    className=\"absolute right-2 top-2 rounded-full bg-red-500 p-1 text-white opacity-0 shadow-sm transition-opacity hover:bg-red-600 group-hover:opacity-100\"\r\n                  >\r\n                    <X size={14} />\r\n                  </button>\r\n                </div>\r\n\r\n                {/* Alt Text Input */}\r\n                <div className=\"space-y-2\">\r\n                  <label className=\"text-xs font-medium text-gray-600\">\r\n                    Alt Text {i === 0 && \"(Main Image)\"}\r\n                    <span className=\"text-red-500\">*</span>\r\n                  </label>\r\n                  <Input\r\n                    value={imageAltTexts[i] || \"\"}\r\n                    onChange={(e) => handleAltTextChange(i, e.target.value)}\r\n                    placeholder={`Describe image ${i + 1}... (e.g., \"Red cotton t-shirt front view\")`}\r\n                    className={`text-sm ${\r\n                      !imageAltTexts[i] || imageAltTexts[i].trim() === \"\"\r\n                        ? \"border-red-300 focus:border-red-500\"\r\n                        : \"border-green-300 focus:border-green-500\"\r\n                    }`}\r\n                    required\r\n                  />\r\n                  {!imageAltTexts[i] || imageAltTexts[i].trim() === \"\" ? (\r\n                    <p className=\"text-xs text-red-600\">\r\n                      ⚠️ Alt text is required for accessibility and SEO\r\n                    </p>\r\n                  ) : (\r\n                    <p className=\"text-xs text-green-600\">\r\n                      ✅ Good for SEO and accessibility\r\n                    </p>\r\n                  )}\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Uploading Skeletons */}\r\n      {uploadingCount > 0 && (\r\n        <div className=\"space-y-2\">\r\n          <h4 className=\"text-sm font-medium text-gray-700\">Uploading...</h4>\r\n          <div className=\"grid gap-4 md:grid-cols-2\">\r\n            {Array.from({ length: uploadingCount }).map((_, idx) => (\r\n              <div key={`uploading-${idx}`} className=\"rounded-lg border p-4\">\r\n                <div className=\"aspect-square overflow-hidden rounded border\">\r\n                  <Skeleton className=\"size-full\" />\r\n                </div>\r\n                <div className=\"mt-3 space-y-2\">\r\n                  <Skeleton className=\"h-4 w-20\" />\r\n                  <Skeleton className=\"h-8 w-full\" />\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Upload Dropzone */}\r\n      {images.length < maxImages && (\r\n        <div className=\"flex\">\r\n          <UploadDropzone<OurFileRouter, \"imageUploader\">\r\n            key={dropzoneKey}\r\n            endpoint=\"imageUploader\"\r\n            onBeforeUploadBegin={handleBeforeUploadBegin}\r\n            onClientUploadComplete={handleClientUploadComplete}\r\n            onUploadError={handleUploadError}\r\n            appearance={{\r\n              button: \"w-28 h-10 text-sm font-semibold ut-ready:bg-green-500\",\r\n              container: \"w-full\",\r\n            }}\r\n          />\r\n        </div>\r\n      )}\r\n\r\n      {/* Alt Text Completion Status */}\r\n      {images.length > 0 && (\r\n        <div className=\"rounded-lg border border-blue-200 bg-blue-50 p-3\">\r\n          <div className=\"flex items-center gap-2\">\r\n            <div className=\"text-sm font-medium text-blue-900\">\r\n              Alt Text Completion:{\" \"}\r\n              {imageAltTexts.filter((alt) => alt && alt.trim() !== \"\").length} /{\" \"}\r\n              {images.length}\r\n            </div>\r\n            {imageAltTexts.filter((alt) => alt && alt.trim() !== \"\").length ===\r\n            images.length ? (\r\n              <span className=\"text-green-600\">✅ All Complete</span>\r\n            ) : (\r\n              <span className=\"text-red-600\">\r\n                ⚠️{\" \"}\r\n                {images.length -\r\n                  imageAltTexts.filter((alt) => alt && alt.trim() !== \"\")\r\n                    .length}{\" \"}\r\n                Missing\r\n              </span>\r\n            )}\r\n          </div>\r\n          <p className=\"mt-1 text-xs text-blue-700\">\r\n            All images must have alt text before you can continue to the next\r\n            step.\r\n          </p>\r\n        </div>\r\n      )}\r\n\r\n      {/* Bottom Note */}\r\n      <div className=\"flex flex-col text-xs text-gray-500\">\r\n        {images.length >= maxImages && (\r\n          <p className=\"font-semibold text-green-500\">\r\n            Maximum image limit reached.\r\n          </p>\r\n        )}\r\n        <p>\r\n          First image will be the main product image. Additional images will be\r\n          shown separately.\r\n        </p>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAEA;AAEA;AACA;AACA;AAGA;AACA;AACA;;;;;;;;;;;AAEA,MAAM,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,qUAAA,CAAA,uBAAoB,AAAD;AAYvC,MAAM,qBAAwD,CAAC,EACpE,MAAM,EACN,SAAS,EACT,aAAa,EACb,gBAAgB,EAChB,YAAY,CAAC,EACd;;IACC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAA2B,CAAC;IAC3E,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAEjE,0BAA0B;IAC1B,MAAM,sBAAsB,CAAC,OAAe;QAC1C,MAAM,cAAc;eAAI;SAAc;QACtC,WAAW,CAAC,MAAM,GAAG;QACrB,iBAAiB;IACnB;IAEA,6DAA6D;IAC7D,MAAM,EAAE,WAAW,EAAE,GAAG,eAAe;IAGvC,MAAM,0BAA0B,CAAC;QAC/B,MAAM,QAAQ,OAAO,MAAM,GAAG,MAAM,MAAM;QAE1C,IAAI,QAAQ,WAAW;YACrB,2QAAA,CAAA,QAAK,CAAC,KAAK,CACT,CAAC,0BAA0B,EAAE,UAAU,0BAA0B,EAAE,OAAO,MAAM,CAAC,CAAC,CAAC;YAGrF,eAAe,CAAC,OAAS,OAAO;YAEhC,MAAM,IAAI,MAAM;QAClB;QAEA,kBAAkB,MAAM,MAAM;QAE9B,OAAO;IACT;IAEA,MAAM,6BAA6B,CAAC;QAClC,kBAAkB;QAElB,IAAI,CAAC,OAAO,IAAI,MAAM,KAAK,GAAG;YAC5B,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YAEZ;QACF;QAEA,MAAM,YAAY,IAAI,GAAG,CAAC,CAAC,OAAS,CAAC;gBACnC,KAAK,KAAK,GAAG;gBACb,KAAK,KAAK,MAAM,IAAI;YACtB,CAAC;QAED,MAAM,gBAAgB;eAAI;eAAW;SAAU;QAE/C,IAAI,cAAc,MAAM,GAAG,WAAW;YACpC,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,0BAA0B,EAAE,UAAU,QAAQ,CAAC;YAE5D,eAAe,CAAC,OAAS,OAAO;YAEhC;QACF;QAEA,UAAU;QAEV,qCAAqC;QACrC,MAAM,cAAc;eAAI;SAAc;QACtC,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;YACzC,YAAY,IAAI,CAAC;QACnB;QACA,iBAAiB;QAEjB,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,MAAM,oBAAoB,CAAC;QACzB,QAAQ,KAAK,CAAC,kBAAkB;QAEhC,eAAe,CAAC,OAAS,OAAO;QAEhC,kBAAkB;QAElB,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,qBAAqB;YAC9C,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,OAAO,IAAI,MAAM,OAAO,KAAK,mBAAmB;YAC9C,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,0BAA0B,EAAE,UAAU,QAAQ,CAAC;QAC9D,OAAO;YACL,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,cAAc,OAAO;QACzB,MAAM,gBAAgB,MAAM,CAAC,MAAM;QAEnC,kBAAkB,CAAC,OAAS;mBAAI;gBAAM,cAAc,GAAG;aAAC;QAExD,WAAW;YACT,gBAAgB,CAAC;gBACf,MAAM,UAAU;oBAAE,GAAG,IAAI;gBAAC;gBAC1B,OAAO,OAAO,CAAC,cAAc,GAAG,CAAC;gBAEjC,OAAO;YACT;YAEA,UAAU,CAAC,OAAS,KAAK,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;YAEhD,yCAAyC;YACzC,iBAAiB,CAAC,OAAS,KAAK,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;YAEvD,IAAI;gBACF,MAAM,UAAU,MAAM,CAAA,GAAA,8HAAA,CAAA,wBAAqB,AAAD,EAAE,cAAc,GAAG;gBAC7D,IAAI,CAAC,SAAS;oBACZ,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACd,OAAO;oBACL,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBAChB;YACF,EAAE,OAAO,GAAG;gBACV,QAAQ,KAAK,CAAC,uCAAuC;gBACrD,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;YAEA,kBAAkB,CAAC,OACjB,KAAK,MAAM,CAAC,CAAC,MAAQ,QAAQ,cAAc,GAAG;QAElD,GAAG;IACL;IAEA,qBACE,4TAAC;QAAI,WAAU;;YAEZ,OAAO,MAAM,GAAG,mBACf,4TAAC;gBAAI,WAAU;;kCACb,4TAAC;wBAAG,WAAU;kCAAoC;;;;;;kCAGlD,4TAAC;wBAAI,WAAU;kCACZ,OAAO,GAAG,CAAC,CAAC,OAAO,kBAClB,4TAAC;gCAEC,WAAW,CAAC,kDAAkD,EAC5D,eAAe,QAAQ,CAAC,MAAM,GAAG,IAC7B,wBACA,yBACJ;;kDAGF,4TAAC;wCAAI,WAAU;;4CACZ,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,kBACvB,4TAAC,gIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DAGtB,4TAAC,+PAAA,CAAA,UAAK;gDACJ,KAAK,MAAM,GAAG;gDACd,KAAK,aAAa,CAAC,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,GAAG;gDACjD,IAAI;gDACJ,OAAM;gDACN,WAAU;gDACV,OAAO;oDACL,SAAS,YAAY,CAAC,MAAM,GAAG,CAAC,GAAG,IAAI;gDACzC;gDACA,QAAQ,IACN,gBAAgB,CAAC,OAAS,CAAC;4DACzB,GAAG,IAAI;4DACP,CAAC,MAAM,GAAG,CAAC,EAAE;wDACf,CAAC;gDAEH,SAAS;;;;;;0DAIX,4TAAC;gDAAI,WAAU;0DACZ,MAAM,IAAI,SAAS,CAAC,CAAC,EAAE,IAAI,GAAG;;;;;;0DAIjC,4TAAC;gDACC,MAAK;gDACL,SAAS,IAAM,YAAY;gDAC3B,WAAU;0DAEV,cAAA,4TAAC,mRAAA,CAAA,IAAC;oDAAC,MAAM;;;;;;;;;;;;;;;;;kDAKb,4TAAC;wCAAI,WAAU;;0DACb,4TAAC;gDAAM,WAAU;;oDAAoC;oDACzC,MAAM,KAAK;kEACrB,4TAAC;wDAAK,WAAU;kEAAe;;;;;;;;;;;;0DAEjC,4TAAC,6HAAA,CAAA,QAAK;gDACJ,OAAO,aAAa,CAAC,EAAE,IAAI;gDAC3B,UAAU,CAAC,IAAM,oBAAoB,GAAG,EAAE,MAAM,CAAC,KAAK;gDACtD,aAAa,CAAC,eAAe,EAAE,IAAI,EAAE,2CAA2C,CAAC;gDACjF,WAAW,CAAC,QAAQ,EAClB,CAAC,aAAa,CAAC,EAAE,IAAI,aAAa,CAAC,EAAE,CAAC,IAAI,OAAO,KAC7C,wCACA,2CACJ;gDACF,QAAQ;;;;;;4CAET,CAAC,aAAa,CAAC,EAAE,IAAI,aAAa,CAAC,EAAE,CAAC,IAAI,OAAO,mBAChD,4TAAC;gDAAE,WAAU;0DAAuB;;;;;qEAIpC,4TAAC;gDAAE,WAAU;0DAAyB;;;;;;;;;;;;;+BApErC,MAAM,GAAG;;;;;;;;;;;;;;;;YAgFvB,iBAAiB,mBAChB,4TAAC;gBAAI,WAAU;;kCACb,4TAAC;wBAAG,WAAU;kCAAoC;;;;;;kCAClD,4TAAC;wBAAI,WAAU;kCACZ,MAAM,IAAI,CAAC;4BAAE,QAAQ;wBAAe,GAAG,GAAG,CAAC,CAAC,GAAG,oBAC9C,4TAAC;gCAA6B,WAAU;;kDACtC,4TAAC;wCAAI,WAAU;kDACb,cAAA,4TAAC,gIAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;kDAEtB,4TAAC;wCAAI,WAAU;;0DACb,4TAAC,gIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,4TAAC,gIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;;;+BANd,CAAC,UAAU,EAAE,KAAK;;;;;;;;;;;;;;;;YAenC,OAAO,MAAM,GAAG,2BACf,4TAAC;gBAAI,WAAU;0BACb,cAAA,4TAAC,iUAAA,CAAA,iBAAc;oBAEb,UAAS;oBACT,qBAAqB;oBACrB,wBAAwB;oBACxB,eAAe;oBACf,YAAY;wBACV,QAAQ;wBACR,WAAW;oBACb;mBARK;;;;;;;;;;YAcV,OAAO,MAAM,GAAG,mBACf,4TAAC;gBAAI,WAAU;;kCACb,4TAAC;wBAAI,WAAU;;0CACb,4TAAC;gCAAI,WAAU;;oCAAoC;oCAC5B;oCACpB,cAAc,MAAM,CAAC,CAAC,MAAQ,OAAO,IAAI,IAAI,OAAO,IAAI,MAAM;oCAAC;oCAAG;oCAClE,OAAO,MAAM;;;;;;;4BAEf,cAAc,MAAM,CAAC,CAAC,MAAQ,OAAO,IAAI,IAAI,OAAO,IAAI,MAAM,KAC/D,OAAO,MAAM,iBACX,4TAAC;gCAAK,WAAU;0CAAiB;;;;;qDAEjC,4TAAC;gCAAK,WAAU;;oCAAe;oCAC1B;oCACF,OAAO,MAAM,GACZ,cAAc,MAAM,CAAC,CAAC,MAAQ,OAAO,IAAI,IAAI,OAAO,IACjD,MAAM;oCAAE;oCAAI;;;;;;;;;;;;;kCAKvB,4TAAC;wBAAE,WAAU;kCAA6B;;;;;;;;;;;;0BAQ9C,4TAAC;gBAAI,WAAU;;oBACZ,OAAO,MAAM,IAAI,2BAChB,4TAAC;wBAAE,WAAU;kCAA+B;;;;;;kCAI9C,4TAAC;kCAAE;;;;;;;;;;;;;;;;;;AAOX;GA5Sa;;QAqBa;;;KArBb", "debugId": null}}, {"offset": {"line": 2208, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/pages/products/add/MediaSection.tsx"], "sourcesContent": ["import {\r\n  Camera,\r\n  Eye,\r\n  Image,\r\n  Info,\r\n  Monitor,\r\n  Palette,\r\n  Smartphone,\r\n  Star,\r\n  Upload,\r\n  Video,\r\n} from \"lucide-react\";\r\nimport {\r\n  FieldErrors,\r\n  UseFormRegister,\r\n  UseFormSetValue,\r\n  UseFormWatch,\r\n} from \"react-hook-form\";\r\n\r\nimport { FormField } from \"@/components/common/FormField\";\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\r\nimport { CollapsibleSection } from \"@/components/ui/collapsible-section\";\r\nimport { ImportantNotice } from \"@/components/ui/important-notice\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { ProductFormData } from \"@/schemas/productSchema\";\r\n\r\nimport { ImageUploadSection } from \"./ImageUploadSection\";\r\n\r\ntype MediaSectionProps = {\r\n  register: UseFormRegister<ProductFormData>;\r\n  errors: FieldErrors<ProductFormData>;\r\n  setValue: UseFormSetValue<ProductFormData>;\r\n  watch: UseFormWatch<ProductFormData>;\r\n  images: { key: string; url: string }[];\r\n  setImages: React.Dispatch<\r\n    React.SetStateAction<{ key: string; url: string }[]>\r\n  >;\r\n  imageAltTexts: string[];\r\n  setImageAltTexts: React.Dispatch<React.SetStateAction<string[]>>;\r\n};\r\n\r\nexport const MediaSection = ({\r\n  register,\r\n  errors,\r\n  setValue,\r\n  watch,\r\n  images,\r\n  setImages,\r\n  imageAltTexts,\r\n  setImageAltTexts,\r\n}: MediaSectionProps) => {\r\n  return (\r\n    <div className=\"space-y-8\">\r\n      {/* Section Header */}\r\n      <div className=\"flex items-center gap-3 border-b border-gray-200 pb-4\">\r\n        <div className=\"rounded-full bg-pink-100 p-2\">\r\n          <Camera className=\"h-5 w-5 text-pink-600\" />\r\n        </div>\r\n        <div>\r\n          <h2 className=\"text-2xl font-bold text-gray-900\">Images & Media</h2>\r\n          <p className=\"text-gray-600\">\r\n            Upload product images and media content\r\n          </p>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Important Notice */}\r\n      <ImportantNotice\r\n        description=\"You must upload at least one product image and provide alt text for all images before proceeding to the next step.\"\r\n        requiredFields={[\"Product Images\", \"Alt Text for All Images\"]}\r\n        tip=\"Product images are essential for customer engagement and sales conversion. Alt text improves accessibility and SEO rankings.\"\r\n        variant=\"amber\"\r\n      />\r\n\r\n      {/* Product Images Card */}\r\n      <Card className=\"border-l-4 border-l-pink-500\">\r\n        <CardHeader className=\"pb-3\">\r\n          <CardTitle className=\"flex items-center gap-2 text-lg\">\r\n            <Image className=\"h-5 w-5 text-pink-600\" />\r\n            Product Images\r\n            <Badge variant=\"destructive\" className=\"text-xs\">\r\n              Required\r\n            </Badge>\r\n          </CardTitle>\r\n          <p className=\"text-sm text-gray-600\">\r\n            Upload high-quality images to showcase your product\r\n          </p>\r\n        </CardHeader>\r\n        <CardContent>\r\n          <FormField\r\n            id=\"media\"\r\n            label=\"\"\r\n            error={errors.mainImage?.message}\r\n            optional={false}\r\n          >\r\n            <div className=\"space-y-4\">\r\n              <ImageUploadSection\r\n                images={images}\r\n                setImages={setImages}\r\n                imageAltTexts={imageAltTexts}\r\n                setImageAltTexts={setImageAltTexts}\r\n                maxImages={4}\r\n              />\r\n\r\n              {/* Image Guidelines */}\r\n              <div className=\"rounded-lg border border-pink-200 bg-pink-50 p-4\">\r\n                <div className=\"mb-3 flex items-start gap-2\">\r\n                  <Camera className=\"mt-0.5 h-4 w-4 text-pink-600\" />\r\n                  <span className=\"font-medium text-pink-900\">\r\n                    Image Guidelines\r\n                  </span>\r\n                </div>\r\n                <div className=\"grid gap-3 md:grid-cols-2\">\r\n                  <div className=\"flex items-center gap-2\">\r\n                    <Monitor className=\"h-4 w-4 text-pink-600\" />\r\n                    <span className=\"text-sm text-pink-800\">\r\n                      Minimum 800 x 800 px\r\n                    </span>\r\n                  </div>\r\n                  <div className=\"flex items-center gap-2\">\r\n                    <Palette className=\"h-4 w-4 text-pink-600\" />\r\n                    <span className=\"text-sm text-pink-800\">\r\n                      JPG, PNG, WebP formats\r\n                    </span>\r\n                  </div>\r\n                  <div className=\"flex items-center gap-2\">\r\n                    <Star className=\"h-4 w-4 text-pink-600\" />\r\n                    <span className=\"text-sm text-pink-800\">\r\n                      Square aspect ratio preferred\r\n                    </span>\r\n                  </div>\r\n                  <div className=\"flex items-center gap-2\">\r\n                    <Eye className=\"h-4 w-4 text-pink-600\" />\r\n                    <span className=\"text-sm text-pink-800\">\r\n                      First image is main display\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </FormField>\r\n        </CardContent>\r\n      </Card>\r\n\r\n      {/* Video Content - Collapsible */}\r\n      <CollapsibleSection\r\n        title=\"Video Content\"\r\n        description=\"Add video URLs to showcase your product in action\"\r\n        icon={<Video className=\"h-5 w-5 text-purple-600\" />}\r\n        borderColor=\"border-l-purple-500\"\r\n        defaultOpen={false}\r\n        isOptional={true}\r\n      >\r\n        <div className=\"space-y-4\">\r\n          {/* Video URL */}\r\n          <FormField id=\"videoUrl\" label=\"Video URL\" optional={true}>\r\n            <Input\r\n              id=\"videoUrl\"\r\n              {...register(\"videoUrl\")}\r\n              placeholder=\"https://youtube.com/watch?v=... or https://vimeo.com/...\"\r\n              className=\"border-2 focus:border-purple-500\"\r\n            />\r\n          </FormField>\r\n\r\n          {/* Video Tips */}\r\n          <div className=\"rounded-lg border border-purple-200 bg-purple-50 p-3\">\r\n            <div className=\"flex items-start gap-2\">\r\n              <Video className=\"mt-0.5 h-4 w-4 text-purple-600\" />\r\n              <div className=\"text-sm text-purple-800\">\r\n                <strong>Video Tips:</strong> Product demos, unboxing videos, and\r\n                360° views increase conversion rates by up to 80%\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </CollapsibleSection>\r\n\r\n      {/* 3D Models & AR - Collapsible */}\r\n      <CollapsibleSection\r\n        title=\"3D Models & AR\"\r\n        description=\"Add 3D models for immersive product experiences\"\r\n        icon={<Monitor className=\"h-5 w-5 text-green-600\" />}\r\n        borderColor=\"border-l-green-500\"\r\n        defaultOpen={false}\r\n        isOptional={true}\r\n      >\r\n        <div className=\"space-y-4\">\r\n          {/* 3D Model URL */}\r\n          <FormField id=\"threeDModelUrl\" label=\"3D Model URL\" optional={true}>\r\n            <Input\r\n              id=\"threeDModelUrl\"\r\n              {...register(\"threeDModelUrl\")}\r\n              placeholder=\"https://example.com/model.glb or .usdz file URL\"\r\n              className=\"border-2 focus:border-green-500\"\r\n            />\r\n          </FormField>\r\n\r\n          {/* AR Instructions */}\r\n          <div className=\"rounded-lg border border-green-200 bg-green-50 p-3\">\r\n            <div className=\"flex items-start gap-2\">\r\n              <Smartphone className=\"mt-0.5 h-4 w-4 text-green-600\" />\r\n              <div className=\"text-sm text-green-800\">\r\n                <strong>AR Ready:</strong> Upload .glb or .usdz files to enable\r\n                View in AR on mobile devices\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </CollapsibleSection>\r\n\r\n      {/* Media Summary Card */}\r\n      <Card className=\"border-pink-200 bg-gradient-to-r from-pink-50 to-purple-50\">\r\n        <CardHeader>\r\n          <CardTitle className=\"flex items-center gap-2 text-pink-800\">\r\n            <Upload className=\"h-5 w-5\" />\r\n            Media Best Practices\r\n          </CardTitle>\r\n        </CardHeader>\r\n        <CardContent>\r\n          <div className=\"grid gap-4 md:grid-cols-2\">\r\n            <div className=\"flex items-start gap-3\">\r\n              <div className=\"rounded-full bg-pink-100 p-2\">\r\n                <Image className=\"h-4 w-4 text-pink-600\" />\r\n              </div>\r\n              <div>\r\n                <h4 className=\"font-medium text-pink-900\">\r\n                  High Quality Images\r\n                </h4>\r\n                <p className=\"mt-1 text-sm text-pink-700\">\r\n                  Use professional lighting and multiple angles to showcase\r\n                  product details\r\n                </p>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"flex items-start gap-3\">\r\n              <div className=\"rounded-full bg-purple-100 p-2\">\r\n                <Video className=\"h-4 w-4 text-purple-600\" />\r\n              </div>\r\n              <div>\r\n                <h4 className=\"font-medium text-purple-900\">Video Content</h4>\r\n                <p className=\"mt-1 text-sm text-purple-700\">\r\n                  Show products in use, demonstrate features, and provide 360°\r\n                  views\r\n                </p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAmBA;AACA;AACA;AACA;AACA;AACA;AAGA;;;;;;;;;;AAeO,MAAM,eAAe,CAAC,EAC3B,QAAQ,EACR,MAAM,EACN,QAAQ,EACR,KAAK,EACL,MAAM,EACN,SAAS,EACT,aAAa,EACb,gBAAgB,EACE;IAClB,qBACE,4TAAC;QAAI,WAAU;;0BAEb,4TAAC;gBAAI,WAAU;;kCACb,4TAAC;wBAAI,WAAU;kCACb,cAAA,4TAAC,6RAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;;;;;;kCAEpB,4TAAC;;0CACC,4TAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,4TAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;;;;;;;0BAOjC,4TAAC,2IAAA,CAAA,kBAAe;gBACd,aAAY;gBACZ,gBAAgB;oBAAC;oBAAkB;iBAA0B;gBAC7D,KAAI;gBACJ,SAAQ;;;;;;0BAIV,4TAAC,4HAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,4TAAC,4HAAA,CAAA,aAAU;wBAAC,WAAU;;0CACpB,4TAAC,4HAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,4TAAC,2RAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;oCAA0B;kDAE3C,4TAAC,6HAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAc,WAAU;kDAAU;;;;;;;;;;;;0CAInD,4TAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAIvC,4TAAC,4HAAA,CAAA,cAAW;kCACV,cAAA,4TAAC,qIAAA,CAAA,YAAS;4BACR,IAAG;4BACH,OAAM;4BACN,OAAO,OAAO,SAAS,EAAE;4BACzB,UAAU;sCAEV,cAAA,4TAAC;gCAAI,WAAU;;kDACb,4TAAC,gKAAA,CAAA,qBAAkB;wCACjB,QAAQ;wCACR,WAAW;wCACX,eAAe;wCACf,kBAAkB;wCAClB,WAAW;;;;;;kDAIb,4TAAC;wCAAI,WAAU;;0DACb,4TAAC;gDAAI,WAAU;;kEACb,4TAAC,6RAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,4TAAC;wDAAK,WAAU;kEAA4B;;;;;;;;;;;;0DAI9C,4TAAC;gDAAI,WAAU;;kEACb,4TAAC;wDAAI,WAAU;;0EACb,4TAAC,+RAAA,CAAA,UAAO;gEAAC,WAAU;;;;;;0EACnB,4TAAC;gEAAK,WAAU;0EAAwB;;;;;;;;;;;;kEAI1C,4TAAC;wDAAI,WAAU;;0EACb,4TAAC,+RAAA,CAAA,UAAO;gEAAC,WAAU;;;;;;0EACnB,4TAAC;gEAAK,WAAU;0EAAwB;;;;;;;;;;;;kEAI1C,4TAAC;wDAAI,WAAU;;0EACb,4TAAC,yRAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,4TAAC;gEAAK,WAAU;0EAAwB;;;;;;;;;;;;kEAI1C,4TAAC;wDAAI,WAAU;;0EACb,4TAAC,uRAAA,CAAA,MAAG;gEAAC,WAAU;;;;;;0EACf,4TAAC;gEAAK,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAYtD,4TAAC,8IAAA,CAAA,qBAAkB;gBACjB,OAAM;gBACN,aAAY;gBACZ,oBAAM,4TAAC,2RAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;gBACvB,aAAY;gBACZ,aAAa;gBACb,YAAY;0BAEZ,cAAA,4TAAC;oBAAI,WAAU;;sCAEb,4TAAC,qIAAA,CAAA,YAAS;4BAAC,IAAG;4BAAW,OAAM;4BAAY,UAAU;sCACnD,cAAA,4TAAC,6HAAA,CAAA,QAAK;gCACJ,IAAG;gCACF,GAAG,SAAS,WAAW;gCACxB,aAAY;gCACZ,WAAU;;;;;;;;;;;sCAKd,4TAAC;4BAAI,WAAU;sCACb,cAAA,4TAAC;gCAAI,WAAU;;kDACb,4TAAC,2RAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,4TAAC;wCAAI,WAAU;;0DACb,4TAAC;0DAAO;;;;;;4CAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAStC,4TAAC,8IAAA,CAAA,qBAAkB;gBACjB,OAAM;gBACN,aAAY;gBACZ,oBAAM,4TAAC,+RAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;gBACzB,aAAY;gBACZ,aAAa;gBACb,YAAY;0BAEZ,cAAA,4TAAC;oBAAI,WAAU;;sCAEb,4TAAC,qIAAA,CAAA,YAAS;4BAAC,IAAG;4BAAiB,OAAM;4BAAe,UAAU;sCAC5D,cAAA,4TAAC,6HAAA,CAAA,QAAK;gCACJ,IAAG;gCACF,GAAG,SAAS,iBAAiB;gCAC9B,aAAY;gCACZ,WAAU;;;;;;;;;;;sCAKd,4TAAC;4BAAI,WAAU;sCACb,cAAA,4TAAC;gCAAI,WAAU;;kDACb,4TAAC,qSAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;kDACtB,4TAAC;wCAAI,WAAU;;0DACb,4TAAC;0DAAO;;;;;;4CAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASpC,4TAAC,4HAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,4TAAC,4HAAA,CAAA,aAAU;kCACT,cAAA,4TAAC,4HAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,4TAAC,6RAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;;;;;kCAIlC,4TAAC,4HAAA,CAAA,cAAW;kCACV,cAAA,4TAAC;4BAAI,WAAU;;8CACb,4TAAC;oCAAI,WAAU;;sDACb,4TAAC;4CAAI,WAAU;sDACb,cAAA,4TAAC,2RAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,4TAAC;;8DACC,4TAAC;oDAAG,WAAU;8DAA4B;;;;;;8DAG1C,4TAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;;;;;;;8CAO9C,4TAAC;oCAAI,WAAU;;sDACb,4TAAC;4CAAI,WAAU;sDACb,cAAA,4TAAC,2RAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,4TAAC;;8DACC,4TAAC;oDAAG,WAAU;8DAA8B;;;;;;8DAC5C,4TAAC;oDAAE,WAAU;8DAA+B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW5D;KAnNa", "debugId": null}}, {"offset": {"line": 2859, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/pages/products/add/PricingSection.tsx"], "sourcesContent": ["import { useCallback, useEffect, useState } from \"react\";\r\n\r\nimport { format } from \"date-fns\";\r\nimport {\r\n  Calculator,\r\n  CalendarIcon,\r\n  DollarSign,\r\n  Percent,\r\n  TrendingDown,\r\n} from \"lucide-react\";\r\nimport {\r\n  FieldErrors,\r\n  UseFormRegister,\r\n  UseFormSetValue,\r\n  UseFormWatch,\r\n} from \"react-hook-form\";\r\n\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Calendar } from \"@/components/ui/calendar\";\r\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Label } from \"@/components/ui/label\";\r\nimport {\r\n  Popover,\r\n  PopoverContent,\r\n  PopoverTrigger,\r\n} from \"@/components/ui/popover\";\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from \"@/components/ui/select\";\r\nimport { Switch } from \"@/components/ui/switch\";\r\nimport { ProductFormData, currencyValues } from \"@/schemas/productSchema\";\r\nimport { CurrencyUnit } from \"@/types/common\";\r\n\r\nimport { FormField } from \"../../../common/FormField\";\r\nimport { ImportantNotice } from \"../../../ui/important-notice\";\r\n\r\ntype PricingSectionProps = {\r\n  register: UseFormRegister<ProductFormData>;\r\n  errors: FieldErrors<ProductFormData>;\r\n  setValue: UseFormSetValue<ProductFormData>;\r\n  watch: UseFormWatch<ProductFormData>;\r\n};\r\n\r\nexport const PricingSection: React.FC<PricingSectionProps> = ({\r\n  register,\r\n  errors,\r\n  setValue,\r\n  watch,\r\n}) => {\r\n  const [saleDate, setSaleDate] = useState<Date | undefined>();\r\n\r\n  // Watch form values for dynamic calculations\r\n  const regularPrice = watch(\"price\"); // Regular/standard price\r\n  const salePrice = watch(\"salePrice\"); // Discounted sale price (new field)\r\n  const isOnSale = watch(\"isOnSale\");\r\n  const costPrice = watch(\"costPrice\");\r\n\r\n  // Helper function to check if a value is a valid positive number\r\n  const isValidNumber = useCallback((value: unknown): value is number => {\r\n    return typeof value === \"number\" && !isNaN(value) && value > 0;\r\n  }, []);\r\n\r\n  // Calculate current selling price (what customers actually pay)\r\n  const currentSellingPrice =\r\n    isOnSale && isValidNumber(salePrice) ? salePrice : regularPrice;\r\n\r\n  // Calculate savings and profit margin - only if we have valid numbers\r\n  const savings =\r\n    isOnSale && isValidNumber(regularPrice) && isValidNumber(salePrice)\r\n      ? regularPrice - salePrice\r\n      : 0;\r\n\r\n  const savingsPercentage =\r\n    isOnSale && isValidNumber(regularPrice) && savings > 0\r\n      ? Math.round((savings / regularPrice) * 100)\r\n      : 0;\r\n\r\n  const profitMargin =\r\n    isValidNumber(costPrice) && isValidNumber(currentSellingPrice)\r\n      ? Math.round(\r\n          ((currentSellingPrice - costPrice) / currentSellingPrice) * 100\r\n        )\r\n      : 0;\r\n\r\n  // Quick discount percentages\r\n  const quickDiscounts = [5, 10, 15, 20, 25, 30, 40, 50];\r\n\r\n  // Apply quick discount\r\n  const applyQuickDiscount = (percentage: number) => {\r\n    if (isValidNumber(regularPrice)) {\r\n      const discountAmount = regularPrice * (percentage / 100);\r\n      const newSalePrice = regularPrice - discountAmount;\r\n      setValue(\"salePrice\", Number(newSalePrice.toFixed(2))); // Using proper salePrice field\r\n      setValue(\"originalPrice\", regularPrice); // Set originalPrice for backwards compatibility\r\n      setValue(\"isOnSale\", true);\r\n    }\r\n  };\r\n\r\n  // Clear sale\r\n  const clearSale = () => {\r\n    setValue(\"isOnSale\", false);\r\n    setValue(\"salePrice\", undefined);\r\n    setValue(\"originalPrice\", undefined);\r\n  };\r\n\r\n  const handleSaleToggle = (checked: boolean) => {\r\n    setValue(\"isOnSale\", checked);\r\n    if (!checked) {\r\n      setValue(\"originalPrice\", undefined);\r\n      setValue(\"saleEndsAt\", undefined);\r\n      setSaleDate(undefined);\r\n    }\r\n  };\r\n\r\n  const handleDateSelect = (date: Date | undefined) => {\r\n    setSaleDate(date);\r\n    if (date) {\r\n      setValue(\"saleEndsAt\", date.toISOString());\r\n    } else {\r\n      setValue(\"saleEndsAt\", undefined);\r\n    }\r\n  };\r\n\r\n  // Update isOnSale when component mounts\r\n  useEffect(() => {\r\n    if (\r\n      isValidNumber(regularPrice) &&\r\n      isValidNumber(salePrice) &&\r\n      regularPrice > salePrice\r\n    ) {\r\n      setValue(\"isOnSale\", true);\r\n    }\r\n  }, [regularPrice, salePrice, setValue, isValidNumber]);\r\n\r\n  return (\r\n    <div className=\"space-y-8\">\r\n      {/* Section Header */}\r\n      <div className=\"flex items-center gap-3 border-b border-gray-200 pb-4\">\r\n        <div className=\"rounded-full bg-green-100 p-2\">\r\n          <DollarSign className=\"h-5 w-5 text-green-600\" />\r\n        </div>\r\n        <div>\r\n          <h2 className=\"text-2xl font-bold text-gray-900\">\r\n            Pricing Information\r\n          </h2>\r\n          <p className=\"text-gray-600\">\r\n            Set your product pricing and sale options\r\n          </p>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Important Notice */}\r\n      <ImportantNotice\r\n        description=\"You must complete all required fields in this section before proceeding to the next step.\"\r\n        requiredFields={[\"Regular Price\", \"Currency\", \"Cost Price\"]}\r\n        tip=\"Cost price helps calculate profit margins and is essential for business analytics.\"\r\n        variant=\"amber\"\r\n      />\r\n\r\n      {/* Regular Price Card */}\r\n      <Card className=\"border-l-4 border-l-blue-500\">\r\n        <CardHeader className=\"pb-3\">\r\n          <CardTitle className=\"flex items-center gap-2 text-lg\">\r\n            <DollarSign className=\"h-5 w-5 text-blue-600\" />\r\n            Regular Price\r\n            <Badge variant=\"destructive\" className=\"text-xs\">\r\n              Required\r\n            </Badge>\r\n          </CardTitle>\r\n          <p className=\"text-sm text-gray-600\">\r\n            The standard price of your product (before any discounts)\r\n          </p>\r\n        </CardHeader>\r\n        <CardContent>\r\n          <div className=\"grid gap-4 md:grid-cols-2\">\r\n            <div className=\"space-y-2\">\r\n              <div className=\"flex min-h-[20px] items-center justify-between\">\r\n                <span className=\"text-sm font-medium text-gray-700\">Price</span>\r\n                {errors.price && (\r\n                  <span className=\"text-xs font-medium text-destructive\">\r\n                    {errors.price.message}\r\n                  </span>\r\n                )}\r\n              </div>\r\n              <Input\r\n                id=\"price\"\r\n                type=\"number\"\r\n                step=\"0.01\"\r\n                {...register(\"price\", {\r\n                  setValueAs: (value) => {\r\n                    if (value === \"\" || value === null || value === undefined) {\r\n                      return undefined;\r\n                    }\r\n                    const numValue = parseFloat(value);\r\n                    return isNaN(numValue) ? undefined : numValue;\r\n                  },\r\n                })}\r\n                placeholder=\"0.00\"\r\n                className=\"border-2 p-4 text-lg focus:border-blue-500\"\r\n              />\r\n            </div>\r\n\r\n            <div className=\"space-y-2\">\r\n              <div className=\"flex min-h-[20px] items-center justify-between\">\r\n                <span className=\"text-sm font-medium text-gray-700\">\r\n                  Currency\r\n                </span>\r\n                {errors.currency && (\r\n                  <span className=\"text-xs font-medium text-destructive\">\r\n                    {errors.currency.message}\r\n                  </span>\r\n                )}\r\n              </div>\r\n              <Select\r\n                onValueChange={(value) =>\r\n                  setValue(\"currency\", value as CurrencyUnit)\r\n                }\r\n                defaultValue={watch(\"currency\")}\r\n              >\r\n                <SelectTrigger className=\"border-2 p-4 text-lg focus:border-blue-500\">\r\n                  <SelectValue placeholder=\"Select currency\" />\r\n                </SelectTrigger>\r\n                <SelectContent>\r\n                  {currencyValues.map((currency) => (\r\n                    <SelectItem key={currency} value={currency}>\r\n                      {currency}\r\n                    </SelectItem>\r\n                  ))}\r\n                </SelectContent>\r\n              </Select>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Current Selling Price Display */}\r\n          {isValidNumber(regularPrice) && (\r\n            <div className=\"mt-4 rounded-lg border border-blue-200 bg-blue-50 p-3\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <span className=\"text-sm font-medium text-blue-900\">\r\n                  Customers will pay:\r\n                </span>\r\n                <span className=\"text-lg font-bold text-blue-700\">\r\n                  {watch(\"currency\")}{\" \"}\r\n                  {isValidNumber(currentSellingPrice)\r\n                    ? currentSellingPrice.toFixed(2)\r\n                    : regularPrice.toFixed(2)}\r\n                  {isOnSale && savingsPercentage > 0 && (\r\n                    <span className=\"ml-2 text-sm text-green-600\">\r\n                      ({savingsPercentage}% OFF!)\r\n                    </span>\r\n                  )}\r\n                </span>\r\n              </div>\r\n            </div>\r\n          )}\r\n        </CardContent>\r\n      </Card>\r\n\r\n      {/* Cost Price & Business Intelligence Card */}\r\n      <Card className=\"border-l-4 border-l-blue-500\">\r\n        <CardHeader className=\"pb-3\">\r\n          <CardTitle className=\"flex items-center gap-2 text-lg\">\r\n            <Calculator className=\"h-5 w-5 text-blue-600\" />\r\n            Business Intelligence\r\n            <Badge variant=\"destructive\" className=\"text-xs\">\r\n              Required\r\n            </Badge>\r\n          </CardTitle>\r\n          <p className=\"text-sm text-gray-600\">\r\n            Track costs and profit margins for business analysis - required for\r\n            proceeding\r\n          </p>\r\n        </CardHeader>\r\n        <CardContent>\r\n          <FormField\r\n            id=\"costPrice\"\r\n            label=\"Cost Price (What you paid)\"\r\n            error={errors.costPrice?.message}\r\n            optional={false}\r\n          >\r\n            <Input\r\n              id=\"costPrice\"\r\n              type=\"number\"\r\n              step=\"0.01\"\r\n              {...register(\"costPrice\", {\r\n                setValueAs: (value) => {\r\n                  if (value === \"\" || value === null || value === undefined) {\r\n                    return undefined;\r\n                  }\r\n                  const numValue = parseFloat(value);\r\n                  return isNaN(numValue) ? undefined : numValue;\r\n                },\r\n              })}\r\n              placeholder=\"0.00\"\r\n              className=\"border-2 p-4 text-lg focus:border-blue-500\"\r\n            />\r\n          </FormField>\r\n\r\n          {/* Profit Margin Display */}\r\n          {isValidNumber(costPrice) && isValidNumber(currentSellingPrice) && (\r\n            <div className=\"mt-3 rounded-lg bg-blue-50 p-3\">\r\n              <div className=\"mb-2 flex items-center gap-2\">\r\n                <TrendingDown className=\"h-4 w-4 text-blue-600\" />\r\n                <span className=\"font-medium text-blue-900\">\r\n                  Profit Analysis\r\n                </span>\r\n              </div>\r\n              <div className=\"grid gap-2 text-sm\">\r\n                <div className=\"flex justify-between\">\r\n                  <span>Profit per Sale:</span>\r\n                  <span className=\"font-medium text-blue-600\">\r\n                    {watch(\"currency\")}{\" \"}\r\n                    {(currentSellingPrice - costPrice).toFixed(2)}\r\n                  </span>\r\n                </div>\r\n                <div className=\"flex justify-between\">\r\n                  <span>Profit Margin:</span>\r\n                  <span\r\n                    className={`font-medium ${profitMargin > 0 ? \"text-green-600\" : \"text-red-600\"}`}\r\n                  >\r\n                    {profitMargin}%\r\n                  </span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          )}\r\n        </CardContent>\r\n      </Card>\r\n\r\n      {/* Sale Configuration Card */}\r\n      <Card className=\"border-l-4 border-l-red-500\">\r\n        <CardHeader className=\"pb-3\">\r\n          <CardTitle className=\"flex items-center gap-2 text-lg\">\r\n            <TrendingDown className=\"h-5 w-5 text-red-600\" />\r\n            Sale & Discounts\r\n            <Badge variant=\"secondary\" className=\"text-xs\">\r\n              Optional\r\n            </Badge>\r\n          </CardTitle>\r\n          <p className=\"text-sm text-gray-600\">\r\n            Set up discounts and promotional pricing\r\n          </p>\r\n        </CardHeader>\r\n        <CardContent>\r\n          <div className=\"space-y-4\">\r\n            <div className=\"flex items-center space-x-3\">\r\n              <Switch\r\n                id=\"sale-toggle\"\r\n                checked={isOnSale}\r\n                onCheckedChange={handleSaleToggle}\r\n              />\r\n              <Label htmlFor=\"sale-toggle\" className=\"text-base font-medium\">\r\n                Put this product on sale\r\n              </Label>\r\n            </div>\r\n\r\n            {/* Quick Discount Buttons */}\r\n            {isValidNumber(regularPrice) && !isOnSale && (\r\n              <div className=\"rounded-lg border border-gray-200 bg-gray-50 p-4\">\r\n                <h4 className=\"mb-3 font-medium text-gray-900\">\r\n                  Quick Discounts\r\n                </h4>\r\n                <div className=\"grid grid-cols-4 gap-2 md:grid-cols-8\">\r\n                  {quickDiscounts.map((percentage) => (\r\n                    <Button\r\n                      key={percentage}\r\n                      type=\"button\"\r\n                      variant=\"outline\"\r\n                      size=\"sm\"\r\n                      onClick={() => applyQuickDiscount(percentage)}\r\n                      className=\"text-xs hover:border-red-300 hover:bg-red-50\"\r\n                    >\r\n                      {percentage}% OFF\r\n                    </Button>\r\n                  ))}\r\n                </div>\r\n                <p className=\"mt-2 text-xs text-gray-500\">\r\n                  Click any button to automatically calculate the sale price\r\n                </p>\r\n              </div>\r\n            )}\r\n\r\n            {isOnSale && (\r\n              <div className=\"space-y-4 rounded-lg border border-red-200 bg-red-50 p-4\">\r\n                <div className=\"grid gap-4 md:grid-cols-2\">\r\n                  <FormField\r\n                    id=\"salePrice\"\r\n                    label=\"Sale Price\"\r\n                    error={errors.salePrice?.message}\r\n                    optional={true}\r\n                  >\r\n                    <Input\r\n                      id=\"salePrice\"\r\n                      type=\"number\"\r\n                      step=\"0.01\"\r\n                      {...register(\"salePrice\", {\r\n                        setValueAs: (value) => {\r\n                          if (\r\n                            value === \"\" ||\r\n                            value === null ||\r\n                            value === undefined\r\n                          ) {\r\n                            return undefined;\r\n                          }\r\n                          const numValue = parseFloat(value);\r\n                          return isNaN(numValue) ? undefined : numValue;\r\n                        },\r\n                      })}\r\n                      placeholder=\"0.00\"\r\n                      className=\"border-2 focus:border-red-500\"\r\n                    />\r\n                    <p className=\"mt-1 text-xs text-gray-500\">\r\n                      The discounted price customers will pay\r\n                    </p>\r\n                  </FormField>\r\n\r\n                  <FormField\r\n                    id=\"saleEndsAt\"\r\n                    label=\"Sale End Date\"\r\n                    optional={true}\r\n                  >\r\n                    <Popover>\r\n                      <PopoverTrigger asChild>\r\n                        <Button\r\n                          variant=\"outline\"\r\n                          className=\"w-full justify-start border-2 text-left font-normal focus:border-red-500\"\r\n                        >\r\n                          <CalendarIcon className=\"mr-2 h-4 w-4\" />\r\n                          {saleDate ? (\r\n                            format(saleDate, \"PPP\")\r\n                          ) : (\r\n                            <span>Pick a date</span>\r\n                          )}\r\n                        </Button>\r\n                      </PopoverTrigger>\r\n                      <PopoverContent className=\"w-auto p-0\">\r\n                        <Calendar\r\n                          mode=\"single\"\r\n                          selected={saleDate}\r\n                          onSelect={handleDateSelect}\r\n                          initialFocus\r\n                        />\r\n                      </PopoverContent>\r\n                    </Popover>\r\n                  </FormField>\r\n                </div>\r\n\r\n                {/* Quick Discount Buttons for Active Sale */}\r\n                <div className=\"rounded-lg border border-red-300 bg-white p-3\">\r\n                  <div className=\"mb-2 flex items-center justify-between\">\r\n                    <h4 className=\"font-medium text-red-900\">\r\n                      Quick Discounts\r\n                    </h4>\r\n                    <Button\r\n                      type=\"button\"\r\n                      variant=\"ghost\"\r\n                      size=\"sm\"\r\n                      onClick={clearSale}\r\n                      className=\"text-xs text-red-600 hover:bg-red-100\"\r\n                    >\r\n                      Clear Sale\r\n                    </Button>\r\n                  </div>\r\n                  <div className=\"grid grid-cols-4 gap-2 md:grid-cols-8\">\r\n                    {quickDiscounts.map((percentage) => (\r\n                      <Button\r\n                        key={percentage}\r\n                        type=\"button\"\r\n                        variant={\r\n                          savingsPercentage === percentage\r\n                            ? \"default\"\r\n                            : \"outline\"\r\n                        }\r\n                        size=\"sm\"\r\n                        onClick={() => applyQuickDiscount(percentage)}\r\n                        className={`text-xs ${\r\n                          savingsPercentage === percentage\r\n                            ? \"bg-red-600 text-white\"\r\n                            : \"hover:border-red-300 hover:bg-red-50\"\r\n                        }`}\r\n                      >\r\n                        {percentage}%\r\n                      </Button>\r\n                    ))}\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Enhanced Sale Summary */}\r\n                {isValidNumber(regularPrice) && isValidNumber(salePrice) && (\r\n                  <div className=\"rounded-lg border border-red-300 bg-white p-3\">\r\n                    <div className=\"mb-2 flex items-center gap-2\">\r\n                      <Percent className=\"h-4 w-4 text-red-600\" />\r\n                      <span className=\"font-medium text-red-900\">\r\n                        Sale Analysis\r\n                      </span>\r\n                    </div>\r\n                    <div className=\"grid gap-2 text-sm\">\r\n                      {regularPrice > salePrice ? (\r\n                        <>\r\n                          <div className=\"flex justify-between\">\r\n                            <span>Regular Price:</span>\r\n                            <span className=\"font-medium text-gray-600 line-through\">\r\n                              {watch(\"currency\")} {regularPrice.toFixed(2)}\r\n                            </span>\r\n                          </div>\r\n                          <div className=\"flex justify-between\">\r\n                            <span>Sale Price:</span>\r\n                            <span className=\"font-medium text-green-600\">\r\n                              {watch(\"currency\")} {salePrice.toFixed(2)}\r\n                            </span>\r\n                          </div>\r\n                          <div className=\"flex justify-between border-t pt-2\">\r\n                            <span>Customer Saves:</span>\r\n                            <span className=\"font-bold text-red-600\">\r\n                              {watch(\"currency\")} {savings.toFixed(2)}\r\n                            </span>\r\n                          </div>\r\n                          <div className=\"mt-2 rounded bg-green-50 p-2 text-center\">\r\n                            <span className=\"text-lg font-bold text-green-700\">\r\n                              {savingsPercentage}% OFF\r\n                            </span>\r\n                          </div>\r\n                        </>\r\n                      ) : regularPrice === salePrice ? (\r\n                        <div className=\"text-center text-amber-600\">\r\n                          <span className=\"font-medium\">\r\n                            ⚠️ No discount - prices are equal\r\n                          </span>\r\n                        </div>\r\n                      ) : (\r\n                        <div className=\"text-center text-red-600\">\r\n                          <span className=\"font-medium\">\r\n                            ⚠️ Sale price is higher than regular price\r\n                          </span>\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            )}\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAcA;AACA;AACA;AACA;AACA;AACA;AACA;AAKA;AAOA;AACA;AAGA;AACA;;;;;;;;;;;;;;;;;;AASO,MAAM,iBAAgD,CAAC,EAC5D,QAAQ,EACR,MAAM,EACN,QAAQ,EACR,KAAK,EACN;;IACC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD;IAEvC,6CAA6C;IAC7C,MAAM,eAAe,MAAM,UAAU,yBAAyB;IAC9D,MAAM,YAAY,MAAM,cAAc,oCAAoC;IAC1E,MAAM,WAAW,MAAM;IACvB,MAAM,YAAY,MAAM;IAExB,iEAAiE;IACjE,MAAM,gBAAgB,CAAA,GAAA,4RAAA,CAAA,cAAW,AAAD;qDAAE,CAAC;YACjC,OAAO,OAAO,UAAU,YAAY,CAAC,MAAM,UAAU,QAAQ;QAC/D;oDAAG,EAAE;IAEL,gEAAgE;IAChE,MAAM,sBACJ,YAAY,cAAc,aAAa,YAAY;IAErD,sEAAsE;IACtE,MAAM,UACJ,YAAY,cAAc,iBAAiB,cAAc,aACrD,eAAe,YACf;IAEN,MAAM,oBACJ,YAAY,cAAc,iBAAiB,UAAU,IACjD,KAAK,KAAK,CAAC,AAAC,UAAU,eAAgB,OACtC;IAEN,MAAM,eACJ,cAAc,cAAc,cAAc,uBACtC,KAAK,KAAK,CACR,AAAC,CAAC,sBAAsB,SAAS,IAAI,sBAAuB,OAE9D;IAEN,6BAA6B;IAC7B,MAAM,iBAAiB;QAAC;QAAG;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;KAAG;IAEtD,uBAAuB;IACvB,MAAM,qBAAqB,CAAC;QAC1B,IAAI,cAAc,eAAe;YAC/B,MAAM,iBAAiB,eAAe,CAAC,aAAa,GAAG;YACvD,MAAM,eAAe,eAAe;YACpC,SAAS,aAAa,OAAO,aAAa,OAAO,CAAC,MAAM,+BAA+B;YACvF,SAAS,iBAAiB,eAAe,gDAAgD;YACzF,SAAS,YAAY;QACvB;IACF;IAEA,aAAa;IACb,MAAM,YAAY;QAChB,SAAS,YAAY;QACrB,SAAS,aAAa;QACtB,SAAS,iBAAiB;IAC5B;IAEA,MAAM,mBAAmB,CAAC;QACxB,SAAS,YAAY;QACrB,IAAI,CAAC,SAAS;YACZ,SAAS,iBAAiB;YAC1B,SAAS,cAAc;YACvB,YAAY;QACd;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,YAAY;QACZ,IAAI,MAAM;YACR,SAAS,cAAc,KAAK,WAAW;QACzC,OAAO;YACL,SAAS,cAAc;QACzB;IACF;IAEA,wCAAwC;IACxC,CAAA,GAAA,4RAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IACE,cAAc,iBACd,cAAc,cACd,eAAe,WACf;gBACA,SAAS,YAAY;YACvB;QACF;mCAAG;QAAC;QAAc;QAAW;QAAU;KAAc;IAErD,qBACE,4TAAC;QAAI,WAAU;;0BAEb,4TAAC;gBAAI,WAAU;;kCACb,4TAAC;wBAAI,WAAU;kCACb,cAAA,4TAAC,ySAAA,CAAA,aAAU;4BAAC,WAAU;;;;;;;;;;;kCAExB,4TAAC;;0CACC,4TAAC;gCAAG,WAAU;0CAAmC;;;;;;0CAGjD,4TAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;;;;;;;0BAOjC,4TAAC,2IAAA,CAAA,kBAAe;gBACd,aAAY;gBACZ,gBAAgB;oBAAC;oBAAiB;oBAAY;iBAAa;gBAC3D,KAAI;gBACJ,SAAQ;;;;;;0BAIV,4TAAC,4HAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,4TAAC,4HAAA,CAAA,aAAU;wBAAC,WAAU;;0CACpB,4TAAC,4HAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,4TAAC,ySAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;oCAA0B;kDAEhD,4TAAC,6HAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAc,WAAU;kDAAU;;;;;;;;;;;;0CAInD,4TAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAIvC,4TAAC,4HAAA,CAAA,cAAW;;0CACV,4TAAC;gCAAI,WAAU;;kDACb,4TAAC;wCAAI,WAAU;;0DACb,4TAAC;gDAAI,WAAU;;kEACb,4TAAC;wDAAK,WAAU;kEAAoC;;;;;;oDACnD,OAAO,KAAK,kBACX,4TAAC;wDAAK,WAAU;kEACb,OAAO,KAAK,CAAC,OAAO;;;;;;;;;;;;0DAI3B,4TAAC,6HAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,MAAK;gDACJ,GAAG,SAAS,SAAS;oDACpB,YAAY,CAAC;wDACX,IAAI,UAAU,MAAM,UAAU,QAAQ,UAAU,WAAW;4DACzD,OAAO;wDACT;wDACA,MAAM,WAAW,WAAW;wDAC5B,OAAO,MAAM,YAAY,YAAY;oDACvC;gDACF,EAAE;gDACF,aAAY;gDACZ,WAAU;;;;;;;;;;;;kDAId,4TAAC;wCAAI,WAAU;;0DACb,4TAAC;gDAAI,WAAU;;kEACb,4TAAC;wDAAK,WAAU;kEAAoC;;;;;;oDAGnD,OAAO,QAAQ,kBACd,4TAAC;wDAAK,WAAU;kEACb,OAAO,QAAQ,CAAC,OAAO;;;;;;;;;;;;0DAI9B,4TAAC,8HAAA,CAAA,SAAM;gDACL,eAAe,CAAC,QACd,SAAS,YAAY;gDAEvB,cAAc,MAAM;;kEAEpB,4TAAC,8HAAA,CAAA,gBAAa;wDAAC,WAAU;kEACvB,cAAA,4TAAC,8HAAA,CAAA,cAAW;4DAAC,aAAY;;;;;;;;;;;kEAE3B,4TAAC,8HAAA,CAAA,gBAAa;kEACX,2HAAA,CAAA,iBAAc,CAAC,GAAG,CAAC,CAAC,yBACnB,4TAAC,8HAAA,CAAA,aAAU;gEAAgB,OAAO;0EAC/B;+DADc;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAU1B,cAAc,+BACb,4TAAC;gCAAI,WAAU;0CACb,cAAA,4TAAC;oCAAI,WAAU;;sDACb,4TAAC;4CAAK,WAAU;sDAAoC;;;;;;sDAGpD,4TAAC;4CAAK,WAAU;;gDACb,MAAM;gDAAa;gDACnB,cAAc,uBACX,oBAAoB,OAAO,CAAC,KAC5B,aAAa,OAAO,CAAC;gDACxB,YAAY,oBAAoB,mBAC/B,4TAAC;oDAAK,WAAU;;wDAA8B;wDAC1C;wDAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWpC,4TAAC,4HAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,4TAAC,4HAAA,CAAA,aAAU;wBAAC,WAAU;;0CACpB,4TAAC,4HAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,4TAAC,qSAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;oCAA0B;kDAEhD,4TAAC,6HAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAc,WAAU;kDAAU;;;;;;;;;;;;0CAInD,4TAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAKvC,4TAAC,4HAAA,CAAA,cAAW;;0CACV,4TAAC,qIAAA,CAAA,YAAS;gCACR,IAAG;gCACH,OAAM;gCACN,OAAO,OAAO,SAAS,EAAE;gCACzB,UAAU;0CAEV,cAAA,4TAAC,6HAAA,CAAA,QAAK;oCACJ,IAAG;oCACH,MAAK;oCACL,MAAK;oCACJ,GAAG,SAAS,aAAa;wCACxB,YAAY,CAAC;4CACX,IAAI,UAAU,MAAM,UAAU,QAAQ,UAAU,WAAW;gDACzD,OAAO;4CACT;4CACA,MAAM,WAAW,WAAW;4CAC5B,OAAO,MAAM,YAAY,YAAY;wCACvC;oCACF,EAAE;oCACF,aAAY;oCACZ,WAAU;;;;;;;;;;;4BAKb,cAAc,cAAc,cAAc,sCACzC,4TAAC;gCAAI,WAAU;;kDACb,4TAAC;wCAAI,WAAU;;0DACb,4TAAC,6SAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;0DACxB,4TAAC;gDAAK,WAAU;0DAA4B;;;;;;;;;;;;kDAI9C,4TAAC;wCAAI,WAAU;;0DACb,4TAAC;gDAAI,WAAU;;kEACb,4TAAC;kEAAK;;;;;;kEACN,4TAAC;wDAAK,WAAU;;4DACb,MAAM;4DAAa;4DACnB,CAAC,sBAAsB,SAAS,EAAE,OAAO,CAAC;;;;;;;;;;;;;0DAG/C,4TAAC;gDAAI,WAAU;;kEACb,4TAAC;kEAAK;;;;;;kEACN,4TAAC;wDACC,WAAW,CAAC,YAAY,EAAE,eAAe,IAAI,mBAAmB,gBAAgB;;4DAE/E;4DAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAU5B,4TAAC,4HAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,4TAAC,4HAAA,CAAA,aAAU;wBAAC,WAAU;;0CACpB,4TAAC,4HAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,4TAAC,6SAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;oCAAyB;kDAEjD,4TAAC,6HAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAY,WAAU;kDAAU;;;;;;;;;;;;0CAIjD,4TAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAIvC,4TAAC,4HAAA,CAAA,cAAW;kCACV,cAAA,4TAAC;4BAAI,WAAU;;8CACb,4TAAC;oCAAI,WAAU;;sDACb,4TAAC,8HAAA,CAAA,SAAM;4CACL,IAAG;4CACH,SAAS;4CACT,iBAAiB;;;;;;sDAEnB,4TAAC,6HAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAc,WAAU;sDAAwB;;;;;;;;;;;;gCAMhE,cAAc,iBAAiB,CAAC,0BAC/B,4TAAC;oCAAI,WAAU;;sDACb,4TAAC;4CAAG,WAAU;sDAAiC;;;;;;sDAG/C,4TAAC;4CAAI,WAAU;sDACZ,eAAe,GAAG,CAAC,CAAC,2BACnB,4TAAC,8HAAA,CAAA,SAAM;oDAEL,MAAK;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS,IAAM,mBAAmB;oDAClC,WAAU;;wDAET;wDAAW;;mDAPP;;;;;;;;;;sDAWX,4TAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;gCAM7C,0BACC,4TAAC;oCAAI,WAAU;;sDACb,4TAAC;4CAAI,WAAU;;8DACb,4TAAC,qIAAA,CAAA,YAAS;oDACR,IAAG;oDACH,OAAM;oDACN,OAAO,OAAO,SAAS,EAAE;oDACzB,UAAU;;sEAEV,4TAAC,6HAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,MAAK;4DACL,MAAK;4DACJ,GAAG,SAAS,aAAa;gEACxB,YAAY,CAAC;oEACX,IACE,UAAU,MACV,UAAU,QACV,UAAU,WACV;wEACA,OAAO;oEACT;oEACA,MAAM,WAAW,WAAW;oEAC5B,OAAO,MAAM,YAAY,YAAY;gEACvC;4DACF,EAAE;4DACF,aAAY;4DACZ,WAAU;;;;;;sEAEZ,4TAAC;4DAAE,WAAU;sEAA6B;;;;;;;;;;;;8DAK5C,4TAAC,qIAAA,CAAA,YAAS;oDACR,IAAG;oDACH,OAAM;oDACN,UAAU;8DAEV,cAAA,4TAAC,+HAAA,CAAA,UAAO;;0EACN,4TAAC,+HAAA,CAAA,iBAAc;gEAAC,OAAO;0EACrB,cAAA,4TAAC,8HAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,WAAU;;sFAEV,4TAAC,qSAAA,CAAA,eAAY;4EAAC,WAAU;;;;;;wEACvB,WACC,CAAA,GAAA,gNAAA,CAAA,SAAM,AAAD,EAAE,UAAU,uBAEjB,4TAAC;sFAAK;;;;;;;;;;;;;;;;;0EAIZ,4TAAC,+HAAA,CAAA,iBAAc;gEAAC,WAAU;0EACxB,cAAA,4TAAC,gIAAA,CAAA,WAAQ;oEACP,MAAK;oEACL,UAAU;oEACV,UAAU;oEACV,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAQtB,4TAAC;4CAAI,WAAU;;8DACb,4TAAC;oDAAI,WAAU;;sEACb,4TAAC;4DAAG,WAAU;sEAA2B;;;;;;sEAGzC,4TAAC,8HAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAQ;4DACR,MAAK;4DACL,SAAS;4DACT,WAAU;sEACX;;;;;;;;;;;;8DAIH,4TAAC;oDAAI,WAAU;8DACZ,eAAe,GAAG,CAAC,CAAC,2BACnB,4TAAC,8HAAA,CAAA,SAAM;4DAEL,MAAK;4DACL,SACE,sBAAsB,aAClB,YACA;4DAEN,MAAK;4DACL,SAAS,IAAM,mBAAmB;4DAClC,WAAW,CAAC,QAAQ,EAClB,sBAAsB,aAClB,0BACA,wCACJ;;gEAED;gEAAW;;2DAfP;;;;;;;;;;;;;;;;wCAsBZ,cAAc,iBAAiB,cAAc,4BAC5C,4TAAC;4CAAI,WAAU;;8DACb,4TAAC;oDAAI,WAAU;;sEACb,4TAAC,+RAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;sEACnB,4TAAC;4DAAK,WAAU;sEAA2B;;;;;;;;;;;;8DAI7C,4TAAC;oDAAI,WAAU;8DACZ,eAAe,0BACd;;0EACE,4TAAC;gEAAI,WAAU;;kFACb,4TAAC;kFAAK;;;;;;kFACN,4TAAC;wEAAK,WAAU;;4EACb,MAAM;4EAAY;4EAAE,aAAa,OAAO,CAAC;;;;;;;;;;;;;0EAG9C,4TAAC;gEAAI,WAAU;;kFACb,4TAAC;kFAAK;;;;;;kFACN,4TAAC;wEAAK,WAAU;;4EACb,MAAM;4EAAY;4EAAE,UAAU,OAAO,CAAC;;;;;;;;;;;;;0EAG3C,4TAAC;gEAAI,WAAU;;kFACb,4TAAC;kFAAK;;;;;;kFACN,4TAAC;wEAAK,WAAU;;4EACb,MAAM;4EAAY;4EAAE,QAAQ,OAAO,CAAC;;;;;;;;;;;;;0EAGzC,4TAAC;gEAAI,WAAU;0EACb,cAAA,4TAAC;oEAAK,WAAU;;wEACb;wEAAkB;;;;;;;;;;;;;uEAIvB,iBAAiB,0BACnB,4TAAC;wDAAI,WAAU;kEACb,cAAA,4TAAC;4DAAK,WAAU;sEAAc;;;;;;;;;;6EAKhC,4TAAC;wDAAI,WAAU;kEACb,cAAA,4TAAC;4DAAK,WAAU;sEAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAexD;GAtfa;KAAA", "debugId": null}}, {"offset": {"line": 3987, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/pages/products/add/SEOSection.tsx"], "sourcesContent": ["import {\r\n  BarChart3,\r\n  ExternalLink,\r\n  Eye,\r\n  Globe,\r\n  Hash,\r\n  MousePointer,\r\n  Search,\r\n  Star,\r\n  Target,\r\n  TrendingUp,\r\n  Users,\r\n  Zap,\r\n} from \"lucide-react\";\r\nimport {\r\n  FieldErrors,\r\n  UseFormRegister,\r\n  UseFormSetValue,\r\n  UseFormWatch,\r\n} from \"react-hook-form\";\r\n\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Label } from \"@/components/ui/label\";\r\nimport { Switch } from \"@/components/ui/switch\";\r\nimport { Textarea } from \"@/components/ui/textarea\";\r\nimport { ProductFormData } from \"@/schemas/productSchema\";\r\n\r\nimport { FormField } from \"../../../common/FormField\";\r\nimport { CollapsibleSection } from \"../../../ui/collapsible-section\";\r\n\r\ntype SEOSectionProps = {\r\n  register: UseFormRegister<ProductFormData>;\r\n  errors: FieldErrors<ProductFormData>;\r\n  setValue: UseFormSetValue<ProductFormData>;\r\n  watch: UseFormWatch<ProductFormData>;\r\n};\r\n\r\nexport const SEOSection: React.FC<SEOSectionProps> = ({\r\n  register,\r\n  errors,\r\n  setValue,\r\n  watch,\r\n}) => {\r\n  const metaTitle = watch(\"metaTitle\");\r\n  const metaDescription = watch(\"metaDescription\");\r\n  const focusKeyword = watch(\"focusKeyword\");\r\n\r\n  return (\r\n    <div className=\"space-y-8\">\r\n      {/* Section Header */}\r\n      <div className=\"flex items-center gap-3 border-b border-gray-200 pb-4\">\r\n        <div className=\"rounded-full bg-green-100 p-2\">\r\n          <Search className=\"h-5 w-5 text-green-600\" />\r\n        </div>\r\n        <div>\r\n          <h2 className=\"text-2xl font-bold text-gray-900\">SEO & Marketing</h2>\r\n          <p className=\"text-gray-600\">\r\n            Optimize your product for search engines and marketing\r\n          </p>\r\n        </div>\r\n      </div>\r\n\r\n      {/* SEO Meta Information - Collapsible */}\r\n      <CollapsibleSection\r\n        title=\"SEO Meta Information\"\r\n        description=\"Optimize how your product appears in search engine results\"\r\n        icon={<Search className=\"h-5 w-5 text-green-600\" />}\r\n        borderColor=\"border-l-green-500\"\r\n        defaultOpen={false}\r\n        isOptional={true}\r\n      >\r\n        <div className=\"space-y-4\">\r\n          {/* Meta Title */}\r\n          <FormField id=\"metaTitle\" label=\"Meta Title\" optional={true}>\r\n            <Input\r\n              id=\"metaTitle\"\r\n              {...register(\"metaTitle\")}\r\n              placeholder=\"SEO-optimized title for search engines\"\r\n              className=\"border-2 focus:border-green-500\"\r\n              maxLength={60}\r\n            />\r\n            <div className=\"mt-1 flex justify-between text-xs text-gray-500\">\r\n              <span>Recommended: 50-60 characters</span>\r\n              <span>{metaTitle?.length || 0}/60</span>\r\n            </div>\r\n          </FormField>\r\n\r\n          {/* Meta Description */}\r\n          <FormField\r\n            id=\"metaDescription\"\r\n            label=\"Meta Description\"\r\n            optional={true}\r\n          >\r\n            <Textarea\r\n              id=\"metaDescription\"\r\n              {...register(\"metaDescription\")}\r\n              placeholder=\"Brief description that appears in search results...\"\r\n              rows={3}\r\n              className=\"resize-none border-2 focus:border-green-500\"\r\n              maxLength={160}\r\n            />\r\n            <div className=\"mt-1 flex justify-between text-xs text-gray-500\">\r\n              <span>Recommended: 150-160 characters</span>\r\n              <span>{metaDescription?.length || 0}/160</span>\r\n            </div>\r\n          </FormField>\r\n\r\n          {/* Focus Keyword */}\r\n          <FormField id=\"focusKeyword\" label=\"Focus Keyword\" optional={true}>\r\n            <Input\r\n              id=\"focusKeyword\"\r\n              {...register(\"focusKeyword\")}\r\n              placeholder=\"Main keyword to target for SEO\"\r\n              className=\"border-2 focus:border-green-500\"\r\n            />\r\n          </FormField>\r\n\r\n          {/* SEO Preview */}\r\n          {(metaTitle || metaDescription) && (\r\n            <div className=\"mt-4 rounded-lg border border-green-200 bg-green-50 p-4\">\r\n              <h4 className=\"mb-2 font-medium text-green-900\">\r\n                Search Engine Preview\r\n              </h4>\r\n              <div className=\"rounded border bg-white p-3\">\r\n                <div className=\"cursor-pointer text-lg font-medium text-blue-600 hover:underline\">\r\n                  {metaTitle || \"Your Product Title\"}\r\n                </div>\r\n                <div className=\"text-sm text-green-700\">\r\n                  yourstore.com/products/product-name\r\n                </div>\r\n                <div className=\"mt-1 text-sm text-gray-600\">\r\n                  {metaDescription ||\r\n                    \"Your product description will appear here...\"}\r\n                </div>\r\n              </div>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </CollapsibleSection>\r\n\r\n      {/* Keywords & Tags - Collapsible */}\r\n      <CollapsibleSection\r\n        title=\"Keywords & Tags\"\r\n        description=\"Add relevant keywords and tags to improve discoverability\"\r\n        icon={<Hash className=\"h-5 w-5 text-blue-600\" />}\r\n        borderColor=\"border-l-blue-500\"\r\n        defaultOpen={false}\r\n        isOptional={true}\r\n      >\r\n        <div className=\"space-y-4\">\r\n          {/* SEO Keywords */}\r\n          <FormField id=\"seoKeywords\" label=\"SEO Keywords\" optional={true}>\r\n            <Input\r\n              id=\"seoKeywords\"\r\n              {...register(\"seoKeywords\")}\r\n              placeholder=\"keyword1, keyword2, keyword3\"\r\n              className=\"border-2 focus:border-blue-500\"\r\n            />\r\n            <p className=\"mt-1 text-xs text-gray-500\">\r\n              Separate keywords with commas. Focus on relevant, specific terms.\r\n            </p>\r\n          </FormField>\r\n\r\n          {/* Canonical URL */}\r\n          <FormField id=\"canonicalUrl\" label=\"Canonical URL\" optional={true}>\r\n            <Input\r\n              id=\"canonicalUrl\"\r\n              {...register(\"canonicalUrl\")}\r\n              placeholder=\"https://yourstore.com/products/product-name\"\r\n              className=\"border-2 focus:border-blue-500\"\r\n            />\r\n            <p className=\"mt-1 text-xs text-gray-500\">\r\n              Specify the preferred URL for this product to avoid duplicate\r\n              content issues.\r\n            </p>\r\n          </FormField>\r\n        </div>\r\n      </CollapsibleSection>\r\n\r\n      {/* Marketing Features Card */}\r\n      <Card className=\"border-l-4 border-l-purple-500\">\r\n        <CardHeader className=\"pb-3\">\r\n          <CardTitle className=\"flex items-center gap-2 text-lg\">\r\n            <Target className=\"h-5 w-5 text-purple-600\" />\r\n            Marketing Features\r\n            <Badge variant=\"secondary\" className=\"text-xs\">\r\n              Optional\r\n            </Badge>\r\n          </CardTitle>\r\n          <p className=\"text-sm text-gray-600\">\r\n            Enable special marketing features and promotions\r\n          </p>\r\n        </CardHeader>\r\n        <CardContent>\r\n          <div className=\"space-y-4\">\r\n            {/* Featured Product */}\r\n            <div className=\"flex items-center space-x-3\">\r\n              <Switch\r\n                id=\"featured\"\r\n                checked={watch(\"featured\")}\r\n                onCheckedChange={(checked) => setValue(\"featured\", checked)}\r\n              />\r\n              <Label htmlFor=\"featured\" className=\"text-base font-medium\">\r\n                Featured Product\r\n              </Label>\r\n            </div>\r\n\r\n            {/* Sticky Product */}\r\n            <div className=\"flex items-center space-x-3\">\r\n              <Switch\r\n                id=\"sticky\"\r\n                checked={watch(\"sticky\")}\r\n                onCheckedChange={(checked) => setValue(\"sticky\", checked)}\r\n              />\r\n              <Label htmlFor=\"sticky\" className=\"text-base font-medium\">\r\n                Sticky (Always show at top)\r\n              </Label>\r\n            </div>\r\n\r\n            {/* Marketing Description */}\r\n            {(watch(\"featured\") || watch(\"sticky\")) && (\r\n              <div className=\"rounded-lg border border-purple-200 bg-purple-50 p-3\">\r\n                <div className=\"flex items-start gap-2\">\r\n                  <Star className=\"mt-0.5 h-4 w-4 text-purple-600\" />\r\n                  <div className=\"text-sm text-purple-800\">\r\n                    <strong>Marketing Benefits:</strong>\r\n                    <ul className=\"mt-1 list-inside list-disc space-y-1\">\r\n                      {watch(\"featured\") && (\r\n                        <li>\r\n                          Featured products get priority placement in listings\r\n                        </li>\r\n                      )}\r\n                      {watch(\"sticky\") && (\r\n                        <li>\r\n                          Sticky products always appear at the top of search\r\n                          results\r\n                        </li>\r\n                      )}\r\n                    </ul>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n\r\n      {/* SEO Tips Card */}\r\n      <Card className=\"border-green-200 bg-gradient-to-r from-green-50 to-blue-50\">\r\n        <CardHeader>\r\n          <CardTitle className=\"flex items-center gap-2 text-green-800\">\r\n            <TrendingUp className=\"h-5 w-5\" />\r\n            SEO Best Practices\r\n          </CardTitle>\r\n        </CardHeader>\r\n        <CardContent>\r\n          <div className=\"grid gap-4 md:grid-cols-2\">\r\n            <div className=\"flex items-start gap-3\">\r\n              <div className=\"rounded-full bg-green-100 p-2\">\r\n                <Search className=\"h-4 w-4 text-green-600\" />\r\n              </div>\r\n              <div>\r\n                <h4 className=\"font-medium text-green-900\">Keyword Research</h4>\r\n                <p className=\"mt-1 text-sm text-green-700\">\r\n                  Use tools like Google Keyword Planner to find relevant,\r\n                  high-traffic keywords\r\n                </p>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"flex items-start gap-3\">\r\n              <div className=\"rounded-full bg-blue-100 p-2\">\r\n                <Eye className=\"h-4 w-4 text-blue-600\" />\r\n              </div>\r\n              <div>\r\n                <h4 className=\"font-medium text-blue-900\">User Intent</h4>\r\n                <p className=\"mt-1 text-sm text-blue-700\">\r\n                  Write titles and descriptions that match what customers are\r\n                  searching for\r\n                </p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAqBA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;;;;;;;;;;;AASO,MAAM,aAAwC,CAAC,EACpD,QAAQ,EACR,MAAM,EACN,QAAQ,EACR,KAAK,EACN;IACC,MAAM,YAAY,MAAM;IACxB,MAAM,kBAAkB,MAAM;IAC9B,MAAM,eAAe,MAAM;IAE3B,qBACE,4TAAC;QAAI,WAAU;;0BAEb,4TAAC;gBAAI,WAAU;;kCACb,4TAAC;wBAAI,WAAU;kCACb,cAAA,4TAAC,6RAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;;;;;;kCAEpB,4TAAC;;0CACC,4TAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,4TAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;;;;;;;0BAOjC,4TAAC,8IAAA,CAAA,qBAAkB;gBACjB,OAAM;gBACN,aAAY;gBACZ,oBAAM,4TAAC,6RAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;gBACxB,aAAY;gBACZ,aAAa;gBACb,YAAY;0BAEZ,cAAA,4TAAC;oBAAI,WAAU;;sCAEb,4TAAC,qIAAA,CAAA,YAAS;4BAAC,IAAG;4BAAY,OAAM;4BAAa,UAAU;;8CACrD,4TAAC,6HAAA,CAAA,QAAK;oCACJ,IAAG;oCACF,GAAG,SAAS,YAAY;oCACzB,aAAY;oCACZ,WAAU;oCACV,WAAW;;;;;;8CAEb,4TAAC;oCAAI,WAAU;;sDACb,4TAAC;sDAAK;;;;;;sDACN,4TAAC;;gDAAM,WAAW,UAAU;gDAAE;;;;;;;;;;;;;;;;;;;sCAKlC,4TAAC,qIAAA,CAAA,YAAS;4BACR,IAAG;4BACH,OAAM;4BACN,UAAU;;8CAEV,4TAAC,gIAAA,CAAA,WAAQ;oCACP,IAAG;oCACF,GAAG,SAAS,kBAAkB;oCAC/B,aAAY;oCACZ,MAAM;oCACN,WAAU;oCACV,WAAW;;;;;;8CAEb,4TAAC;oCAAI,WAAU;;sDACb,4TAAC;sDAAK;;;;;;sDACN,4TAAC;;gDAAM,iBAAiB,UAAU;gDAAE;;;;;;;;;;;;;;;;;;;sCAKxC,4TAAC,qIAAA,CAAA,YAAS;4BAAC,IAAG;4BAAe,OAAM;4BAAgB,UAAU;sCAC3D,cAAA,4TAAC,6HAAA,CAAA,QAAK;gCACJ,IAAG;gCACF,GAAG,SAAS,eAAe;gCAC5B,aAAY;gCACZ,WAAU;;;;;;;;;;;wBAKb,CAAC,aAAa,eAAe,mBAC5B,4TAAC;4BAAI,WAAU;;8CACb,4TAAC;oCAAG,WAAU;8CAAkC;;;;;;8CAGhD,4TAAC;oCAAI,WAAU;;sDACb,4TAAC;4CAAI,WAAU;sDACZ,aAAa;;;;;;sDAEhB,4TAAC;4CAAI,WAAU;sDAAyB;;;;;;sDAGxC,4TAAC;4CAAI,WAAU;sDACZ,mBACC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASd,4TAAC,8IAAA,CAAA,qBAAkB;gBACjB,OAAM;gBACN,aAAY;gBACZ,oBAAM,4TAAC,yRAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;gBACtB,aAAY;gBACZ,aAAa;gBACb,YAAY;0BAEZ,cAAA,4TAAC;oBAAI,WAAU;;sCAEb,4TAAC,qIAAA,CAAA,YAAS;4BAAC,IAAG;4BAAc,OAAM;4BAAe,UAAU;;8CACzD,4TAAC,6HAAA,CAAA,QAAK;oCACJ,IAAG;oCACF,GAAG,SAAS,cAAc;oCAC3B,aAAY;oCACZ,WAAU;;;;;;8CAEZ,4TAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;sCAM5C,4TAAC,qIAAA,CAAA,YAAS;4BAAC,IAAG;4BAAe,OAAM;4BAAgB,UAAU;;8CAC3D,4TAAC,6HAAA,CAAA,QAAK;oCACJ,IAAG;oCACF,GAAG,SAAS,eAAe;oCAC5B,aAAY;oCACZ,WAAU;;;;;;8CAEZ,4TAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;;;;;;;;;;;;0BAShD,4TAAC,4HAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,4TAAC,4HAAA,CAAA,aAAU;wBAAC,WAAU;;0CACpB,4TAAC,4HAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,4TAAC,6RAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAA4B;kDAE9C,4TAAC,6HAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAY,WAAU;kDAAU;;;;;;;;;;;;0CAIjD,4TAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAIvC,4TAAC,4HAAA,CAAA,cAAW;kCACV,cAAA,4TAAC;4BAAI,WAAU;;8CAEb,4TAAC;oCAAI,WAAU;;sDACb,4TAAC,8HAAA,CAAA,SAAM;4CACL,IAAG;4CACH,SAAS,MAAM;4CACf,iBAAiB,CAAC,UAAY,SAAS,YAAY;;;;;;sDAErD,4TAAC,6HAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAW,WAAU;sDAAwB;;;;;;;;;;;;8CAM9D,4TAAC;oCAAI,WAAU;;sDACb,4TAAC,8HAAA,CAAA,SAAM;4CACL,IAAG;4CACH,SAAS,MAAM;4CACf,iBAAiB,CAAC,UAAY,SAAS,UAAU;;;;;;sDAEnD,4TAAC,6HAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAS,WAAU;sDAAwB;;;;;;;;;;;;gCAM3D,CAAC,MAAM,eAAe,MAAM,SAAS,mBACpC,4TAAC;oCAAI,WAAU;8CACb,cAAA,4TAAC;wCAAI,WAAU;;0DACb,4TAAC,yRAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,4TAAC;gDAAI,WAAU;;kEACb,4TAAC;kEAAO;;;;;;kEACR,4TAAC;wDAAG,WAAU;;4DACX,MAAM,6BACL,4TAAC;0EAAG;;;;;;4DAIL,MAAM,2BACL,4TAAC;0EAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAetB,4TAAC,4HAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,4TAAC,4HAAA,CAAA,aAAU;kCACT,cAAA,4TAAC,4HAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,4TAAC,ySAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;;;;;kCAItC,4TAAC,4HAAA,CAAA,cAAW;kCACV,cAAA,4TAAC;4BAAI,WAAU;;8CACb,4TAAC;oCAAI,WAAU;;sDACb,4TAAC;4CAAI,WAAU;sDACb,cAAA,4TAAC,6RAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;sDAEpB,4TAAC;;8DACC,4TAAC;oDAAG,WAAU;8DAA6B;;;;;;8DAC3C,4TAAC;oDAAE,WAAU;8DAA8B;;;;;;;;;;;;;;;;;;8CAO/C,4TAAC;oCAAI,WAAU;;sDACb,4TAAC;4CAAI,WAAU;sDACb,cAAA,4TAAC,uRAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;;;;;;sDAEjB,4TAAC;;8DACC,4TAAC;oDAAG,WAAU;8DAA4B;;;;;;8DAC1C,4TAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW1D;KA1Pa", "debugId": null}}, {"offset": {"line": 4690, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/pages/products/add/ShippingSection.tsx"], "sourcesContent": ["import React from \"react\";\r\n\r\nimport {\r\n  Building,\r\n  Globe,\r\n  Home,\r\n  MapPin,\r\n  Navigation,\r\n  Package,\r\n  Ruler,\r\n  Truck,\r\n  Weight,\r\n} from \"lucide-react\";\r\nimport {\r\n  FieldErrors,\r\n  UseFormRegister,\r\n  UseFormSetValue,\r\n  UseFormWatch,\r\n} from \"react-hook-form\";\r\n\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\r\nimport { ImportantNotice } from \"@/components/ui/important-notice\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Label } from \"@/components/ui/label\";\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from \"@/components/ui/select\";\r\nimport { Switch } from \"@/components/ui/switch\";\r\nimport {\r\n  ProductFormData,\r\n  dimensionUnitValues,\r\n  shippingClassValues,\r\n  shippingTimeValues,\r\n  weightUnitValues,\r\n} from \"@/schemas/productSchema\";\r\n\r\nimport { FormField } from \"../../../common/FormField\";\r\nimport { CollapsibleSection } from \"../../../ui/collapsible-section\";\r\n\r\n// Helper function to format shipping time labels\r\nconst formatShippingTimeLabel = (timeValue: string): string => {\r\n  const formatMap: Record<string, string> = {\r\n    \"same-day\": \"Same Day Delivery\",\r\n    \"1-2-business-days\": \"1-2 Business Days\",\r\n    \"2-3-business-days\": \"2-3 Business Days\",\r\n    \"3-5-business-days\": \"3-5 Business Days\",\r\n    \"5-7-business-days\": \"5-7 Business Days\",\r\n    \"7-10-business-days\": \"7-10 Business Days\",\r\n    \"10-14-business-days\": \"10-14 Business Days\",\r\n    \"2-3-weeks\": \"2-3 Weeks\",\r\n    \"3-4-weeks\": \"3-4 Weeks\",\r\n    \"4-6-weeks\": \"4-6 Weeks\",\r\n  };\r\n  return formatMap[timeValue] || timeValue;\r\n};\r\n\r\ntype ShippingSectionProps = {\r\n  register: UseFormRegister<ProductFormData>;\r\n  errors: FieldErrors<ProductFormData>;\r\n  setValue: UseFormSetValue<ProductFormData>;\r\n  watch: UseFormWatch<ProductFormData>;\r\n};\r\n\r\n/**\r\n * Shipping section of the product form\r\n * Handles shipping location, cost, time, and free shipping option\r\n */\r\nexport const ShippingSection = ({\r\n  register,\r\n  errors,\r\n  setValue,\r\n  watch,\r\n}: ShippingSectionProps) => {\r\n  const freeShipping = watch(\"freeShipping\");\r\n  const requiresShipping = watch(\"requiresShipping\");\r\n  const productType = watch(\"productType\");\r\n\r\n  const handleFreeShippingChange = (checked: boolean) => {\r\n    setValue(\"freeShipping\", checked);\r\n    if (checked) {\r\n      setValue(\"shippingCost\", 0);\r\n    }\r\n  };\r\n\r\n  const handleRequiresShippingChange = (checked: boolean) => {\r\n    setValue(\"requiresShipping\", checked);\r\n    if (!checked) {\r\n      setValue(\"freeShipping\", false);\r\n      setValue(\"shippingCost\", 0);\r\n    }\r\n  };\r\n\r\n  // Auto-set requiresShipping based on product type\r\n  React.useEffect(() => {\r\n    if (productType === \"digital\" || productType === \"service\") {\r\n      setValue(\"requiresShipping\", false);\r\n    } else {\r\n      setValue(\"requiresShipping\", true);\r\n    }\r\n  }, [productType, setValue]);\r\n\r\n  return (\r\n    <div className=\"space-y-8\">\r\n      {/* Section Header */}\r\n      <div className=\"flex items-center gap-3 border-b border-gray-200 pb-4\">\r\n        <div className=\"rounded-full bg-blue-100 p-2\">\r\n          <Truck className=\"h-5 w-5 text-blue-600\" />\r\n        </div>\r\n        <div>\r\n          <h2 className=\"text-2xl font-bold text-gray-900\">\r\n            Shipping & Delivery\r\n          </h2>\r\n          <p className=\"text-gray-600\">\r\n            Configure shipping options and delivery details\r\n          </p>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Important Notice - Only show when shipping is required */}\r\n      {requiresShipping && (\r\n        <ImportantNotice\r\n          description=\"You must complete all required shipping information before proceeding to the next step.\"\r\n          requiredFields={\r\n            freeShipping\r\n              ? [\"Shipping Class\", \"Estimated Shipping Time\"]\r\n              : [\"Shipping Class\", \"Shipping Cost\", \"Estimated Shipping Time\"]\r\n          }\r\n          tip={\r\n            freeShipping\r\n              ? \"Shipping class and estimated delivery time help customers understand when to expect their orders.\"\r\n              : \"Shipping class, cost, and estimated delivery time help customers make informed purchasing decisions.\"\r\n          }\r\n          variant=\"blue\"\r\n        />\r\n      )}\r\n\r\n      {/* Shipping Requirements Card */}\r\n      <Card className=\"border-l-4 border-l-blue-500\">\r\n        <CardHeader className=\"pb-3\">\r\n          <CardTitle className=\"flex items-center gap-2 text-lg\">\r\n            <Package className=\"h-5 w-5 text-blue-600\" />\r\n            Shipping Requirements\r\n            <Badge variant=\"secondary\" className=\"text-xs\">\r\n              Optional\r\n            </Badge>\r\n          </CardTitle>\r\n          <p className=\"text-sm text-gray-600\">\r\n            Configure whether this product requires physical shipping\r\n          </p>\r\n        </CardHeader>\r\n        <CardContent>\r\n          <div className=\"space-y-4\">\r\n            <div className=\"flex items-center space-x-3\">\r\n              <Switch\r\n                id=\"requires-shipping-toggle\"\r\n                checked={requiresShipping}\r\n                onCheckedChange={handleRequiresShippingChange}\r\n                disabled={\r\n                  productType === \"digital\" || productType === \"service\"\r\n                }\r\n              />\r\n              <Label\r\n                htmlFor=\"requires-shipping-toggle\"\r\n                className=\"text-base font-medium\"\r\n              >\r\n                This product requires shipping\r\n              </Label>\r\n            </div>\r\n\r\n            {(productType === \"digital\" || productType === \"service\") && (\r\n              <div className=\"rounded-lg border border-green-200 bg-green-50 p-3\">\r\n                <div className=\"text-sm text-green-800\">\r\n                  <strong>Note:</strong>{\" \"}\r\n                  {productType === \"digital\" ? \"Digital products\" : \"Services\"}{\" \"}\r\n                  typically don&apos;t require physical shipping.\r\n                </div>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n\r\n      {requiresShipping && (\r\n        <>\r\n          {/* Shipping Class & Pricing Card */}\r\n          <Card className=\"border-l-4 border-l-purple-500\">\r\n            <CardHeader className=\"pb-3\">\r\n              <CardTitle className=\"flex items-center gap-2 text-lg\">\r\n                <Truck className=\"h-5 w-5 text-purple-600\" />\r\n                Shipping Class & Pricing\r\n                <Badge variant=\"destructive\" className=\"text-xs\">\r\n                  Required\r\n                </Badge>\r\n              </CardTitle>\r\n              <p className=\"text-sm text-gray-600\">\r\n                Configure shipping service level and costs\r\n              </p>\r\n            </CardHeader>\r\n            <CardContent>\r\n              <div className=\"space-y-4\">\r\n                {/* Shipping Class */}\r\n                <FormField\r\n                  id=\"shippingClass\"\r\n                  label=\"Shipping Class\"\r\n                  error={errors.shippingClass?.message}\r\n                  optional={false}\r\n                >\r\n                  <Select\r\n                    onValueChange={(value) =>\r\n                      setValue(\r\n                        \"shippingClass\",\r\n                        value as (typeof shippingClassValues)[number]\r\n                      )\r\n                    }\r\n                    value={watch(\"shippingClass\")}\r\n                  >\r\n                    <SelectTrigger className=\"border-2 focus:border-purple-500\">\r\n                      <SelectValue placeholder=\"Select shipping class...\" />\r\n                    </SelectTrigger>\r\n                    <SelectContent>\r\n                      {shippingClassValues.map((shippingClass) => (\r\n                        <SelectItem key={shippingClass} value={shippingClass}>\r\n                          <div className=\"flex items-center gap-2\">\r\n                            <Truck className=\"h-4 w-4\" />\r\n                            <span className=\"font-medium capitalize\">\r\n                              {shippingClass.replace(\"-\", \" \")}\r\n                            </span>\r\n                          </div>\r\n                        </SelectItem>\r\n                      ))}\r\n                    </SelectContent>\r\n                  </Select>\r\n                </FormField>\r\n\r\n                {/* Free Shipping Toggle */}\r\n                <div className=\"flex items-center space-x-3\">\r\n                  <Switch\r\n                    id=\"free-shipping-toggle\"\r\n                    checked={freeShipping}\r\n                    onCheckedChange={handleFreeShippingChange}\r\n                  />\r\n                  <Label\r\n                    htmlFor=\"free-shipping-toggle\"\r\n                    className=\"text-base font-medium\"\r\n                  >\r\n                    Free Shipping\r\n                  </Label>\r\n                </div>\r\n\r\n                {/* Shipping Cost & Time */}\r\n                <div className=\"grid gap-4 md:grid-cols-2\">\r\n                  {!freeShipping && (\r\n                    <FormField\r\n                      id=\"shippingCost\"\r\n                      label=\"Shipping Cost\"\r\n                      error={errors.shippingCost?.message}\r\n                      optional={false}\r\n                    >\r\n                      <Input\r\n                        id=\"shippingCost\"\r\n                        type=\"number\"\r\n                        step=\"0.01\"\r\n                        min=\"0\"\r\n                        {...register(\"shippingCost\", { valueAsNumber: true })}\r\n                        placeholder=\"0.00\"\r\n                        className=\"border-2 focus:border-purple-500\"\r\n                      />\r\n                    </FormField>\r\n                  )}\r\n\r\n                  <FormField\r\n                    id=\"shippingTime\"\r\n                    label=\"Estimated Shipping Time\"\r\n                    error={errors.shippingTime?.message}\r\n                    optional={false}\r\n                  >\r\n                    <Select\r\n                      onValueChange={(value) =>\r\n                        setValue(\r\n                          \"shippingTime\",\r\n                          value as (typeof shippingTimeValues)[number]\r\n                        )\r\n                      }\r\n                      value={watch(\"shippingTime\")}\r\n                    >\r\n                      <SelectTrigger className=\"border-2 focus:border-purple-500\">\r\n                        <SelectValue placeholder=\"Select delivery timeframe...\" />\r\n                      </SelectTrigger>\r\n                      <SelectContent>\r\n                        {shippingTimeValues.map((timeOption) => (\r\n                          <SelectItem key={timeOption} value={timeOption}>\r\n                            <div className=\"flex items-center gap-2\">\r\n                              <Navigation className=\"h-4 w-4\" />\r\n                              <span className=\"font-medium\">\r\n                                {formatShippingTimeLabel(timeOption)}\r\n                              </span>\r\n                            </div>\r\n                          </SelectItem>\r\n                        ))}\r\n                      </SelectContent>\r\n                    </Select>\r\n                  </FormField>\r\n                </div>\r\n              </div>\r\n            </CardContent>\r\n          </Card>\r\n\r\n          {/* Shipping Address - Collapsible */}\r\n          <CollapsibleSection\r\n            title=\"Shipping Origin Address\"\r\n            description=\"Where will this product ship from?\"\r\n            icon={<MapPin className=\"h-5 w-5 text-green-600\" />}\r\n            borderColor=\"border-l-green-500\"\r\n            defaultOpen={false}\r\n            isOptional={true}\r\n          >\r\n            <div className=\"space-y-4\">\r\n              {/* Street Address */}\r\n              <FormField\r\n                id=\"address.street\"\r\n                label=\"Street Address\"\r\n                optional={true}\r\n              >\r\n                <Input\r\n                  id=\"address.street\"\r\n                  {...register(\"address.street\")}\r\n                  placeholder=\"123 Main Street\"\r\n                  className=\"border-2 focus:border-green-500\"\r\n                />\r\n              </FormField>\r\n\r\n              {/* City, State, Postal Code */}\r\n              <div className=\"grid gap-4 md:grid-cols-3\">\r\n                <FormField id=\"address.city\" label=\"City\" optional={true}>\r\n                  <Input\r\n                    id=\"address.city\"\r\n                    {...register(\"address.city\")}\r\n                    placeholder=\"New York\"\r\n                    className=\"border-2 focus:border-green-500\"\r\n                  />\r\n                </FormField>\r\n\r\n                <FormField\r\n                  id=\"address.state\"\r\n                  label=\"State/Province\"\r\n                  optional={true}\r\n                >\r\n                  <Input\r\n                    id=\"address.state\"\r\n                    {...register(\"address.state\")}\r\n                    placeholder=\"NY\"\r\n                    className=\"border-2 focus:border-green-500\"\r\n                  />\r\n                </FormField>\r\n\r\n                <FormField\r\n                  id=\"address.postalCode\"\r\n                  label=\"Postal Code\"\r\n                  optional={true}\r\n                >\r\n                  <Input\r\n                    id=\"address.postalCode\"\r\n                    {...register(\"address.postalCode\")}\r\n                    placeholder=\"10001\"\r\n                    className=\"border-2 focus:border-green-500\"\r\n                  />\r\n                </FormField>\r\n              </div>\r\n\r\n              {/* Country */}\r\n              <FormField id=\"address.country\" label=\"Country\" optional={true}>\r\n                <Input\r\n                  id=\"address.country\"\r\n                  {...register(\"address.country\")}\r\n                  placeholder=\"United States\"\r\n                  className=\"border-2 focus:border-green-500\"\r\n                />\r\n              </FormField>\r\n\r\n              {/* Legacy Location Field */}\r\n              <FormField\r\n                id=\"location\"\r\n                label=\"Quick Location (Legacy)\"\r\n                error={errors.location?.message}\r\n                optional={true}\r\n              >\r\n                <Input\r\n                  id=\"location\"\r\n                  {...register(\"location\")}\r\n                  placeholder=\"e.g., New York, USA\"\r\n                  className=\"border-2 focus:border-green-500\"\r\n                />\r\n                <p className=\"mt-1 text-xs text-gray-500\">\r\n                  Simple location description (will be replaced by address\r\n                  above)\r\n                </p>\r\n              </FormField>\r\n            </div>\r\n          </CollapsibleSection>\r\n        </>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;AAAA;AAAA;AAAA;AAkBA;AACA;AACA;AACA;AACA;AACA;AAOA;AACA;AAQA;AACA;;;;;;;;;;;;;;;AAEA,iDAAiD;AACjD,MAAM,0BAA0B,CAAC;IAC/B,MAAM,YAAoC;QACxC,YAAY;QACZ,qBAAqB;QACrB,qBAAqB;QACrB,qBAAqB;QACrB,qBAAqB;QACrB,sBAAsB;QACtB,uBAAuB;QACvB,aAAa;QACb,aAAa;QACb,aAAa;IACf;IACA,OAAO,SAAS,CAAC,UAAU,IAAI;AACjC;AAaO,MAAM,kBAAkB,CAAC,EAC9B,QAAQ,EACR,MAAM,EACN,QAAQ,EACR,KAAK,EACgB;;IACrB,MAAM,eAAe,MAAM;IAC3B,MAAM,mBAAmB,MAAM;IAC/B,MAAM,cAAc,MAAM;IAE1B,MAAM,2BAA2B,CAAC;QAChC,SAAS,gBAAgB;QACzB,IAAI,SAAS;YACX,SAAS,gBAAgB;QAC3B;IACF;IAEA,MAAM,+BAA+B,CAAC;QACpC,SAAS,oBAAoB;QAC7B,IAAI,CAAC,SAAS;YACZ,SAAS,gBAAgB;YACzB,SAAS,gBAAgB;QAC3B;IACF;IAEA,kDAAkD;IAClD,4RAAA,CAAA,UAAK,CAAC,SAAS;qCAAC;YACd,IAAI,gBAAgB,aAAa,gBAAgB,WAAW;gBAC1D,SAAS,oBAAoB;YAC/B,OAAO;gBACL,SAAS,oBAAoB;YAC/B;QACF;oCAAG;QAAC;QAAa;KAAS;IAE1B,qBACE,4TAAC;QAAI,WAAU;;0BAEb,4TAAC;gBAAI,WAAU;;kCACb,4TAAC;wBAAI,WAAU;kCACb,cAAA,4TAAC,2RAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;;;;;;kCAEnB,4TAAC;;0CACC,4TAAC;gCAAG,WAAU;0CAAmC;;;;;;0CAGjD,4TAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;;;;;;;YAOhC,kCACC,4TAAC,2IAAA,CAAA,kBAAe;gBACd,aAAY;gBACZ,gBACE,eACI;oBAAC;oBAAkB;iBAA0B,GAC7C;oBAAC;oBAAkB;oBAAiB;iBAA0B;gBAEpE,KACE,eACI,sGACA;gBAEN,SAAQ;;;;;;0BAKZ,4TAAC,4HAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,4TAAC,4HAAA,CAAA,aAAU;wBAAC,WAAU;;0CACpB,4TAAC,4HAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,4TAAC,+RAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAA0B;kDAE7C,4TAAC,6HAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAY,WAAU;kDAAU;;;;;;;;;;;;0CAIjD,4TAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAIvC,4TAAC,4HAAA,CAAA,cAAW;kCACV,cAAA,4TAAC;4BAAI,WAAU;;8CACb,4TAAC;oCAAI,WAAU;;sDACb,4TAAC,8HAAA,CAAA,SAAM;4CACL,IAAG;4CACH,SAAS;4CACT,iBAAiB;4CACjB,UACE,gBAAgB,aAAa,gBAAgB;;;;;;sDAGjD,4TAAC,6HAAA,CAAA,QAAK;4CACJ,SAAQ;4CACR,WAAU;sDACX;;;;;;;;;;;;gCAKF,CAAC,gBAAgB,aAAa,gBAAgB,SAAS,mBACtD,4TAAC;oCAAI,WAAU;8CACb,cAAA,4TAAC;wCAAI,WAAU;;0DACb,4TAAC;0DAAO;;;;;;4CAAe;4CACtB,gBAAgB,YAAY,qBAAqB;4CAAY;4CAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAS7E,kCACC;;kCAEE,4TAAC,4HAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,4TAAC,4HAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,4TAAC,4HAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,4TAAC,2RAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAA4B;0DAE7C,4TAAC,6HAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAc,WAAU;0DAAU;;;;;;;;;;;;kDAInD,4TAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAIvC,4TAAC,4HAAA,CAAA,cAAW;0CACV,cAAA,4TAAC;oCAAI,WAAU;;sDAEb,4TAAC,qIAAA,CAAA,YAAS;4CACR,IAAG;4CACH,OAAM;4CACN,OAAO,OAAO,aAAa,EAAE;4CAC7B,UAAU;sDAEV,cAAA,4TAAC,8HAAA,CAAA,SAAM;gDACL,eAAe,CAAC,QACd,SACE,iBACA;gDAGJ,OAAO,MAAM;;kEAEb,4TAAC,8HAAA,CAAA,gBAAa;wDAAC,WAAU;kEACvB,cAAA,4TAAC,8HAAA,CAAA,cAAW;4DAAC,aAAY;;;;;;;;;;;kEAE3B,4TAAC,8HAAA,CAAA,gBAAa;kEACX,2HAAA,CAAA,sBAAmB,CAAC,GAAG,CAAC,CAAC,8BACxB,4TAAC,8HAAA,CAAA,aAAU;gEAAqB,OAAO;0EACrC,cAAA,4TAAC;oEAAI,WAAU;;sFACb,4TAAC,2RAAA,CAAA,QAAK;4EAAC,WAAU;;;;;;sFACjB,4TAAC;4EAAK,WAAU;sFACb,cAAc,OAAO,CAAC,KAAK;;;;;;;;;;;;+DAJjB;;;;;;;;;;;;;;;;;;;;;sDAczB,4TAAC;4CAAI,WAAU;;8DACb,4TAAC,8HAAA,CAAA,SAAM;oDACL,IAAG;oDACH,SAAS;oDACT,iBAAiB;;;;;;8DAEnB,4TAAC,6HAAA,CAAA,QAAK;oDACJ,SAAQ;oDACR,WAAU;8DACX;;;;;;;;;;;;sDAMH,4TAAC;4CAAI,WAAU;;gDACZ,CAAC,8BACA,4TAAC,qIAAA,CAAA,YAAS;oDACR,IAAG;oDACH,OAAM;oDACN,OAAO,OAAO,YAAY,EAAE;oDAC5B,UAAU;8DAEV,cAAA,4TAAC,6HAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,MAAK;wDACL,MAAK;wDACL,KAAI;wDACH,GAAG,SAAS,gBAAgB;4DAAE,eAAe;wDAAK,EAAE;wDACrD,aAAY;wDACZ,WAAU;;;;;;;;;;;8DAKhB,4TAAC,qIAAA,CAAA,YAAS;oDACR,IAAG;oDACH,OAAM;oDACN,OAAO,OAAO,YAAY,EAAE;oDAC5B,UAAU;8DAEV,cAAA,4TAAC,8HAAA,CAAA,SAAM;wDACL,eAAe,CAAC,QACd,SACE,gBACA;wDAGJ,OAAO,MAAM;;0EAEb,4TAAC,8HAAA,CAAA,gBAAa;gEAAC,WAAU;0EACvB,cAAA,4TAAC,8HAAA,CAAA,cAAW;oEAAC,aAAY;;;;;;;;;;;0EAE3B,4TAAC,8HAAA,CAAA,gBAAa;0EACX,2HAAA,CAAA,qBAAkB,CAAC,GAAG,CAAC,CAAC,2BACvB,4TAAC,8HAAA,CAAA,aAAU;wEAAkB,OAAO;kFAClC,cAAA,4TAAC;4EAAI,WAAU;;8FACb,4TAAC,qSAAA,CAAA,aAAU;oFAAC,WAAU;;;;;;8FACtB,4TAAC;oFAAK,WAAU;8FACb,wBAAwB;;;;;;;;;;;;uEAJd;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAkBjC,4TAAC,8IAAA,CAAA,qBAAkB;wBACjB,OAAM;wBACN,aAAY;wBACZ,oBAAM,4TAAC,iSAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;wBACxB,aAAY;wBACZ,aAAa;wBACb,YAAY;kCAEZ,cAAA,4TAAC;4BAAI,WAAU;;8CAEb,4TAAC,qIAAA,CAAA,YAAS;oCACR,IAAG;oCACH,OAAM;oCACN,UAAU;8CAEV,cAAA,4TAAC,6HAAA,CAAA,QAAK;wCACJ,IAAG;wCACF,GAAG,SAAS,iBAAiB;wCAC9B,aAAY;wCACZ,WAAU;;;;;;;;;;;8CAKd,4TAAC;oCAAI,WAAU;;sDACb,4TAAC,qIAAA,CAAA,YAAS;4CAAC,IAAG;4CAAe,OAAM;4CAAO,UAAU;sDAClD,cAAA,4TAAC,6HAAA,CAAA,QAAK;gDACJ,IAAG;gDACF,GAAG,SAAS,eAAe;gDAC5B,aAAY;gDACZ,WAAU;;;;;;;;;;;sDAId,4TAAC,qIAAA,CAAA,YAAS;4CACR,IAAG;4CACH,OAAM;4CACN,UAAU;sDAEV,cAAA,4TAAC,6HAAA,CAAA,QAAK;gDACJ,IAAG;gDACF,GAAG,SAAS,gBAAgB;gDAC7B,aAAY;gDACZ,WAAU;;;;;;;;;;;sDAId,4TAAC,qIAAA,CAAA,YAAS;4CACR,IAAG;4CACH,OAAM;4CACN,UAAU;sDAEV,cAAA,4TAAC,6HAAA,CAAA,QAAK;gDACJ,IAAG;gDACF,GAAG,SAAS,qBAAqB;gDAClC,aAAY;gDACZ,WAAU;;;;;;;;;;;;;;;;;8CAMhB,4TAAC,qIAAA,CAAA,YAAS;oCAAC,IAAG;oCAAkB,OAAM;oCAAU,UAAU;8CACxD,cAAA,4TAAC,6HAAA,CAAA,QAAK;wCACJ,IAAG;wCACF,GAAG,SAAS,kBAAkB;wCAC/B,aAAY;wCACZ,WAAU;;;;;;;;;;;8CAKd,4TAAC,qIAAA,CAAA,YAAS;oCACR,IAAG;oCACH,OAAM;oCACN,OAAO,OAAO,QAAQ,EAAE;oCACxB,UAAU;;sDAEV,4TAAC,6HAAA,CAAA,QAAK;4CACJ,IAAG;4CACF,GAAG,SAAS,WAAW;4CACxB,aAAY;4CACZ,WAAU;;;;;;sDAEZ,4TAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW1D;GAhVa;KAAA", "debugId": null}}, {"offset": {"line": 5414, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/pages/products/add/WarrantySection.tsx"], "sourcesContent": ["import {\r\n  AlertCircle,\r\n  Calendar,\r\n  CheckCircle,\r\n  Clock,\r\n  CreditCard,\r\n  FileText,\r\n  Mail,\r\n  Package,\r\n  Phone,\r\n  RotateCcw,\r\n  Shield,\r\n  Truck,\r\n} from \"lucide-react\";\r\nimport {\r\n  FieldErrors,\r\n  UseFormRegister,\r\n  UseFormSetValue,\r\n  UseFormWatch,\r\n} from \"react-hook-form\";\r\n\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Label } from \"@/components/ui/label\";\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from \"@/components/ui/select\";\r\nimport { Switch } from \"@/components/ui/switch\";\r\nimport { Textarea } from \"@/components/ui/textarea\";\r\nimport { ProductFormData } from \"@/schemas/productSchema\";\r\n\r\nimport { FormField } from \"../../../common/FormField\";\r\nimport { CollapsibleSection } from \"../../../ui/collapsible-section\";\r\n\r\ntype WarrantySectionProps = {\r\n  register: UseFormRegister<ProductFormData>;\r\n  errors: FieldErrors<ProductFormData>;\r\n  setValue: UseFormSetValue<ProductFormData>;\r\n  watch: UseFormWatch<ProductFormData>;\r\n};\r\n\r\nconst warrantyPeriods = [\r\n  { value: \"30-days\", label: \"30 Days\" },\r\n  { value: \"90-days\", label: \"90 Days\" },\r\n  { value: \"6-months\", label: \"6 Months\" },\r\n  { value: \"1-year\", label: \"1 Year\" },\r\n  { value: \"2-years\", label: \"2 Years\" },\r\n  { value: \"3-years\", label: \"3 Years\" },\r\n  { value: \"5-years\", label: \"5 Years\" },\r\n  { value: \"lifetime\", label: \"Lifetime\" },\r\n  { value: \"no-warranty\", label: \"No Warranty\" },\r\n];\r\n\r\nconst returnPeriods = [\r\n  { value: \"7-days\", label: \"7 Days\" },\r\n  { value: \"14-days\", label: \"14 Days\" },\r\n  { value: \"30-days\", label: \"30 Days\" },\r\n  { value: \"60-days\", label: \"60 Days\" },\r\n  { value: \"90-days\", label: \"90 Days\" },\r\n  { value: \"no-returns\", label: \"No Returns\" },\r\n];\r\n\r\nexport const WarrantySection: React.FC<WarrantySectionProps> = ({\r\n  register,\r\n  errors,\r\n  setValue,\r\n  watch,\r\n}) => {\r\n  const warrantyPeriod = watch(\"warrantyPeriod\");\r\n  const returnPeriod = watch(\"returnPeriod\");\r\n  const hasWarranty = warrantyPeriod && warrantyPeriod !== \"no-warranty\";\r\n  const allowsReturns = returnPeriod && returnPeriod !== \"no-returns\";\r\n\r\n  return (\r\n    <div className=\"space-y-8\">\r\n      {/* Section Header */}\r\n      <div className=\"flex items-center gap-3 border-b border-gray-200 pb-4\">\r\n        <div className=\"rounded-full bg-blue-100 p-2\">\r\n          <Shield className=\"h-5 w-5 text-blue-600\" />\r\n        </div>\r\n        <div>\r\n          <h2 className=\"text-2xl font-bold text-gray-900\">\r\n            Warranty & Returns\r\n          </h2>\r\n          <p className=\"text-gray-600\">\r\n            Configure warranty coverage and return policies\r\n          </p>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Warranty Information Card */}\r\n      <Card className=\"border-l-4 border-l-blue-500\">\r\n        <CardHeader className=\"pb-3\">\r\n          <CardTitle className=\"flex items-center gap-2 text-lg\">\r\n            <Shield className=\"h-5 w-5 text-blue-600\" />\r\n            Warranty Coverage\r\n            <Badge variant=\"secondary\" className=\"text-xs\">\r\n              Optional\r\n            </Badge>\r\n          </CardTitle>\r\n          <p className=\"text-sm text-gray-600\">\r\n            Define warranty terms and coverage for this product\r\n          </p>\r\n        </CardHeader>\r\n        <CardContent>\r\n          <div className=\"space-y-4\">\r\n            {/* Warranty Period */}\r\n            <FormField\r\n              id=\"warrantyPeriod\"\r\n              label=\"Warranty Period\"\r\n              optional={true}\r\n            >\r\n              <Select\r\n                onValueChange={(value) => setValue(\"warrantyPeriod\", value)}\r\n                value={warrantyPeriod}\r\n              >\r\n                <SelectTrigger className=\"border-2 focus:border-blue-500\">\r\n                  <SelectValue placeholder=\"Select warranty period...\" />\r\n                </SelectTrigger>\r\n                <SelectContent>\r\n                  {warrantyPeriods.map((period) => (\r\n                    <SelectItem key={period.value} value={period.value}>\r\n                      <div className=\"flex items-center gap-2\">\r\n                        {period.value === \"no-warranty\" ? (\r\n                          <AlertCircle className=\"h-4 w-4 text-red-500\" />\r\n                        ) : (\r\n                          <Shield className=\"h-4 w-4 text-blue-500\" />\r\n                        )}\r\n                        <span className=\"font-medium\">{period.label}</span>\r\n                      </div>\r\n                    </SelectItem>\r\n                  ))}\r\n                </SelectContent>\r\n              </Select>\r\n            </FormField>\r\n\r\n            {/* Warranty Terms */}\r\n            {hasWarranty && (\r\n              <FormField\r\n                id=\"warrantyTerms\"\r\n                label=\"Warranty Terms & Conditions\"\r\n                optional={true}\r\n              >\r\n                <Textarea\r\n                  id=\"warrantyTerms\"\r\n                  {...register(\"warrantyTerms\")}\r\n                  placeholder=\"Describe what is covered under warranty, exclusions, and claim process...\"\r\n                  rows={4}\r\n                  className=\"resize-none border-2 focus:border-blue-500\"\r\n                />\r\n              </FormField>\r\n            )}\r\n\r\n            {/* Warranty Status Display */}\r\n            {warrantyPeriod && (\r\n              <div\r\n                className={`rounded-lg border p-3 ${\r\n                  hasWarranty\r\n                    ? \"border-blue-200 bg-blue-50\"\r\n                    : \"border-gray-200 bg-gray-50\"\r\n                }`}\r\n              >\r\n                <div className=\"flex items-center gap-2\">\r\n                  {hasWarranty ? (\r\n                    <CheckCircle className=\"h-4 w-4 text-blue-600\" />\r\n                  ) : (\r\n                    <AlertCircle className=\"h-4 w-4 text-gray-600\" />\r\n                  )}\r\n                  <span\r\n                    className={`font-medium ${\r\n                      hasWarranty ? \"text-blue-900\" : \"text-gray-900\"\r\n                    }`}\r\n                  >\r\n                    {hasWarranty\r\n                      ? `Warranty Coverage: ${warrantyPeriods.find((p) => p.value === warrantyPeriod)?.label}`\r\n                      : \"No Warranty Coverage\"}\r\n                  </span>\r\n                </div>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n\r\n      {/* Return Policy Card */}\r\n      <Card className=\"border-l-4 border-l-green-500\">\r\n        <CardHeader className=\"pb-3\">\r\n          <CardTitle className=\"flex items-center gap-2 text-lg\">\r\n            <RotateCcw className=\"h-5 w-5 text-green-600\" />\r\n            Return Policy\r\n            <Badge variant=\"secondary\" className=\"text-xs\">\r\n              Optional\r\n            </Badge>\r\n          </CardTitle>\r\n          <p className=\"text-sm text-gray-600\">\r\n            Set return window and conditions for customer returns\r\n          </p>\r\n        </CardHeader>\r\n        <CardContent>\r\n          <div className=\"space-y-4\">\r\n            {/* Return Period */}\r\n            <FormField id=\"returnPeriod\" label=\"Return Period\" optional={true}>\r\n              <Select\r\n                onValueChange={(value) => setValue(\"returnPeriod\", value)}\r\n                value={returnPeriod}\r\n              >\r\n                <SelectTrigger className=\"border-2 focus:border-green-500\">\r\n                  <SelectValue placeholder=\"Select return period...\" />\r\n                </SelectTrigger>\r\n                <SelectContent>\r\n                  {returnPeriods.map((period) => (\r\n                    <SelectItem key={period.value} value={period.value}>\r\n                      <div className=\"flex items-center gap-2\">\r\n                        {period.value === \"no-returns\" ? (\r\n                          <AlertCircle className=\"h-4 w-4 text-red-500\" />\r\n                        ) : (\r\n                          <RotateCcw className=\"h-4 w-4 text-green-500\" />\r\n                        )}\r\n                        <span className=\"font-medium\">{period.label}</span>\r\n                      </div>\r\n                    </SelectItem>\r\n                  ))}\r\n                </SelectContent>\r\n              </Select>\r\n            </FormField>\r\n\r\n            {/* Return Policy Details */}\r\n            {allowsReturns && (\r\n              <FormField\r\n                id=\"returnPolicy\"\r\n                label=\"Return Policy Details\"\r\n                optional={true}\r\n              >\r\n                <Textarea\r\n                  id=\"returnPolicy\"\r\n                  {...register(\"returnPolicy\")}\r\n                  placeholder=\"Describe return conditions, who pays shipping, refund process, item condition requirements...\"\r\n                  rows={4}\r\n                  className=\"resize-none border-2 focus:border-green-500\"\r\n                />\r\n              </FormField>\r\n            )}\r\n\r\n            {/* Return Status Display */}\r\n            {returnPeriod && (\r\n              <div\r\n                className={`rounded-lg border p-3 ${\r\n                  allowsReturns\r\n                    ? \"border-green-200 bg-green-50\"\r\n                    : \"border-gray-200 bg-gray-50\"\r\n                }`}\r\n              >\r\n                <div className=\"flex items-center gap-2\">\r\n                  {allowsReturns ? (\r\n                    <CheckCircle className=\"h-4 w-4 text-green-600\" />\r\n                  ) : (\r\n                    <AlertCircle className=\"h-4 w-4 text-gray-600\" />\r\n                  )}\r\n                  <span\r\n                    className={`font-medium ${\r\n                      allowsReturns ? \"text-green-900\" : \"text-gray-900\"\r\n                    }`}\r\n                  >\r\n                    {allowsReturns\r\n                      ? `Returns Accepted: ${returnPeriods.find((p) => p.value === returnPeriod)?.label} window`\r\n                      : \"No Returns Accepted\"}\r\n                  </span>\r\n                </div>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n\r\n      {/* Customer Support - Collapsible */}\r\n      <CollapsibleSection\r\n        title=\"Customer Support\"\r\n        description=\"Provide support contact information for warranty and returns\"\r\n        icon={<Phone className=\"h-5 w-5 text-purple-600\" />}\r\n        borderColor=\"border-l-purple-500\"\r\n        defaultOpen={false}\r\n        isOptional={true}\r\n      >\r\n        <div className=\"space-y-4\">\r\n          {/* Support Email */}\r\n          <FormField id=\"supportEmail\" label=\"Support Email\" optional={true}>\r\n            <Input\r\n              id=\"supportEmail\"\r\n              type=\"email\"\r\n              {...register(\"supportEmail\")}\r\n              placeholder=\"<EMAIL>\"\r\n              className=\"border-2 focus:border-purple-500\"\r\n            />\r\n          </FormField>\r\n\r\n          {/* Support Phone */}\r\n          <FormField id=\"supportPhone\" label=\"Support Phone\" optional={true}>\r\n            <Input\r\n              id=\"supportPhone\"\r\n              {...register(\"supportPhone\")}\r\n              placeholder=\"+****************\"\r\n              className=\"border-2 focus:border-purple-500\"\r\n            />\r\n          </FormField>\r\n\r\n          {/* Support Hours */}\r\n          <FormField id=\"supportHours\" label=\"Support Hours\" optional={true}>\r\n            <Input\r\n              id=\"supportHours\"\r\n              {...register(\"supportHours\")}\r\n              placeholder=\"Monday-Friday 9AM-5PM EST\"\r\n              className=\"border-2 focus:border-purple-500\"\r\n            />\r\n          </FormField>\r\n        </div>\r\n      </CollapsibleSection>\r\n\r\n      {/* Policy Summary Card */}\r\n      <Card className=\"border-blue-200 bg-gradient-to-r from-blue-50 to-green-50\">\r\n        <CardHeader>\r\n          <CardTitle className=\"flex items-center gap-2 text-blue-800\">\r\n            <FileText className=\"h-5 w-5\" />\r\n            Policy Summary\r\n          </CardTitle>\r\n        </CardHeader>\r\n        <CardContent>\r\n          <div className=\"grid gap-4 md:grid-cols-2\">\r\n            <div className=\"flex items-center gap-3\">\r\n              <div className=\"rounded-full bg-blue-100 p-2\">\r\n                <Shield className=\"h-4 w-4 text-blue-600\" />\r\n              </div>\r\n              <div>\r\n                <h4 className=\"font-medium text-blue-900\">Warranty</h4>\r\n                <p className=\"text-sm text-blue-700\">\r\n                  {hasWarranty\r\n                    ? `${warrantyPeriods.find((p) => p.value === warrantyPeriod)?.label} coverage`\r\n                    : \"No warranty\"}\r\n                </p>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"flex items-center gap-3\">\r\n              <div className=\"rounded-full bg-green-100 p-2\">\r\n                <RotateCcw className=\"h-4 w-4 text-green-600\" />\r\n              </div>\r\n              <div>\r\n                <h4 className=\"font-medium text-green-900\">Returns</h4>\r\n                <p className=\"text-sm text-green-700\">\r\n                  {allowsReturns\r\n                    ? `${returnPeriods.find((p) => p.value === returnPeriod)?.label} return window`\r\n                    : \"No returns\"}\r\n                </p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAqBA;AACA;AACA;AAEA;AAQA;AAGA;AACA;;;;;;;;;;AASA,MAAM,kBAAkB;IACtB;QAAE,OAAO;QAAW,OAAO;IAAU;IACrC;QAAE,OAAO;QAAW,OAAO;IAAU;IACrC;QAAE,OAAO;QAAY,OAAO;IAAW;IACvC;QAAE,OAAO;QAAU,OAAO;IAAS;IACnC;QAAE,OAAO;QAAW,OAAO;IAAU;IACrC;QAAE,OAAO;QAAW,OAAO;IAAU;IACrC;QAAE,OAAO;QAAW,OAAO;IAAU;IACrC;QAAE,OAAO;QAAY,OAAO;IAAW;IACvC;QAAE,OAAO;QAAe,OAAO;IAAc;CAC9C;AAED,MAAM,gBAAgB;IACpB;QAAE,OAAO;QAAU,OAAO;IAAS;IACnC;QAAE,OAAO;QAAW,OAAO;IAAU;IACrC;QAAE,OAAO;QAAW,OAAO;IAAU;IACrC;QAAE,OAAO;QAAW,OAAO;IAAU;IACrC;QAAE,OAAO;QAAW,OAAO;IAAU;IACrC;QAAE,OAAO;QAAc,OAAO;IAAa;CAC5C;AAEM,MAAM,kBAAkD,CAAC,EAC9D,QAAQ,EACR,MAAM,EACN,QAAQ,EACR,KAAK,EACN;IACC,MAAM,iBAAiB,MAAM;IAC7B,MAAM,eAAe,MAAM;IAC3B,MAAM,cAAc,kBAAkB,mBAAmB;IACzD,MAAM,gBAAgB,gBAAgB,iBAAiB;IAEvD,qBACE,4TAAC;QAAI,WAAU;;0BAEb,4TAAC;gBAAI,WAAU;;kCACb,4TAAC;wBAAI,WAAU;kCACb,cAAA,4TAAC,6RAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;;;;;;kCAEpB,4TAAC;;0CACC,4TAAC;gCAAG,WAAU;0CAAmC;;;;;;0CAGjD,4TAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;;;;;;;0BAOjC,4TAAC,4HAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,4TAAC,4HAAA,CAAA,aAAU;wBAAC,WAAU;;0CACpB,4TAAC,4HAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,4TAAC,6RAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAA0B;kDAE5C,4TAAC,6HAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAY,WAAU;kDAAU;;;;;;;;;;;;0CAIjD,4TAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAIvC,4TAAC,4HAAA,CAAA,cAAW;kCACV,cAAA,4TAAC;4BAAI,WAAU;;8CAEb,4TAAC,qIAAA,CAAA,YAAS;oCACR,IAAG;oCACH,OAAM;oCACN,UAAU;8CAEV,cAAA,4TAAC,8HAAA,CAAA,SAAM;wCACL,eAAe,CAAC,QAAU,SAAS,kBAAkB;wCACrD,OAAO;;0DAEP,4TAAC,8HAAA,CAAA,gBAAa;gDAAC,WAAU;0DACvB,cAAA,4TAAC,8HAAA,CAAA,cAAW;oDAAC,aAAY;;;;;;;;;;;0DAE3B,4TAAC,8HAAA,CAAA,gBAAa;0DACX,gBAAgB,GAAG,CAAC,CAAC,uBACpB,4TAAC,8HAAA,CAAA,aAAU;wDAAoB,OAAO,OAAO,KAAK;kEAChD,cAAA,4TAAC;4DAAI,WAAU;;gEACZ,OAAO,KAAK,KAAK,8BAChB,4TAAC,2SAAA,CAAA,cAAW;oEAAC,WAAU;;;;;yFAEvB,4TAAC,6RAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;8EAEpB,4TAAC;oEAAK,WAAU;8EAAe,OAAO,KAAK;;;;;;;;;;;;uDAP9B,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;gCAgBpC,6BACC,4TAAC,qIAAA,CAAA,YAAS;oCACR,IAAG;oCACH,OAAM;oCACN,UAAU;8CAEV,cAAA,4TAAC,gIAAA,CAAA,WAAQ;wCACP,IAAG;wCACF,GAAG,SAAS,gBAAgB;wCAC7B,aAAY;wCACZ,MAAM;wCACN,WAAU;;;;;;;;;;;gCAMf,gCACC,4TAAC;oCACC,WAAW,CAAC,sBAAsB,EAChC,cACI,+BACA,8BACJ;8CAEF,cAAA,4TAAC;wCAAI,WAAU;;4CACZ,4BACC,4TAAC,kTAAA,CAAA,cAAW;gDAAC,WAAU;;;;;qEAEvB,4TAAC,2SAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;0DAEzB,4TAAC;gDACC,WAAW,CAAC,YAAY,EACtB,cAAc,kBAAkB,iBAChC;0DAED,cACG,CAAC,mBAAmB,EAAE,gBAAgB,IAAI,CAAC,CAAC,IAAM,EAAE,KAAK,KAAK,iBAAiB,OAAO,GACtF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUlB,4TAAC,4HAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,4TAAC,4HAAA,CAAA,aAAU;wBAAC,WAAU;;0CACpB,4TAAC,4HAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,4TAAC,uSAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAA2B;kDAEhD,4TAAC,6HAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAY,WAAU;kDAAU;;;;;;;;;;;;0CAIjD,4TAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAIvC,4TAAC,4HAAA,CAAA,cAAW;kCACV,cAAA,4TAAC;4BAAI,WAAU;;8CAEb,4TAAC,qIAAA,CAAA,YAAS;oCAAC,IAAG;oCAAe,OAAM;oCAAgB,UAAU;8CAC3D,cAAA,4TAAC,8HAAA,CAAA,SAAM;wCACL,eAAe,CAAC,QAAU,SAAS,gBAAgB;wCACnD,OAAO;;0DAEP,4TAAC,8HAAA,CAAA,gBAAa;gDAAC,WAAU;0DACvB,cAAA,4TAAC,8HAAA,CAAA,cAAW;oDAAC,aAAY;;;;;;;;;;;0DAE3B,4TAAC,8HAAA,CAAA,gBAAa;0DACX,cAAc,GAAG,CAAC,CAAC,uBAClB,4TAAC,8HAAA,CAAA,aAAU;wDAAoB,OAAO,OAAO,KAAK;kEAChD,cAAA,4TAAC;4DAAI,WAAU;;gEACZ,OAAO,KAAK,KAAK,6BAChB,4TAAC,2SAAA,CAAA,cAAW;oEAAC,WAAU;;;;;yFAEvB,4TAAC,uSAAA,CAAA,YAAS;oEAAC,WAAU;;;;;;8EAEvB,4TAAC;oEAAK,WAAU;8EAAe,OAAO,KAAK;;;;;;;;;;;;uDAP9B,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;gCAgBpC,+BACC,4TAAC,qIAAA,CAAA,YAAS;oCACR,IAAG;oCACH,OAAM;oCACN,UAAU;8CAEV,cAAA,4TAAC,gIAAA,CAAA,WAAQ;wCACP,IAAG;wCACF,GAAG,SAAS,eAAe;wCAC5B,aAAY;wCACZ,MAAM;wCACN,WAAU;;;;;;;;;;;gCAMf,8BACC,4TAAC;oCACC,WAAW,CAAC,sBAAsB,EAChC,gBACI,iCACA,8BACJ;8CAEF,cAAA,4TAAC;wCAAI,WAAU;;4CACZ,8BACC,4TAAC,kTAAA,CAAA,cAAW;gDAAC,WAAU;;;;;qEAEvB,4TAAC,2SAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;0DAEzB,4TAAC;gDACC,WAAW,CAAC,YAAY,EACtB,gBAAgB,mBAAmB,iBACnC;0DAED,gBACG,CAAC,kBAAkB,EAAE,cAAc,IAAI,CAAC,CAAC,IAAM,EAAE,KAAK,KAAK,eAAe,MAAM,OAAO,CAAC,GACxF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUlB,4TAAC,8IAAA,CAAA,qBAAkB;gBACjB,OAAM;gBACN,aAAY;gBACZ,oBAAM,4TAAC,2RAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;gBACvB,aAAY;gBACZ,aAAa;gBACb,YAAY;0BAEZ,cAAA,4TAAC;oBAAI,WAAU;;sCAEb,4TAAC,qIAAA,CAAA,YAAS;4BAAC,IAAG;4BAAe,OAAM;4BAAgB,UAAU;sCAC3D,cAAA,4TAAC,6HAAA,CAAA,QAAK;gCACJ,IAAG;gCACH,MAAK;gCACJ,GAAG,SAAS,eAAe;gCAC5B,aAAY;gCACZ,WAAU;;;;;;;;;;;sCAKd,4TAAC,qIAAA,CAAA,YAAS;4BAAC,IAAG;4BAAe,OAAM;4BAAgB,UAAU;sCAC3D,cAAA,4TAAC,6HAAA,CAAA,QAAK;gCACJ,IAAG;gCACF,GAAG,SAAS,eAAe;gCAC5B,aAAY;gCACZ,WAAU;;;;;;;;;;;sCAKd,4TAAC,qIAAA,CAAA,YAAS;4BAAC,IAAG;4BAAe,OAAM;4BAAgB,UAAU;sCAC3D,cAAA,4TAAC,6HAAA,CAAA,QAAK;gCACJ,IAAG;gCACF,GAAG,SAAS,eAAe;gCAC5B,aAAY;gCACZ,WAAU;;;;;;;;;;;;;;;;;;;;;;0BAOlB,4TAAC,4HAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,4TAAC,4HAAA,CAAA,aAAU;kCACT,cAAA,4TAAC,4HAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,4TAAC,qSAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;;;;;kCAIpC,4TAAC,4HAAA,CAAA,cAAW;kCACV,cAAA,4TAAC;4BAAI,WAAU;;8CACb,4TAAC;oCAAI,WAAU;;sDACb,4TAAC;4CAAI,WAAU;sDACb,cAAA,4TAAC,6RAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;sDAEpB,4TAAC;;8DACC,4TAAC;oDAAG,WAAU;8DAA4B;;;;;;8DAC1C,4TAAC;oDAAE,WAAU;8DACV,cACG,GAAG,gBAAgB,IAAI,CAAC,CAAC,IAAM,EAAE,KAAK,KAAK,iBAAiB,MAAM,SAAS,CAAC,GAC5E;;;;;;;;;;;;;;;;;;8CAKV,4TAAC;oCAAI,WAAU;;sDACb,4TAAC;4CAAI,WAAU;sDACb,cAAA,4TAAC,uSAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;;;;;;sDAEvB,4TAAC;;8DACC,4TAAC;oDAAG,WAAU;8DAA6B;;;;;;8DAC3C,4TAAC;oDAAE,WAAU;8DACV,gBACG,GAAG,cAAc,IAAI,CAAC,CAAC,IAAM,EAAE,KAAK,KAAK,eAAe,MAAM,cAAc,CAAC,GAC7E;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAStB;KAzSa", "debugId": null}}, {"offset": {"line": 6204, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/pages/products/add/FormSectionRenderer.tsx"], "sourcesContent": ["import { Dispatch, SetStateAction } from \"react\";\r\n\r\nimport {\r\n  <PERSON>Errors,\r\n  UseFormRegister,\r\n  UseFormSetValue,\r\n  UseFormWatch,\r\n} from \"react-hook-form\";\r\n\r\nimport { ProductFormData } from \"@/schemas/productSchema\";\r\nimport { SectionId } from \"@/types/form-section\";\r\n\r\nimport { AdvancedSection } from \"./AdvancedSection\";\r\nimport { AvailabilitySection } from \"./AvailabilitySection\";\r\nimport { BasicInfoSection } from \"./BasicInfoSection\";\r\nimport { DetailsSection } from \"./DetailsSection\";\r\nimport { InventorySection } from \"./InventorySection\";\r\nimport { MediaSection } from \"./MediaSection\";\r\nimport { PricingSection } from \"./PricingSection\";\r\nimport { SEOSection } from \"./SEOSection\";\r\nimport { ShippingSection } from \"./ShippingSection\";\r\nimport { WarrantySection } from \"./WarrantySection\";\r\n\r\ntype FormSectionRendererProps = {\r\n  currentSection: SectionId;\r\n  register: UseFormRegister<ProductFormData>;\r\n  errors: FieldErrors<ProductFormData>;\r\n  setValue: UseFormSetValue<ProductFormData>;\r\n  watch: UseFormWatch<ProductFormData>;\r\n  tags: string[];\r\n  setTags: Dispatch<SetStateAction<string[]>>;\r\n  images: { key: string; url: string }[];\r\n  setImages: Dispatch<SetStateAction<{ key: string; url: string }[]>>;\r\n  imageAltTexts: string[];\r\n  setImageAltTexts: Dispatch<SetStateAction<string[]>>;\r\n};\r\n\r\n/**\r\n * Renders the appropriate form section based on the current section ID\r\n */\r\nexport const FormSectionRenderer = ({\r\n  currentSection,\r\n  register,\r\n  errors,\r\n  setValue,\r\n  watch,\r\n  tags,\r\n  setTags,\r\n  images,\r\n  setImages,\r\n  imageAltTexts,\r\n  setImageAltTexts,\r\n}: FormSectionRendererProps) => {\r\n  // Render the appropriate section based on currentSection\r\n  switch (currentSection) {\r\n    case \"basic-info\":\r\n      return (\r\n        <BasicInfoSection\r\n          register={register}\r\n          errors={errors}\r\n          setValue={setValue}\r\n          watch={watch}\r\n        />\r\n      );\r\n\r\n    case \"pricing\":\r\n      return (\r\n        <PricingSection\r\n          register={register}\r\n          errors={errors}\r\n          setValue={setValue}\r\n          watch={watch}\r\n        />\r\n      );\r\n\r\n    case \"inventory\":\r\n      return (\r\n        <InventorySection\r\n          register={register}\r\n          errors={errors}\r\n          setValue={setValue}\r\n          watch={watch}\r\n        />\r\n      );\r\n\r\n    case \"details\":\r\n      return (\r\n        <DetailsSection\r\n          register={register}\r\n          errors={errors}\r\n          setValue={setValue}\r\n          watch={watch}\r\n          tags={tags}\r\n          setTags={setTags}\r\n        />\r\n      );\r\n\r\n    case \"media\":\r\n      return (\r\n        <MediaSection\r\n          register={register}\r\n          errors={errors}\r\n          setValue={setValue}\r\n          watch={watch}\r\n          images={images}\r\n          setImages={setImages}\r\n          imageAltTexts={imageAltTexts}\r\n          setImageAltTexts={setImageAltTexts}\r\n        />\r\n      );\r\n\r\n    case \"shipping\":\r\n      return (\r\n        <ShippingSection\r\n          register={register}\r\n          errors={errors}\r\n          setValue={setValue}\r\n          watch={watch}\r\n        />\r\n      );\r\n\r\n    case \"availability\":\r\n      return (\r\n        <AvailabilitySection\r\n          register={register}\r\n          errors={errors}\r\n          setValue={setValue}\r\n          watch={watch}\r\n        />\r\n      );\r\n\r\n    case \"seo\":\r\n      return (\r\n        <SEOSection\r\n          register={register}\r\n          errors={errors}\r\n          setValue={setValue}\r\n          watch={watch}\r\n        />\r\n      );\r\n\r\n    case \"warranty\":\r\n      return (\r\n        <WarrantySection\r\n          register={register}\r\n          errors={errors}\r\n          setValue={setValue}\r\n          watch={watch}\r\n        />\r\n      );\r\n\r\n    case \"advanced\":\r\n      return (\r\n        <AdvancedSection\r\n          register={register}\r\n          errors={errors}\r\n          setValue={setValue}\r\n          watch={watch}\r\n        />\r\n      );\r\n\r\n    default:\r\n      return <div>Section not implemented</div>;\r\n  }\r\n};\r\n"], "names": [], "mappings": ";;;;AAYA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;AAmBO,MAAM,sBAAsB,CAAC,EAClC,cAAc,EACd,QAAQ,EACR,MAAM,EACN,QAAQ,EACR,KAAK,EACL,IAAI,EACJ,OAAO,EACP,MAAM,EACN,SAAS,EACT,aAAa,EACb,gBAAgB,EACS;IACzB,yDAAyD;IACzD,OAAQ;QACN,KAAK;YACH,qBACE,4TAAC,8JAAA,CAAA,mBAAgB;gBACf,UAAU;gBACV,QAAQ;gBACR,UAAU;gBACV,OAAO;;;;;;QAIb,KAAK;YACH,qBACE,4TAAC,4JAAA,CAAA,iBAAc;gBACb,UAAU;gBACV,QAAQ;gBACR,UAAU;gBACV,OAAO;;;;;;QAIb,KAAK;YACH,qBACE,4TAAC,8JAAA,CAAA,mBAAgB;gBACf,UAAU;gBACV,QAAQ;gBACR,UAAU;gBACV,OAAO;;;;;;QAIb,KAAK;YACH,qBACE,4TAAC,4JAAA,CAAA,iBAAc;gBACb,UAAU;gBACV,QAAQ;gBACR,UAAU;gBACV,OAAO;gBACP,MAAM;gBACN,SAAS;;;;;;QAIf,KAAK;YACH,qBACE,4TAAC,0JAAA,CAAA,eAAY;gBACX,UAAU;gBACV,QAAQ;gBACR,UAAU;gBACV,OAAO;gBACP,QAAQ;gBACR,WAAW;gBACX,eAAe;gBACf,kBAAkB;;;;;;QAIxB,KAAK;YACH,qBACE,4TAAC,6JAAA,CAAA,kBAAe;gBACd,UAAU;gBACV,QAAQ;gBACR,UAAU;gBACV,OAAO;;;;;;QAIb,KAAK;YACH,qBACE,4TAAC,iKAAA,CAAA,sBAAmB;gBAClB,UAAU;gBACV,QAAQ;gBACR,UAAU;gBACV,OAAO;;;;;;QAIb,KAAK;YACH,qBACE,4TAAC,wJAAA,CAAA,aAAU;gBACT,UAAU;gBACV,QAAQ;gBACR,UAAU;gBACV,OAAO;;;;;;QAIb,KAAK;YACH,qBACE,4TAAC,6JAAA,CAAA,kBAAe;gBACd,UAAU;gBACV,QAAQ;gBACR,UAAU;gBACV,OAAO;;;;;;QAIb,KAAK;YACH,qBACE,4TAAC,6JAAA,CAAA,kBAAe;gBACd,UAAU;gBACV,QAAQ;gBACR,UAAU;gBACV,OAAO;;;;;;QAIb;YACE,qBAAO,4TAAC;0BAAI;;;;;;IAChB;AACF;KA5Ha", "debugId": null}}, {"offset": {"line": 6370, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/pages/products/add/FormSections.tsx"], "sourcesContent": ["import {\r\n  Calendar,\r\n  DollarSign,\r\n  FileImage,\r\n  Info,\r\n  Package,\r\n  Search,\r\n  Settings,\r\n  Shield,\r\n  Tag,\r\n  Truck,\r\n} from \"lucide-react\";\r\n\r\nimport { FormSection, SectionId } from \"@/types/form-section\";\r\n\r\nexport const getFormSections = (\r\n  completedSections: SectionId[] = []\r\n): FormSection[] => {\r\n  const currentSection =\r\n    completedSections.length > 0\r\n      ? completedSections[completedSections.length - 1]\r\n      : \"basic-info\";\r\n\r\n  return [\r\n    {\r\n      id: \"basic-info\",\r\n      title: \"Basic Information\",\r\n      icon: <Info size={18} />,\r\n      isCompleted: completedSections.includes(\"basic-info\"),\r\n      isActive: currentSection === \"basic-info\",\r\n    },\r\n    {\r\n      id: \"pricing\",\r\n      title: \"Pricing\",\r\n      icon: <DollarSign size={18} />,\r\n      isCompleted: completedSections.includes(\"pricing\"),\r\n      isActive: currentSection === \"pricing\",\r\n    },\r\n    {\r\n      id: \"inventory\",\r\n      title: \"Inventory\",\r\n      icon: <Package size={18} />,\r\n      isCompleted: completedSections.includes(\"inventory\"),\r\n      isActive: currentSection === \"inventory\",\r\n    },\r\n    {\r\n      id: \"details\",\r\n      title: \"Product Details\",\r\n      icon: <Tag size={18} />,\r\n      isCompleted: completedSections.includes(\"details\"),\r\n      isActive: currentSection === \"details\",\r\n    },\r\n    {\r\n      id: \"media\",\r\n      title: \"Images & Media\",\r\n      icon: <FileImage size={18} />,\r\n      isCompleted: completedSections.includes(\"media\"),\r\n      isActive: currentSection === \"media\",\r\n    },\r\n    {\r\n      id: \"shipping\",\r\n      title: \"Shipping\",\r\n      icon: <Truck size={18} />,\r\n      isCompleted: completedSections.includes(\"shipping\"),\r\n      isActive: currentSection === \"shipping\",\r\n      isOptional: true,\r\n    },\r\n    {\r\n      id: \"availability\",\r\n      title: \"Availability\",\r\n      icon: <Calendar size={18} />,\r\n      isCompleted: completedSections.includes(\"availability\"),\r\n      isActive: currentSection === \"availability\",\r\n    },\r\n    {\r\n      id: \"seo\",\r\n      title: \"SEO & Marketing\",\r\n      icon: <Search size={18} />,\r\n      isCompleted: completedSections.includes(\"seo\"),\r\n      isActive: currentSection === \"seo\",\r\n      isOptional: true,\r\n    },\r\n    {\r\n      id: \"warranty\",\r\n      title: \"Warranty & Returns\",\r\n      icon: <Shield size={18} />,\r\n      isCompleted: completedSections.includes(\"warranty\"),\r\n      isActive: currentSection === \"warranty\",\r\n      isOptional: true,\r\n    },\r\n    {\r\n      id: \"advanced\",\r\n      title: \"Advanced Settings\",\r\n      icon: <Settings size={18} />,\r\n      isCompleted: completedSections.includes(\"advanced\"),\r\n      isActive: currentSection === \"advanced\",\r\n      isOptional: true,\r\n    },\r\n  ];\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAeO,MAAM,kBAAkB,CAC7B,oBAAiC,EAAE;IAEnC,MAAM,iBACJ,kBAAkB,MAAM,GAAG,IACvB,iBAAiB,CAAC,kBAAkB,MAAM,GAAG,EAAE,GAC/C;IAEN,OAAO;QACL;YACE,IAAI;YACJ,OAAO;YACP,oBAAM,4TAAC,yRAAA,CAAA,OAAI;gBAAC,MAAM;;;;;;YAClB,aAAa,kBAAkB,QAAQ,CAAC;YACxC,UAAU,mBAAmB;QAC/B;QACA;YACE,IAAI;YACJ,OAAO;YACP,oBAAM,4TAAC,ySAAA,CAAA,aAAU;gBAAC,MAAM;;;;;;YACxB,aAAa,kBAAkB,QAAQ,CAAC;YACxC,UAAU,mBAAmB;QAC/B;QACA;YACE,IAAI;YACJ,OAAO;YACP,oBAAM,4TAAC,+RAAA,CAAA,UAAO;gBAAC,MAAM;;;;;;YACrB,aAAa,kBAAkB,QAAQ,CAAC;YACxC,UAAU,mBAAmB;QAC/B;QACA;YACE,IAAI;YACJ,OAAO;YACP,oBAAM,4TAAC,uRAAA,CAAA,MAAG;gBAAC,MAAM;;;;;;YACjB,aAAa,kBAAkB,QAAQ,CAAC;YACxC,UAAU,mBAAmB;QAC/B;QACA;YACE,IAAI;YACJ,OAAO;YACP,oBAAM,4TAAC,uSAAA,CAAA,YAAS;gBAAC,MAAM;;;;;;YACvB,aAAa,kBAAkB,QAAQ,CAAC;YACxC,UAAU,mBAAmB;QAC/B;QACA;YACE,IAAI;YACJ,OAAO;YACP,oBAAM,4TAAC,2RAAA,CAAA,QAAK;gBAAC,MAAM;;;;;;YACnB,aAAa,kBAAkB,QAAQ,CAAC;YACxC,UAAU,mBAAmB;YAC7B,YAAY;QACd;QACA;YACE,IAAI;YACJ,OAAO;YACP,oBAAM,4TAAC,iSAAA,CAAA,WAAQ;gBAAC,MAAM;;;;;;YACtB,aAAa,kBAAkB,QAAQ,CAAC;YACxC,UAAU,mBAAmB;QAC/B;QACA;YACE,IAAI;YACJ,OAAO;YACP,oBAAM,4TAAC,6RAAA,CAAA,SAAM;gBAAC,MAAM;;;;;;YACpB,aAAa,kBAAkB,QAAQ,CAAC;YACxC,UAAU,mBAAmB;YAC7B,YAAY;QACd;QACA;YACE,IAAI;YACJ,OAAO;YACP,oBAAM,4TAAC,6RAAA,CAAA,SAAM;gBAAC,MAAM;;;;;;YACpB,aAAa,kBAAkB,QAAQ,CAAC;YACxC,UAAU,mBAAmB;YAC7B,YAAY;QACd;QACA;YACE,IAAI;YACJ,OAAO;YACP,oBAAM,4TAAC,iSAAA,CAAA,WAAQ;gBAAC,MAAM;;;;;;YACtB,aAAa,kBAAkB,QAAQ,CAAC;YACxC,UAAU,mBAAmB;YAC7B,YAAY;QACd;KACD;AACH", "debugId": null}}]}