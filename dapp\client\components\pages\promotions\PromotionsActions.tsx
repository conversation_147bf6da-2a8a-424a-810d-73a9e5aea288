"use client";

import { Plus, Download, Filter, Zap, Calendar } from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";

export const PromotionsActions = () => {
  return (
    <div className="flex gap-2">
      <Button variant="outline" size="sm">
        <Filter className="mr-2 h-4 w-4" />
        Filter
      </Button>

      <Button variant="outline" size="sm">
        <Calendar className="mr-2 h-4 w-4" />
        Schedule
      </Button>

      <Button variant="outline" size="sm">
        <Download className="mr-2 h-4 w-4" />
        Export
      </Button>

      <Button variant="outline" size="sm">
        <Zap className="mr-2 h-4 w-4" />
        Quick Sale
      </Button>

      <Button size="sm">
        <Plus className="mr-2 h-4 w-4" />
        New Promotion
      </Button>
    </div>
  );
};
