{"name": "espusla", "version": "0.0.1", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.2.3", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.1.12", "@uploadthing/react": "^7.3.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "eslint-plugin-tailwindcss": "^3.18.0", "framer-motion": "^12.9.2", "lucide-react": "^0.482.0", "next": "15.2.2", "react": "^19.1.0", "react-day-picker": "8.10.1", "react-dom": "^19.1.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.56.1", "react-icons": "^5.5.0", "sonner": "^2.0.3", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "uploadthing": "^7.6.0", "zod": "^3.24.3"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@next/eslint-plugin-next": "^15.3.1", "@trivago/prettier-plugin-sort-imports": "^5.2.2", "@types/node": "^20.17.32", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "eslint": "^9.25.1", "eslint-config-next": "15.2.2", "eslint-config-prettier": "^9.1.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "postcss": "^8.5.3", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^3.4.17", "typescript": "^5.8.3"}, "packageManager": "pnpm@10.8.0"}