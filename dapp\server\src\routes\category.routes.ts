import { Router } from "express";
import { CategoryController } from "../controllers/category/category.controller";

const router = Router();
const categoryController = new CategoryController();

// GET /api/categories - Get all categories with optional filtering
router.get("/", categoryController.getCategories);

// GET /api/categories/slug/:slug - Get category by slug
router.get("/slug/:slug", categoryController.getCategoryBySlug);

// GET /api/categories/:id - Get category by ID
router.get("/:id", categoryController.getCategoryById);

// POST /api/categories - Create a new category
router.post("/", categoryController.createCategory);

// PUT /api/categories/:id - Update a category
router.put("/:id", categoryController.updateCategory);

// DELETE /api/categories/:id - Delete a category
router.delete("/:id", categoryController.deleteCategory);

export const categoryRoutes = router;
