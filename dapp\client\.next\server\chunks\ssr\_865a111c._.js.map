{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/common/PageHeaderWrapper.tsx"], "sourcesContent": ["import React from \"react\";\r\n\r\ntype PageHeaderWrapperProps = {\r\n  title: string;\r\n  description: string;\r\n  children?: React.ReactNode;\r\n};\r\n\r\n/**\r\n * A standardized page header component with title and description\r\n * Used across admin pages for consistent UI\r\n */\r\nexport const PageHeaderWrapper = ({\r\n  title,\r\n  description,\r\n  children,\r\n}: PageHeaderWrapperProps) => {\r\n  return (\r\n    <div className=\"container mx-auto py-6\">\r\n      <div className=\"flex flex-col items-start justify-between gap-4 border-b pb-5 sm:flex-row sm:items-center sm:gap-0\">\r\n        <div>\r\n          <h1 className=\"text-3xl font-bold tracking-tight\">{title}</h1>\r\n          <p className=\"text-muted-foreground\">{description}</p>\r\n        </div>\r\n        {children && <div>{children}</div>}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;;AAYO,MAAM,oBAAoB,CAAC,EAChC,KAAK,EACL,WAAW,EACX,QAAQ,EACe;IACvB,qBACE,6WAAC;QAAI,WAAU;kBACb,cAAA,6WAAC;YAAI,WAAU;;8BACb,6WAAC;;sCACC,6WAAC;4BAAG,WAAU;sCAAqC;;;;;;sCACnD,6WAAC;4BAAE,WAAU;sCAAyB;;;;;;;;;;;;gBAEvC,0BAAY,6WAAC;8BAAK;;;;;;;;;;;;;;;;;AAI3B", "debugId": null}}, {"offset": {"line": 67, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Card = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\r\n      \"rounded-xl border bg-card text-card-foreground shadow\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCard.displayName = \"Card\"\r\n\r\nconst CardHeader = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardHeader.displayName = \"CardHeader\"\r\n\r\nconst CardTitle = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"font-semibold leading-none tracking-tight\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardTitle.displayName = \"CardTitle\"\r\n\r\nconst CardDescription = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardDescription.displayName = \"CardDescription\"\r\n\r\nconst CardContent = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\r\n))\r\nCardContent.displayName = \"CardContent\"\r\n\r\nconst CardFooter = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex items-center p-6 pt-0\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardFooter.displayName = \"CardFooter\"\r\n\r\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\r\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Input = React.forwardRef<HTMLInputElement, React.ComponentProps<\"input\">>(\r\n  ({ className, type, ...props }, ref) => {\r\n    return (\r\n      <input\r\n        type={type}\r\n        className={cn(\r\n          \"flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n          className\r\n        )}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n)\r\nInput.displayName = \"Input\"\r\n\r\nexport { Input }\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,sBAAQ,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,6WAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,2WACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 177, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst labelVariants = cva(\r\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\r\n)\r\n\r\nconst Label = React.forwardRef<\r\n  React.ElementRef<typeof LabelPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\r\n    VariantProps<typeof labelVariants>\r\n>(({ className, ...props }, ref) => (\r\n  <LabelPrimitive.Root\r\n    ref={ref}\r\n    className={cn(labelVariants(), className)}\r\n    {...props}\r\n  />\r\n))\r\nLabel.displayName = LabelPrimitive.Root.displayName\r\n\r\nexport { Label }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,gBAAgB,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EACtB;AAGF,MAAM,sBAAQ,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAI3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC,8QAAA,CAAA,OAAmB;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC9B,GAAG,KAAK;;;;;;AAGb,MAAM,WAAW,GAAG,8QAAA,CAAA,OAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 209, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\r\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Select = SelectPrimitive.Root\r\n\r\nconst SelectGroup = SelectPrimitive.Group\r\n\r\nconst SelectValue = SelectPrimitive.Value\r\n\r\nconst SelectTrigger = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\r\n>(({ className, children, ...props }, ref) => (\r\n  <SelectPrimitive.Trigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex h-9 w-full items-center justify-between whitespace-nowrap rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm ring-offset-background data-[placeholder]:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    {children}\r\n    <SelectPrimitive.Icon asChild>\r\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\r\n    </SelectPrimitive.Icon>\r\n  </SelectPrimitive.Trigger>\r\n))\r\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\r\n\r\nconst SelectScrollUpButton = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.ScrollUpButton\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex cursor-default items-center justify-center py-1\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <ChevronUp className=\"h-4 w-4\" />\r\n  </SelectPrimitive.ScrollUpButton>\r\n))\r\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\r\n\r\nconst SelectScrollDownButton = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.ScrollDownButton\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex cursor-default items-center justify-center py-1\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <ChevronDown className=\"h-4 w-4\" />\r\n  </SelectPrimitive.ScrollDownButton>\r\n))\r\nSelectScrollDownButton.displayName =\r\n  SelectPrimitive.ScrollDownButton.displayName\r\n\r\nconst SelectContent = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\r\n>(({ className, children, position = \"popper\", ...props }, ref) => (\r\n  <SelectPrimitive.Portal>\r\n    <SelectPrimitive.Content\r\n      ref={ref}\r\n      className={cn(\r\n        \"relative z-50 max-h-[--radix-select-content-available-height] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-select-content-transform-origin]\",\r\n        position === \"popper\" &&\r\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\r\n        className\r\n      )}\r\n      position={position}\r\n      {...props}\r\n    >\r\n      <SelectScrollUpButton />\r\n      <SelectPrimitive.Viewport\r\n        className={cn(\r\n          \"p-1\",\r\n          position === \"popper\" &&\r\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\r\n        )}\r\n      >\r\n        {children}\r\n      </SelectPrimitive.Viewport>\r\n      <SelectScrollDownButton />\r\n    </SelectPrimitive.Content>\r\n  </SelectPrimitive.Portal>\r\n))\r\nSelectContent.displayName = SelectPrimitive.Content.displayName\r\n\r\nconst SelectLabel = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Label>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.Label\r\n    ref={ref}\r\n    className={cn(\"px-2 py-1.5 text-sm font-semibold\", className)}\r\n    {...props}\r\n  />\r\n))\r\nSelectLabel.displayName = SelectPrimitive.Label.displayName\r\n\r\nconst SelectItem = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Item>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\r\n>(({ className, children, ...props }, ref) => (\r\n  <SelectPrimitive.Item\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <span className=\"absolute right-2 flex h-3.5 w-3.5 items-center justify-center\">\r\n      <SelectPrimitive.ItemIndicator>\r\n        <Check className=\"h-4 w-4\" />\r\n      </SelectPrimitive.ItemIndicator>\r\n    </span>\r\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\r\n  </SelectPrimitive.Item>\r\n))\r\nSelectItem.displayName = SelectPrimitive.Item.displayName\r\n\r\nconst SelectSeparator = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Separator>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.Separator\r\n    ref={ref}\r\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\r\n    {...props}\r\n  />\r\n))\r\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\r\n\r\nexport {\r\n  Select,\r\n  SelectGroup,\r\n  SelectValue,\r\n  SelectTrigger,\r\n  SelectContent,\r\n  SelectLabel,\r\n  SelectItem,\r\n  SelectSeparator,\r\n  SelectScrollUpButton,\r\n  SelectScrollDownButton,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,+QAAA,CAAA,OAAoB;AAEnC,MAAM,cAAc,+QAAA,CAAA,QAAqB;AAEzC,MAAM,cAAc,+QAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6WAAC,+QAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,kUACA;QAED,GAAG,KAAK;;YAER;0BACD,6WAAC,+QAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6WAAC,wSAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,+QAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAG1C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC,+QAAA,CAAA,iBAA8B;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6WAAC,oSAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;AAGzB,qBAAqB,WAAW,GAAG,+QAAA,CAAA,iBAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC,+QAAA,CAAA,mBAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6WAAC,wSAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;AAG3B,uBAAuB,WAAW,GAChC,+QAAA,CAAA,mBAAgC,CAAC,WAAW;AAE9C,MAAM,8BAAgB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,6WAAC,+QAAA,CAAA,SAAsB;kBACrB,cAAA,6WAAC,+QAAA,CAAA,UAAuB;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6WAAC;;;;;8BACD,6WAAC,+QAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6WAAC;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,+QAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC,+QAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,qCAAqC;QAClD,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,+QAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6WAAC,+QAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,6NACA;QAED,GAAG,KAAK;;0BAET,6WAAC;gBAAK,WAAU;0BACd,cAAA,6WAAC,+QAAA,CAAA,gBAA6B;8BAC5B,cAAA,6WAAC,wRAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGrB,6WAAC,+QAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,+QAAA,CAAA,OAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC,+QAAA,CAAA,YAAyB;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG,+QAAA,CAAA,YAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 401, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/ui/switch.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SwitchPrimitives from \"@radix-ui/react-switch\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Switch = React.forwardRef<\r\n  React.ElementRef<typeof SwitchPrimitives.Root>,\r\n  React.ComponentPropsWithoutRef<typeof SwitchPrimitives.Root>\r\n>(({ className, ...props }, ref) => (\r\n  <SwitchPrimitives.Root\r\n    className={cn(\r\n      \"peer inline-flex h-5 w-9 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input\",\r\n      className\r\n    )}\r\n    {...props}\r\n    ref={ref}\r\n  >\r\n    <SwitchPrimitives.Thumb\r\n      className={cn(\r\n        \"pointer-events-none block h-4 w-4 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-4 data-[state=unchecked]:translate-x-0\"\r\n      )}\r\n    />\r\n  </SwitchPrimitives.Root>\r\n))\r\nSwitch.displayName = SwitchPrimitives.Root.displayName\r\n\r\nexport { Switch }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,uBAAS,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAG5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC,+QAAA,CAAA,OAAqB;QACpB,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,+XACA;QAED,GAAG,KAAK;QACT,KAAK;kBAEL,cAAA,6WAAC,+QAAA,CAAA,QAAsB;YACrB,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV;;;;;;;;;;;AAKR,OAAO,WAAW,GAAG,+QAAA,CAAA,OAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 437, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Textarea = React.forwardRef<\r\n  HTMLTextAreaElement,\r\n  React.ComponentProps<\"textarea\">\r\n>(({ className, ...props }, ref) => {\r\n  return (\r\n    <textarea\r\n      className={cn(\r\n        \"flex min-h-[60px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-base shadow-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n        className\r\n      )}\r\n      ref={ref}\r\n      {...props}\r\n    />\r\n  )\r\n})\r\nTextarea.displayName = \"Textarea\"\r\n\r\nexport { Textarea }\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,yBAAW,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,6WAAC;QACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,6QACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AACA,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 465, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/pages/management/BrandManagerEnhanced.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\n\nimport {\n  Archive,\n  Award,\n  Building2,\n  Copy,\n  Edit,\n  ExternalLink,\n  Eye,\n  Filter,\n  Globe,\n  Grid3X3,\n  Image,\n  List,\n  Mail,\n  MoreHorizontal,\n  Package,\n  Pencil,\n  Plus,\n  Save,\n  Search,\n  SortAsc,\n  SortDesc,\n  Star,\n  Tag,\n  Trash2,\n  TrendingUp,\n  X,\n} from \"lucide-react\";\nimport { toast } from \"sonner\";\n\nimport { Badge } from \"@/components/ui/badge\";\nimport { Button } from \"@/components/ui/button\";\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from \"@/components/ui/dropdown-menu\";\nimport { Input } from \"@/components/ui/input\";\nimport { Label } from \"@/components/ui/label\";\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from \"@/components/ui/select\";\nimport { Switch } from \"@/components/ui/switch\";\nimport { Textarea } from \"@/components/ui/textarea\";\n\nexport type Brand = {\n  id: string;\n  name: string;\n  description: string;\n  slug: string;\n  logo?: string;\n  website?: string;\n  email?: string;\n  country?: string;\n  foundedYear?: number;\n  isActive: boolean;\n  isPremium: boolean;\n  productCount: number;\n  rating: number;\n  sortOrder: number;\n  createdAt: string;\n  updatedAt: string;\n};\n\n// Mock brands with enhanced data\nconst mockBrands: Brand[] = [\n  {\n    id: \"brand-1\",\n    name: \"Apple\",\n    description:\n      \"Innovative technology company known for premium consumer electronics\",\n    slug: \"apple\",\n    logo: \"🍎\",\n    website: \"https://apple.com\",\n    email: \"<EMAIL>\",\n    country: \"United States\",\n    foundedYear: 1976,\n    isActive: true,\n    isPremium: true,\n    productCount: 89,\n    rating: 4.8,\n    sortOrder: 1,\n    createdAt: \"2024-01-01T00:00:00Z\",\n    updatedAt: \"2024-01-15T10:30:00Z\",\n  },\n  {\n    id: \"brand-2\",\n    name: \"Samsung\",\n    description:\n      \"Global leader in technology, semiconductors, and consumer electronics\",\n    slug: \"samsung\",\n    logo: \"📱\",\n    website: \"https://samsung.com\",\n    email: \"<EMAIL>\",\n    country: \"South Korea\",\n    foundedYear: 1938,\n    isActive: true,\n    isPremium: true,\n    productCount: 156,\n    rating: 4.6,\n    sortOrder: 2,\n    createdAt: \"2024-01-02T00:00:00Z\",\n    updatedAt: \"2024-01-14T16:45:00Z\",\n  },\n  {\n    id: \"brand-3\",\n    name: \"Nike\",\n    description: \"World's leading supplier of athletic shoes and apparel\",\n    slug: \"nike\",\n    logo: \"✅\",\n    website: \"https://nike.com\",\n    email: \"<EMAIL>\",\n    country: \"United States\",\n    foundedYear: 1964,\n    isActive: true,\n    isPremium: true,\n    productCount: 234,\n    rating: 4.7,\n    sortOrder: 3,\n    createdAt: \"2024-01-03T00:00:00Z\",\n    updatedAt: \"2024-01-13T09:20:00Z\",\n  },\n  {\n    id: \"brand-4\",\n    name: \"IKEA\",\n    description:\n      \"Swedish furniture retailer known for affordable, functional home furnishing\",\n    slug: \"ikea\",\n    logo: \"🏠\",\n    website: \"https://ikea.com\",\n    email: \"<EMAIL>\",\n    country: \"Sweden\",\n    foundedYear: 1943,\n    isActive: true,\n    isPremium: false,\n    productCount: 445,\n    rating: 4.3,\n    sortOrder: 4,\n    createdAt: \"2024-01-04T00:00:00Z\",\n    updatedAt: \"2024-01-12T14:15:00Z\",\n  },\n  {\n    id: \"brand-5\",\n    name: \"Adidas\",\n    description:\n      \"German multinational corporation that designs and manufactures sports equipment\",\n    slug: \"adidas\",\n    logo: \"👟\",\n    website: \"https://adidas.com\",\n    email: \"<EMAIL>\",\n    country: \"Germany\",\n    foundedYear: 1949,\n    isActive: true,\n    isPremium: true,\n    productCount: 178,\n    rating: 4.5,\n    sortOrder: 5,\n    createdAt: \"2024-01-05T00:00:00Z\",\n    updatedAt: \"2024-01-11T11:30:00Z\",\n  },\n  {\n    id: \"brand-6\",\n    name: \"Sony\",\n    description:\n      \"Japanese multinational conglomerate focused on electronics and entertainment\",\n    slug: \"sony\",\n    logo: \"🎮\",\n    website: \"https://sony.com\",\n    email: \"<EMAIL>\",\n    country: \"Japan\",\n    foundedYear: 1946,\n    isActive: false,\n    isPremium: true,\n    productCount: 67,\n    rating: 4.4,\n    sortOrder: 6,\n    createdAt: \"2024-01-06T00:00:00Z\",\n    updatedAt: \"2024-01-10T08:45:00Z\",\n  },\n];\n\ntype BrandManagerProps = {\n  initialBrands?: Brand[];\n  onBrandsChange?: (brands: Brand[]) => void;\n};\n\n/**\n * Enhanced component for managing product brands with professional UI\n */\nexport const BrandManagerEnhanced = ({\n  initialBrands = mockBrands,\n  onBrandsChange,\n}: BrandManagerProps) => {\n  const [brands, setBrands] = useState<Brand[]>(initialBrands);\n  const [newBrand, setNewBrand] = useState<Partial<Brand>>({\n    name: \"\",\n    description: \"\",\n    logo: \"\",\n    website: \"\",\n    email: \"\",\n    country: \"\",\n    foundedYear: undefined,\n    isActive: true,\n    isPremium: false,\n  });\n  const [editingBrandId, setEditingBrandId] = useState<string | null>(null);\n  const [editForm, setEditForm] = useState<Partial<Brand>>({});\n  const [searchQuery, setSearchQuery] = useState(\"\");\n  const [filterStatus, setFilterStatus] = useState(\"all\");\n  const [filterType, setFilterType] = useState(\"all\");\n  const [sortBy, setSortBy] = useState(\"name\");\n  const [sortOrder, setSortOrder] = useState<\"asc\" | \"desc\">(\"asc\");\n  const [viewMode, setViewMode] = useState<\"grid\" | \"list\">(\"grid\");\n  const [showAddForm, setShowAddForm] = useState(false);\n\n  // Generate a slug from the brand name\n  const generateSlug = (name: string) => {\n    return name\n      .toLowerCase()\n      .replace(/[^a-z0-9]+/g, \"-\")\n      .replace(/^-|-$/g, \"\");\n  };\n\n  // Filter and sort brands\n  const filteredAndSortedBrands = brands\n    .filter((brand) => {\n      const matchesSearch =\n        brand.name.toLowerCase().includes(searchQuery.toLowerCase()) ||\n        brand.description.toLowerCase().includes(searchQuery.toLowerCase()) ||\n        brand.country?.toLowerCase().includes(searchQuery.toLowerCase());\n\n      const matchesStatus =\n        filterStatus === \"all\" ||\n        (filterStatus === \"active\" && brand.isActive) ||\n        (filterStatus === \"inactive\" && !brand.isActive);\n\n      const matchesType =\n        filterType === \"all\" ||\n        (filterType === \"premium\" && brand.isPremium) ||\n        (filterType === \"standard\" && !brand.isPremium);\n\n      return matchesSearch && matchesStatus && matchesType;\n    })\n    .sort((a, b) => {\n      let comparison = 0;\n      switch (sortBy) {\n        case \"name\":\n          comparison = a.name.localeCompare(b.name);\n          break;\n        case \"products\":\n          comparison = a.productCount - b.productCount;\n          break;\n        case \"rating\":\n          comparison = a.rating - b.rating;\n          break;\n        case \"founded\":\n          comparison = (a.foundedYear || 0) - (b.foundedYear || 0);\n          break;\n        case \"created\":\n          comparison =\n            new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();\n          break;\n        default:\n          comparison = a.sortOrder - b.sortOrder;\n      }\n      return sortOrder === \"asc\" ? comparison : -comparison;\n    });\n\n  // Add a new brand\n  const handleAddBrand = () => {\n    if (!newBrand.name) {\n      toast.error(\"Brand name is required\");\n      return;\n    }\n\n    const slug = generateSlug(newBrand.name);\n\n    // Check if slug already exists\n    if (brands.some((brand) => brand.slug === slug)) {\n      toast.error(\"A brand with this name already exists\");\n      return;\n    }\n\n    const newBrandWithId: Brand = {\n      id: `brand-${Date.now()}`,\n      name: newBrand.name,\n      description: newBrand.description || \"\",\n      slug,\n      logo: newBrand.logo || \"🏢\",\n      website: newBrand.website || \"\",\n      email: newBrand.email || \"\",\n      country: newBrand.country || \"\",\n      foundedYear: newBrand.foundedYear,\n      isActive: newBrand.isActive ?? true,\n      isPremium: newBrand.isPremium ?? false,\n      productCount: 0,\n      rating: 0,\n      sortOrder: brands.length + 1,\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString(),\n    };\n\n    const updatedBrands = [...brands, newBrandWithId];\n    setBrands(updatedBrands);\n    setNewBrand({\n      name: \"\",\n      description: \"\",\n      logo: \"\",\n      website: \"\",\n      email: \"\",\n      country: \"\",\n      foundedYear: undefined,\n      isActive: true,\n      isPremium: false,\n    });\n    setShowAddForm(false);\n\n    // Notify parent component\n    if (onBrandsChange) {\n      onBrandsChange(updatedBrands);\n    }\n\n    toast.success(\"Brand added successfully\");\n  };\n\n  // Start editing a brand\n  const handleEditStart = (brand: Brand) => {\n    setEditingBrandId(brand.id);\n    setEditForm({ ...brand });\n  };\n\n  // Cancel editing\n  const handleEditCancel = () => {\n    setEditingBrandId(null);\n    setEditForm({});\n  };\n\n  // Save edited brand\n  const handleEditSave = () => {\n    if (!editForm.name) {\n      toast.error(\"Brand name is required\");\n      return;\n    }\n\n    const updatedBrands = brands.map((brand) =>\n      brand.id === editingBrandId\n        ? {\n            ...brand,\n            name: editForm.name || brand.name,\n            description: editForm.description || brand.description,\n            logo: editForm.logo || brand.logo,\n            website: editForm.website || brand.website,\n            email: editForm.email || brand.email,\n            country: editForm.country || brand.country,\n            foundedYear: editForm.foundedYear || brand.foundedYear,\n            isActive: editForm.isActive ?? brand.isActive,\n            isPremium: editForm.isPremium ?? brand.isPremium,\n            // Only update slug if name changed\n            slug:\n              brand.name !== editForm.name\n                ? generateSlug(editForm.name)\n                : brand.slug,\n            updatedAt: new Date().toISOString(),\n          }\n        : brand\n    );\n\n    setBrands(updatedBrands);\n    setEditingBrandId(null);\n    setEditForm({});\n\n    // Notify parent component\n    if (onBrandsChange) {\n      onBrandsChange(updatedBrands);\n    }\n\n    toast.success(\"Brand updated successfully\");\n  };\n\n  // Delete a brand\n  const handleDeleteBrand = (brandId: string) => {\n    const updatedBrands = brands.filter((brand) => brand.id !== brandId);\n    setBrands(updatedBrands);\n\n    // Notify parent component\n    if (onBrandsChange) {\n      onBrandsChange(updatedBrands);\n    }\n\n    toast.success(\"Brand deleted successfully\");\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header with Statistics */}\n      <div className=\"grid gap-4 md:grid-cols-4\">\n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center gap-3\">\n              <div className=\"rounded-full bg-green-100 p-2\">\n                <Tag className=\"h-5 w-5 text-green-600\" />\n              </div>\n              <div>\n                <p className=\"text-sm text-gray-600\">Total Brands</p>\n                <p className=\"text-2xl font-bold\">{brands.length}</p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center gap-3\">\n              <div className=\"rounded-full bg-blue-100 p-2\">\n                <TrendingUp className=\"h-5 w-5 text-blue-600\" />\n              </div>\n              <div>\n                <p className=\"text-sm text-gray-600\">Active Brands</p>\n                <p className=\"text-2xl font-bold\">\n                  {brands.filter((b) => b.isActive).length}\n                </p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center gap-3\">\n              <div className=\"rounded-full bg-purple-100 p-2\">\n                <Award className=\"h-5 w-5 text-purple-600\" />\n              </div>\n              <div>\n                <p className=\"text-sm text-gray-600\">Premium Brands</p>\n                <p className=\"text-2xl font-bold\">\n                  {brands.filter((b) => b.isPremium).length}\n                </p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center gap-3\">\n              <div className=\"rounded-full bg-orange-100 p-2\">\n                <Star className=\"h-5 w-5 text-orange-600\" />\n              </div>\n              <div>\n                <p className=\"text-sm text-gray-600\">Avg Rating</p>\n                <p className=\"text-2xl font-bold\">\n                  {brands.length > 0\n                    ? (\n                        brands.reduce((sum, b) => sum + b.rating, 0) /\n                        brands.length\n                      ).toFixed(1)\n                    : \"0.0\"}\n                </p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* Controls */}\n      <Card>\n        <CardContent className=\"p-6\">\n          <div className=\"flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between\">\n            {/* Search and Filters */}\n            <div className=\"flex flex-1 gap-4\">\n              <div className=\"relative max-w-md flex-1\">\n                <Search className=\"absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400\" />\n                <Input\n                  placeholder=\"Search brands...\"\n                  value={searchQuery}\n                  onChange={(e) => setSearchQuery(e.target.value)}\n                  className=\"pl-10\"\n                />\n              </div>\n\n              <Select value={filterStatus} onValueChange={setFilterStatus}>\n                <SelectTrigger className=\"w-32\">\n                  <SelectValue />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"all\">All Status</SelectItem>\n                  <SelectItem value=\"active\">Active</SelectItem>\n                  <SelectItem value=\"inactive\">Inactive</SelectItem>\n                </SelectContent>\n              </Select>\n\n              <Select value={filterType} onValueChange={setFilterType}>\n                <SelectTrigger className=\"w-32\">\n                  <SelectValue />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"all\">All Types</SelectItem>\n                  <SelectItem value=\"premium\">Premium</SelectItem>\n                  <SelectItem value=\"standard\">Standard</SelectItem>\n                </SelectContent>\n              </Select>\n\n              <Select value={sortBy} onValueChange={setSortBy}>\n                <SelectTrigger className=\"w-32\">\n                  <SelectValue />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"name\">Name</SelectItem>\n                  <SelectItem value=\"products\">Products</SelectItem>\n                  <SelectItem value=\"rating\">Rating</SelectItem>\n                  <SelectItem value=\"founded\">Founded</SelectItem>\n                  <SelectItem value=\"created\">Created</SelectItem>\n                </SelectContent>\n              </Select>\n            </div>\n\n            {/* View Controls */}\n            <div className=\"flex items-center gap-2\">\n              <Button\n                variant={sortOrder === \"asc\" ? \"default\" : \"outline\"}\n                size=\"sm\"\n                onClick={() =>\n                  setSortOrder(sortOrder === \"asc\" ? \"desc\" : \"asc\")\n                }\n              >\n                {sortOrder === \"asc\" ? (\n                  <SortAsc className=\"h-4 w-4\" />\n                ) : (\n                  <SortDesc className=\"h-4 w-4\" />\n                )}\n              </Button>\n\n              <div className=\"flex rounded-md border\">\n                <Button\n                  variant={viewMode === \"grid\" ? \"default\" : \"ghost\"}\n                  size=\"sm\"\n                  onClick={() => setViewMode(\"grid\")}\n                  className=\"rounded-r-none\"\n                >\n                  <Grid3X3 className=\"h-4 w-4\" />\n                </Button>\n                <Button\n                  variant={viewMode === \"list\" ? \"default\" : \"ghost\"}\n                  size=\"sm\"\n                  onClick={() => setViewMode(\"list\")}\n                  className=\"rounded-l-none\"\n                >\n                  <List className=\"h-4 w-4\" />\n                </Button>\n              </div>\n\n              <Button onClick={() => setShowAddForm(true)}>\n                <Plus className=\"mr-2 h-4 w-4\" />\n                Add Brand\n              </Button>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Add Brand Form */}\n      {showAddForm && (\n        <Card className=\"border-2 border-green-200 bg-green-50/50\">\n          <CardHeader>\n            <CardTitle className=\"flex items-center gap-2\">\n              <Plus className=\"h-5 w-5 text-green-600\" />\n              Add New Brand\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"grid gap-4 md:grid-cols-2\">\n              <div>\n                <Label htmlFor=\"new-brand-name\">Brand Name *</Label>\n                <Input\n                  id=\"new-brand-name\"\n                  value={newBrand.name}\n                  onChange={(e) =>\n                    setNewBrand({ ...newBrand, name: e.target.value })\n                  }\n                  placeholder=\"e.g. Apple\"\n                  className=\"mt-1\"\n                />\n              </div>\n\n              <div>\n                <Label htmlFor=\"new-brand-logo\">Logo (Emoji)</Label>\n                <Input\n                  id=\"new-brand-logo\"\n                  value={newBrand.logo}\n                  onChange={(e) =>\n                    setNewBrand({ ...newBrand, logo: e.target.value })\n                  }\n                  placeholder=\"🍎\"\n                  className=\"mt-1\"\n                />\n              </div>\n\n              <div className=\"md:col-span-2\">\n                <Label htmlFor=\"new-brand-description\">Description</Label>\n                <Textarea\n                  id=\"new-brand-description\"\n                  value={newBrand.description}\n                  onChange={(e) =>\n                    setNewBrand({ ...newBrand, description: e.target.value })\n                  }\n                  placeholder=\"Describe the brand, its values, and what makes it unique...\"\n                  className=\"mt-1\"\n                  rows={3}\n                />\n              </div>\n\n              <div>\n                <Label htmlFor=\"new-brand-website\">Website</Label>\n                <Input\n                  id=\"new-brand-website\"\n                  type=\"url\"\n                  value={newBrand.website}\n                  onChange={(e) =>\n                    setNewBrand({ ...newBrand, website: e.target.value })\n                  }\n                  placeholder=\"https://example.com\"\n                  className=\"mt-1\"\n                />\n              </div>\n\n              <div>\n                <Label htmlFor=\"new-brand-email\">Contact Email</Label>\n                <Input\n                  id=\"new-brand-email\"\n                  type=\"email\"\n                  value={newBrand.email}\n                  onChange={(e) =>\n                    setNewBrand({ ...newBrand, email: e.target.value })\n                  }\n                  placeholder=\"<EMAIL>\"\n                  className=\"mt-1\"\n                />\n              </div>\n\n              <div>\n                <Label htmlFor=\"new-brand-country\">Country</Label>\n                <Input\n                  id=\"new-brand-country\"\n                  value={newBrand.country}\n                  onChange={(e) =>\n                    setNewBrand({ ...newBrand, country: e.target.value })\n                  }\n                  placeholder=\"United States\"\n                  className=\"mt-1\"\n                />\n              </div>\n\n              <div>\n                <Label htmlFor=\"new-brand-founded\">Founded Year</Label>\n                <Input\n                  id=\"new-brand-founded\"\n                  type=\"number\"\n                  min=\"1800\"\n                  max={new Date().getFullYear()}\n                  value={newBrand.foundedYear || \"\"}\n                  onChange={(e) =>\n                    setNewBrand({\n                      ...newBrand,\n                      foundedYear: parseInt(e.target.value) || undefined,\n                    })\n                  }\n                  placeholder=\"1976\"\n                  className=\"mt-1\"\n                />\n              </div>\n\n              <div className=\"flex items-center space-x-2 pt-6\">\n                <Switch\n                  id=\"new-brand-active\"\n                  checked={newBrand.isActive}\n                  onCheckedChange={(checked) =>\n                    setNewBrand({ ...newBrand, isActive: checked })\n                  }\n                />\n                <Label htmlFor=\"new-brand-active\">Active brand</Label>\n              </div>\n\n              <div className=\"flex items-center space-x-2 pt-6\">\n                <Switch\n                  id=\"new-brand-premium\"\n                  checked={newBrand.isPremium}\n                  onCheckedChange={(checked) =>\n                    setNewBrand({ ...newBrand, isPremium: checked })\n                  }\n                />\n                <Label htmlFor=\"new-brand-premium\">Premium brand</Label>\n              </div>\n            </div>\n\n            <div className=\"mt-6 flex gap-2\">\n              <Button onClick={handleAddBrand}>\n                <Save className=\"mr-2 h-4 w-4\" />\n                Add Brand\n              </Button>\n              <Button variant=\"outline\" onClick={() => setShowAddForm(false)}>\n                <X className=\"mr-2 h-4 w-4\" />\n                Cancel\n              </Button>\n            </div>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Brands Display */}\n      {viewMode === \"grid\" ? (\n        <div className=\"grid gap-4 md:grid-cols-2 lg:grid-cols-3\">\n          {filteredAndSortedBrands.map((brand) => (\n            <Card key={brand.id} className=\"transition-shadow hover:shadow-md\">\n              <CardContent className=\"p-4\">\n                {editingBrandId === brand.id ? (\n                  // Edit form\n                  <div className=\"space-y-3\">\n                    <Input\n                      value={editForm.name || \"\"}\n                      onChange={(e) =>\n                        setEditForm({ ...editForm, name: e.target.value })\n                      }\n                      placeholder=\"Brand name\"\n                    />\n                    <Textarea\n                      value={editForm.description || \"\"}\n                      onChange={(e) =>\n                        setEditForm({\n                          ...editForm,\n                          description: e.target.value,\n                        })\n                      }\n                      placeholder=\"Description\"\n                      rows={2}\n                    />\n                    <div className=\"flex gap-2\">\n                      <Button size=\"sm\" onClick={handleEditSave}>\n                        <Save className=\"mr-1 h-3 w-3\" />\n                        Save\n                      </Button>\n                      <Button\n                        size=\"sm\"\n                        variant=\"outline\"\n                        onClick={handleEditCancel}\n                      >\n                        <X className=\"h-3 w-3\" />\n                      </Button>\n                    </div>\n                  </div>\n                ) : (\n                  // Display mode\n                  <div className=\"space-y-3\">\n                    <div className=\"flex items-start justify-between\">\n                      <div className=\"flex items-center gap-3\">\n                        <span className=\"text-3xl\">{brand.logo}</span>\n                        <div>\n                          <h3 className=\"font-semibold\">{brand.name}</h3>\n                          <div className=\"flex gap-1\">\n                            <Badge\n                              variant={brand.isActive ? \"default\" : \"secondary\"}\n                              className=\"text-xs\"\n                            >\n                              {brand.isActive ? \"Active\" : \"Inactive\"}\n                            </Badge>\n                            {brand.isPremium && (\n                              <Badge\n                                variant=\"outline\"\n                                className=\"border-purple-200 text-xs text-purple-600\"\n                              >\n                                Premium\n                              </Badge>\n                            )}\n                          </div>\n                        </div>\n                      </div>\n\n                      <DropdownMenu>\n                        <DropdownMenuTrigger asChild>\n                          <Button variant=\"ghost\" size=\"sm\">\n                            <MoreHorizontal className=\"h-4 w-4\" />\n                          </Button>\n                        </DropdownMenuTrigger>\n                        <DropdownMenuContent align=\"end\">\n                          <DropdownMenuItem\n                            onClick={() => handleEditStart(brand)}\n                          >\n                            <Edit className=\"mr-2 h-4 w-4\" />\n                            Edit\n                          </DropdownMenuItem>\n                          <DropdownMenuItem>\n                            <Eye className=\"mr-2 h-4 w-4\" />\n                            View Products\n                          </DropdownMenuItem>\n                          {brand.website && (\n                            <DropdownMenuItem asChild>\n                              <a\n                                href={brand.website}\n                                target=\"_blank\"\n                                rel=\"noopener noreferrer\"\n                              >\n                                <ExternalLink className=\"mr-2 h-4 w-4\" />\n                                Visit Website\n                              </a>\n                            </DropdownMenuItem>\n                          )}\n                          <DropdownMenuItem>\n                            <Copy className=\"mr-2 h-4 w-4\" />\n                            Duplicate\n                          </DropdownMenuItem>\n                          <DropdownMenuSeparator />\n                          <DropdownMenuItem>\n                            <Archive className=\"mr-2 h-4 w-4\" />\n                            Archive\n                          </DropdownMenuItem>\n                          <DropdownMenuItem\n                            onClick={() => handleDeleteBrand(brand.id)}\n                            className=\"text-red-600\"\n                          >\n                            <Trash2 className=\"mr-2 h-4 w-4\" />\n                            Delete\n                          </DropdownMenuItem>\n                        </DropdownMenuContent>\n                      </DropdownMenu>\n                    </div>\n\n                    <p className=\"line-clamp-2 text-sm text-gray-600\">\n                      {brand.description || \"No description provided\"}\n                    </p>\n\n                    <div className=\"space-y-2 text-xs text-gray-500\">\n                      <div className=\"flex items-center justify-between\">\n                        <span>{brand.productCount} products</span>\n                        <div className=\"flex items-center gap-1\">\n                          <Star className=\"h-3 w-3 fill-yellow-400 text-yellow-400\" />\n                          <span>{brand.rating.toFixed(1)}</span>\n                        </div>\n                      </div>\n\n                      {brand.country && (\n                        <div className=\"flex items-center gap-1\">\n                          <Globe className=\"h-3 w-3\" />\n                          <span>{brand.country}</span>\n                          {brand.foundedYear && (\n                            <span>• Est. {brand.foundedYear}</span>\n                          )}\n                        </div>\n                      )}\n\n                      {brand.website && (\n                        <div className=\"flex items-center gap-1\">\n                          <ExternalLink className=\"h-3 w-3\" />\n                          <a\n                            href={brand.website}\n                            target=\"_blank\"\n                            rel=\"noopener noreferrer\"\n                            className=\"truncate text-blue-600 hover:underline\"\n                          >\n                            {brand.website.replace(/^https?:\\/\\//, \"\")}\n                          </a>\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                )}\n              </CardContent>\n            </Card>\n          ))}\n        </div>\n      ) : (\n        // List view - simplified for now\n        <Card>\n          <CardContent className=\"p-0\">\n            <div className=\"divide-y\">\n              {filteredAndSortedBrands.map((brand) => (\n                <div key={brand.id} className=\"p-4 hover:bg-gray-50\">\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"flex items-center gap-4\">\n                      <span className=\"text-2xl\">{brand.logo}</span>\n                      <div>\n                        <div className=\"flex items-center gap-2\">\n                          <h3 className=\"font-semibold\">{brand.name}</h3>\n                          <Badge\n                            variant={brand.isActive ? \"default\" : \"secondary\"}\n                            className=\"text-xs\"\n                          >\n                            {brand.isActive ? \"Active\" : \"Inactive\"}\n                          </Badge>\n                          {brand.isPremium && (\n                            <Badge\n                              variant=\"outline\"\n                              className=\"border-purple-200 text-xs text-purple-600\"\n                            >\n                              Premium\n                            </Badge>\n                          )}\n                        </div>\n                        <p className=\"text-sm text-gray-600\">\n                          {brand.description || \"No description\"}\n                        </p>\n                        <div className=\"mt-1 flex items-center gap-4 text-xs text-gray-500\">\n                          <span>{brand.productCount} products</span>\n                          <span>Rating: {brand.rating.toFixed(1)}</span>\n                          {brand.country && <span>{brand.country}</span>}\n                          {brand.foundedYear && (\n                            <span>Est. {brand.foundedYear}</span>\n                          )}\n                        </div>\n                      </div>\n                    </div>\n\n                    <div className=\"flex gap-2\">\n                      <Button\n                        size=\"sm\"\n                        variant=\"outline\"\n                        onClick={() => handleEditStart(brand)}\n                      >\n                        <Pencil className=\"h-4 w-4\" />\n                      </Button>\n                      <Button\n                        size=\"sm\"\n                        variant=\"outline\"\n                        onClick={() => handleDeleteBrand(brand.id)}\n                      >\n                        <Trash2 className=\"h-4 w-4\" />\n                      </Button>\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Empty State */}\n      {filteredAndSortedBrands.length === 0 && (\n        <Card>\n          <CardContent className=\"p-12 text-center\">\n            <Building2 className=\"mx-auto h-12 w-12 text-gray-400\" />\n            <h3 className=\"mt-2 text-lg font-medium text-gray-900\">\n              No brands found\n            </h3>\n            <p className=\"mt-1 text-sm text-gray-500\">\n              {searchQuery || filterStatus !== \"all\" || filterType !== \"all\"\n                ? \"Try adjusting your search or filter criteria.\"\n                : \"Get started by adding your first brand.\"}\n            </p>\n            {!searchQuery && filterStatus === \"all\" && filterType === \"all\" && (\n              <Button className=\"mt-4\" onClick={() => setShowAddForm(true)}>\n                <Plus className=\"mr-2 h-4 w-4\" />\n                Add Your First Brand\n              </Button>\n            )}\n          </CardContent>\n        </Card>\n      )}\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA4BA;AAEA;AACA;AACA;AACA;AAOA;AACA;AACA;AAOA;AACA;AAtDA;;;;;;;;;;;;;;AA2EA,iCAAiC;AACjC,MAAM,aAAsB;IAC1B;QACE,IAAI;QACJ,MAAM;QACN,aACE;QACF,MAAM;QACN,MAAM;QACN,SAAS;QACT,OAAO;QACP,SAAS;QACT,aAAa;QACb,UAAU;QACV,WAAW;QACX,cAAc;QACd,QAAQ;QACR,WAAW;QACX,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,aACE;QACF,MAAM;QACN,MAAM;QACN,SAAS;QACT,OAAO;QACP,SAAS;QACT,aAAa;QACb,UAAU;QACV,WAAW;QACX,cAAc;QACd,QAAQ;QACR,WAAW;QACX,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,MAAM;QACN,SAAS;QACT,OAAO;QACP,SAAS;QACT,aAAa;QACb,UAAU;QACV,WAAW;QACX,cAAc;QACd,QAAQ;QACR,WAAW;QACX,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,aACE;QACF,MAAM;QACN,MAAM;QACN,SAAS;QACT,OAAO;QACP,SAAS;QACT,aAAa;QACb,UAAU;QACV,WAAW;QACX,cAAc;QACd,QAAQ;QACR,WAAW;QACX,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,aACE;QACF,MAAM;QACN,MAAM;QACN,SAAS;QACT,OAAO;QACP,SAAS;QACT,aAAa;QACb,UAAU;QACV,WAAW;QACX,cAAc;QACd,QAAQ;QACR,WAAW;QACX,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,aACE;QACF,MAAM;QACN,MAAM;QACN,SAAS;QACT,OAAO;QACP,SAAS;QACT,aAAa;QACb,UAAU;QACV,WAAW;QACX,cAAc;QACd,QAAQ;QACR,WAAW;QACX,WAAW;QACX,WAAW;IACb;CACD;AAUM,MAAM,uBAAuB,CAAC,EACnC,gBAAgB,UAAU,EAC1B,cAAc,EACI;IAClB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAW;IAC9C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAkB;QACvD,MAAM;QACN,aAAa;QACb,MAAM;QACN,SAAS;QACT,OAAO;QACP,SAAS;QACT,aAAa;QACb,UAAU;QACV,WAAW;IACb;IACA,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAkB,CAAC;IAC1D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAkB;IAC3D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAmB;IAC1D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,sCAAsC;IACtC,MAAM,eAAe,CAAC;QACpB,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,eAAe,KACvB,OAAO,CAAC,UAAU;IACvB;IAEA,yBAAyB;IACzB,MAAM,0BAA0B,OAC7B,MAAM,CAAC,CAAC;QACP,MAAM,gBACJ,MAAM,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OACzD,MAAM,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OAChE,MAAM,OAAO,EAAE,cAAc,SAAS,YAAY,WAAW;QAE/D,MAAM,gBACJ,iBAAiB,SAChB,iBAAiB,YAAY,MAAM,QAAQ,IAC3C,iBAAiB,cAAc,CAAC,MAAM,QAAQ;QAEjD,MAAM,cACJ,eAAe,SACd,eAAe,aAAa,MAAM,SAAS,IAC3C,eAAe,cAAc,CAAC,MAAM,SAAS;QAEhD,OAAO,iBAAiB,iBAAiB;IAC3C,GACC,IAAI,CAAC,CAAC,GAAG;QACR,IAAI,aAAa;QACjB,OAAQ;YACN,KAAK;gBACH,aAAa,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,IAAI;gBACxC;YACF,KAAK;gBACH,aAAa,EAAE,YAAY,GAAG,EAAE,YAAY;gBAC5C;YACF,KAAK;gBACH,aAAa,EAAE,MAAM,GAAG,EAAE,MAAM;gBAChC;YACF,KAAK;gBACH,aAAa,CAAC,EAAE,WAAW,IAAI,CAAC,IAAI,CAAC,EAAE,WAAW,IAAI,CAAC;gBACvD;YACF,KAAK;gBACH,aACE,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO;gBACjE;YACF;gBACE,aAAa,EAAE,SAAS,GAAG,EAAE,SAAS;QAC1C;QACA,OAAO,cAAc,QAAQ,aAAa,CAAC;IAC7C;IAEF,kBAAkB;IAClB,MAAM,iBAAiB;QACrB,IAAI,CAAC,SAAS,IAAI,EAAE;YAClB,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,MAAM,OAAO,aAAa,SAAS,IAAI;QAEvC,+BAA+B;QAC/B,IAAI,OAAO,IAAI,CAAC,CAAC,QAAU,MAAM,IAAI,KAAK,OAAO;YAC/C,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,MAAM,iBAAwB;YAC5B,IAAI,CAAC,MAAM,EAAE,KAAK,GAAG,IAAI;YACzB,MAAM,SAAS,IAAI;YACnB,aAAa,SAAS,WAAW,IAAI;YACrC;YACA,MAAM,SAAS,IAAI,IAAI;YACvB,SAAS,SAAS,OAAO,IAAI;YAC7B,OAAO,SAAS,KAAK,IAAI;YACzB,SAAS,SAAS,OAAO,IAAI;YAC7B,aAAa,SAAS,WAAW;YACjC,UAAU,SAAS,QAAQ,IAAI;YAC/B,WAAW,SAAS,SAAS,IAAI;YACjC,cAAc;YACd,QAAQ;YACR,WAAW,OAAO,MAAM,GAAG;YAC3B,WAAW,IAAI,OAAO,WAAW;YACjC,WAAW,IAAI,OAAO,WAAW;QACnC;QAEA,MAAM,gBAAgB;eAAI;YAAQ;SAAe;QACjD,UAAU;QACV,YAAY;YACV,MAAM;YACN,aAAa;YACb,MAAM;YACN,SAAS;YACT,OAAO;YACP,SAAS;YACT,aAAa;YACb,UAAU;YACV,WAAW;QACb;QACA,eAAe;QAEf,0BAA0B;QAC1B,IAAI,gBAAgB;YAClB,eAAe;QACjB;QAEA,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,wBAAwB;IACxB,MAAM,kBAAkB,CAAC;QACvB,kBAAkB,MAAM,EAAE;QAC1B,YAAY;YAAE,GAAG,KAAK;QAAC;IACzB;IAEA,iBAAiB;IACjB,MAAM,mBAAmB;QACvB,kBAAkB;QAClB,YAAY,CAAC;IACf;IAEA,oBAAoB;IACpB,MAAM,iBAAiB;QACrB,IAAI,CAAC,SAAS,IAAI,EAAE;YAClB,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,MAAM,gBAAgB,OAAO,GAAG,CAAC,CAAC,QAChC,MAAM,EAAE,KAAK,iBACT;gBACE,GAAG,KAAK;gBACR,MAAM,SAAS,IAAI,IAAI,MAAM,IAAI;gBACjC,aAAa,SAAS,WAAW,IAAI,MAAM,WAAW;gBACtD,MAAM,SAAS,IAAI,IAAI,MAAM,IAAI;gBACjC,SAAS,SAAS,OAAO,IAAI,MAAM,OAAO;gBAC1C,OAAO,SAAS,KAAK,IAAI,MAAM,KAAK;gBACpC,SAAS,SAAS,OAAO,IAAI,MAAM,OAAO;gBAC1C,aAAa,SAAS,WAAW,IAAI,MAAM,WAAW;gBACtD,UAAU,SAAS,QAAQ,IAAI,MAAM,QAAQ;gBAC7C,WAAW,SAAS,SAAS,IAAI,MAAM,SAAS;gBAChD,mCAAmC;gBACnC,MACE,MAAM,IAAI,KAAK,SAAS,IAAI,GACxB,aAAa,SAAS,IAAI,IAC1B,MAAM,IAAI;gBAChB,WAAW,IAAI,OAAO,WAAW;YACnC,IACA;QAGN,UAAU;QACV,kBAAkB;QAClB,YAAY,CAAC;QAEb,0BAA0B;QAC1B,IAAI,gBAAgB;YAClB,eAAe;QACjB;QAEA,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,iBAAiB;IACjB,MAAM,oBAAoB,CAAC;QACzB,MAAM,gBAAgB,OAAO,MAAM,CAAC,CAAC,QAAU,MAAM,EAAE,KAAK;QAC5D,UAAU;QAEV,0BAA0B;QAC1B,IAAI,gBAAgB;YAClB,eAAe;QACjB;QAEA,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,qBACE,6WAAC;QAAI,WAAU;;0BAEb,6WAAC;gBAAI,WAAU;;kCACb,6WAAC,yHAAA,CAAA,OAAI;kCACH,cAAA,6WAAC,yHAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;kDACb,cAAA,6WAAC,oRAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;;;;;;kDAEjB,6WAAC;;0DACC,6WAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,6WAAC;gDAAE,WAAU;0DAAsB,OAAO,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAMxD,6WAAC,yHAAA,CAAA,OAAI;kCACH,cAAA,6WAAC,yHAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;kDACb,cAAA,6WAAC,sSAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;kDAExB,6WAAC;;0DACC,6WAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,6WAAC;gDAAE,WAAU;0DACV,OAAO,MAAM,CAAC,CAAC,IAAM,EAAE,QAAQ,EAAE,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOlD,6WAAC,yHAAA,CAAA,OAAI;kCACH,cAAA,6WAAC,yHAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;kDACb,cAAA,6WAAC,wRAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;kDAEnB,6WAAC;;0DACC,6WAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,6WAAC;gDAAE,WAAU;0DACV,OAAO,MAAM,CAAC,CAAC,IAAM,EAAE,SAAS,EAAE,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOnD,6WAAC,yHAAA,CAAA,OAAI;kCACH,cAAA,6WAAC,yHAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;kDACb,cAAA,6WAAC,sRAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;kDAElB,6WAAC;;0DACC,6WAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,6WAAC;gDAAE,WAAU;0DACV,OAAO,MAAM,GAAG,IACb,CACE,OAAO,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,MAAM,EAAE,KAC1C,OAAO,MAAM,AACf,EAAE,OAAO,CAAC,KACV;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAShB,6WAAC,yHAAA,CAAA,OAAI;0BACH,cAAA,6WAAC,yHAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,6WAAC;wBAAI,WAAU;;0CAEb,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,0RAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,6WAAC,0HAAA,CAAA,QAAK;gDACJ,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gDAC9C,WAAU;;;;;;;;;;;;kDAId,6WAAC,2HAAA,CAAA,SAAM;wCAAC,OAAO;wCAAc,eAAe;;0DAC1C,6WAAC,2HAAA,CAAA,gBAAa;gDAAC,WAAU;0DACvB,cAAA,6WAAC,2HAAA,CAAA,cAAW;;;;;;;;;;0DAEd,6WAAC,2HAAA,CAAA,gBAAa;;kEACZ,6WAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAM;;;;;;kEACxB,6WAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAS;;;;;;kEAC3B,6WAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAW;;;;;;;;;;;;;;;;;;kDAIjC,6WAAC,2HAAA,CAAA,SAAM;wCAAC,OAAO;wCAAY,eAAe;;0DACxC,6WAAC,2HAAA,CAAA,gBAAa;gDAAC,WAAU;0DACvB,cAAA,6WAAC,2HAAA,CAAA,cAAW;;;;;;;;;;0DAEd,6WAAC,2HAAA,CAAA,gBAAa;;kEACZ,6WAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAM;;;;;;kEACxB,6WAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAU;;;;;;kEAC5B,6WAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAW;;;;;;;;;;;;;;;;;;kDAIjC,6WAAC,2HAAA,CAAA,SAAM;wCAAC,OAAO;wCAAQ,eAAe;;0DACpC,6WAAC,2HAAA,CAAA,gBAAa;gDAAC,WAAU;0DACvB,cAAA,6WAAC,2HAAA,CAAA,cAAW;;;;;;;;;;0DAEd,6WAAC,2HAAA,CAAA,gBAAa;;kEACZ,6WAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAO;;;;;;kEACzB,6WAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAW;;;;;;kEAC7B,6WAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAS;;;;;;kEAC3B,6WAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAU;;;;;;kEAC5B,6WAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAU;;;;;;;;;;;;;;;;;;;;;;;;0CAMlC,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,2HAAA,CAAA,SAAM;wCACL,SAAS,cAAc,QAAQ,YAAY;wCAC3C,MAAK;wCACL,SAAS,IACP,aAAa,cAAc,QAAQ,SAAS;kDAG7C,cAAc,sBACb,6WAAC,kTAAA,CAAA,UAAO;4CAAC,WAAU;;;;;iEAEnB,6WAAC,qTAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;kDAIxB,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,2HAAA,CAAA,SAAM;gDACL,SAAS,aAAa,SAAS,YAAY;gDAC3C,MAAK;gDACL,SAAS,IAAM,YAAY;gDAC3B,WAAU;0DAEV,cAAA,6WAAC,gSAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;;;;;;0DAErB,6WAAC,2HAAA,CAAA,SAAM;gDACL,SAAS,aAAa,SAAS,YAAY;gDAC3C,MAAK;gDACL,SAAS,IAAM,YAAY;gDAC3B,WAAU;0DAEV,cAAA,6WAAC,sRAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAIpB,6WAAC,2HAAA,CAAA,SAAM;wCAAC,SAAS,IAAM,eAAe;;0DACpC,6WAAC,sRAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAS1C,6BACC,6WAAC,yHAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6WAAC,yHAAA,CAAA,aAAU;kCACT,cAAA,6WAAC,yHAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6WAAC,sRAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAA2B;;;;;;;;;;;;kCAI/C,6WAAC,yHAAA,CAAA,cAAW;;0CACV,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;;0DACC,6WAAC,0HAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAiB;;;;;;0DAChC,6WAAC,0HAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,OAAO,SAAS,IAAI;gDACpB,UAAU,CAAC,IACT,YAAY;wDAAE,GAAG,QAAQ;wDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;oDAAC;gDAElD,aAAY;gDACZ,WAAU;;;;;;;;;;;;kDAId,6WAAC;;0DACC,6WAAC,0HAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAiB;;;;;;0DAChC,6WAAC,0HAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,OAAO,SAAS,IAAI;gDACpB,UAAU,CAAC,IACT,YAAY;wDAAE,GAAG,QAAQ;wDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;oDAAC;gDAElD,aAAY;gDACZ,WAAU;;;;;;;;;;;;kDAId,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,0HAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAwB;;;;;;0DACvC,6WAAC,6HAAA,CAAA,WAAQ;gDACP,IAAG;gDACH,OAAO,SAAS,WAAW;gDAC3B,UAAU,CAAC,IACT,YAAY;wDAAE,GAAG,QAAQ;wDAAE,aAAa,EAAE,MAAM,CAAC,KAAK;oDAAC;gDAEzD,aAAY;gDACZ,WAAU;gDACV,MAAM;;;;;;;;;;;;kDAIV,6WAAC;;0DACC,6WAAC,0HAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAoB;;;;;;0DACnC,6WAAC,0HAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,OAAO,SAAS,OAAO;gDACvB,UAAU,CAAC,IACT,YAAY;wDAAE,GAAG,QAAQ;wDAAE,SAAS,EAAE,MAAM,CAAC,KAAK;oDAAC;gDAErD,aAAY;gDACZ,WAAU;;;;;;;;;;;;kDAId,6WAAC;;0DACC,6WAAC,0HAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAkB;;;;;;0DACjC,6WAAC,0HAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,OAAO,SAAS,KAAK;gDACrB,UAAU,CAAC,IACT,YAAY;wDAAE,GAAG,QAAQ;wDAAE,OAAO,EAAE,MAAM,CAAC,KAAK;oDAAC;gDAEnD,aAAY;gDACZ,WAAU;;;;;;;;;;;;kDAId,6WAAC;;0DACC,6WAAC,0HAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAoB;;;;;;0DACnC,6WAAC,0HAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,OAAO,SAAS,OAAO;gDACvB,UAAU,CAAC,IACT,YAAY;wDAAE,GAAG,QAAQ;wDAAE,SAAS,EAAE,MAAM,CAAC,KAAK;oDAAC;gDAErD,aAAY;gDACZ,WAAU;;;;;;;;;;;;kDAId,6WAAC;;0DACC,6WAAC,0HAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAoB;;;;;;0DACnC,6WAAC,0HAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,KAAI;gDACJ,KAAK,IAAI,OAAO,WAAW;gDAC3B,OAAO,SAAS,WAAW,IAAI;gDAC/B,UAAU,CAAC,IACT,YAAY;wDACV,GAAG,QAAQ;wDACX,aAAa,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;oDAC3C;gDAEF,aAAY;gDACZ,WAAU;;;;;;;;;;;;kDAId,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,2HAAA,CAAA,SAAM;gDACL,IAAG;gDACH,SAAS,SAAS,QAAQ;gDAC1B,iBAAiB,CAAC,UAChB,YAAY;wDAAE,GAAG,QAAQ;wDAAE,UAAU;oDAAQ;;;;;;0DAGjD,6WAAC,0HAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAmB;;;;;;;;;;;;kDAGpC,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,2HAAA,CAAA,SAAM;gDACL,IAAG;gDACH,SAAS,SAAS,SAAS;gDAC3B,iBAAiB,CAAC,UAChB,YAAY;wDAAE,GAAG,QAAQ;wDAAE,WAAW;oDAAQ;;;;;;0DAGlD,6WAAC,0HAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAoB;;;;;;;;;;;;;;;;;;0CAIvC,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,2HAAA,CAAA,SAAM;wCAAC,SAAS;;0DACf,6WAAC,sRAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGnC,6WAAC,2HAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,SAAS,IAAM,eAAe;;0DACtD,6WAAC,gRAAA,CAAA,IAAC;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;YASvC,aAAa,uBACZ,6WAAC;gBAAI,WAAU;0BACZ,wBAAwB,GAAG,CAAC,CAAC,sBAC5B,6WAAC,yHAAA,CAAA,OAAI;wBAAgB,WAAU;kCAC7B,cAAA,6WAAC,yHAAA,CAAA,cAAW;4BAAC,WAAU;sCACpB,mBAAmB,MAAM,EAAE,GAC1B,YAAY;0CACZ,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,0HAAA,CAAA,QAAK;wCACJ,OAAO,SAAS,IAAI,IAAI;wCACxB,UAAU,CAAC,IACT,YAAY;gDAAE,GAAG,QAAQ;gDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;4CAAC;wCAElD,aAAY;;;;;;kDAEd,6WAAC,6HAAA,CAAA,WAAQ;wCACP,OAAO,SAAS,WAAW,IAAI;wCAC/B,UAAU,CAAC,IACT,YAAY;gDACV,GAAG,QAAQ;gDACX,aAAa,EAAE,MAAM,CAAC,KAAK;4CAC7B;wCAEF,aAAY;wCACZ,MAAM;;;;;;kDAER,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,2HAAA,CAAA,SAAM;gDAAC,MAAK;gDAAK,SAAS;;kEACzB,6WAAC,sRAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGnC,6WAAC,2HAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,SAAS;0DAET,cAAA,6WAAC,gRAAA,CAAA,IAAC;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;uCAKnB,eAAe;0CACf,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;;0DACb,6WAAC;gDAAI,WAAU;;kEACb,6WAAC;wDAAK,WAAU;kEAAY,MAAM,IAAI;;;;;;kEACtC,6WAAC;;0EACC,6WAAC;gEAAG,WAAU;0EAAiB,MAAM,IAAI;;;;;;0EACzC,6WAAC;gEAAI,WAAU;;kFACb,6WAAC,0HAAA,CAAA,QAAK;wEACJ,SAAS,MAAM,QAAQ,GAAG,YAAY;wEACtC,WAAU;kFAET,MAAM,QAAQ,GAAG,WAAW;;;;;;oEAE9B,MAAM,SAAS,kBACd,6WAAC,0HAAA,CAAA,QAAK;wEACJ,SAAQ;wEACR,WAAU;kFACX;;;;;;;;;;;;;;;;;;;;;;;;0DAQT,6WAAC,qIAAA,CAAA,eAAY;;kEACX,6WAAC,qIAAA,CAAA,sBAAmB;wDAAC,OAAO;kEAC1B,cAAA,6WAAC,2HAAA,CAAA,SAAM;4DAAC,SAAQ;4DAAQ,MAAK;sEAC3B,cAAA,6WAAC,oSAAA,CAAA,iBAAc;gEAAC,WAAU;;;;;;;;;;;;;;;;kEAG9B,6WAAC,qIAAA,CAAA,sBAAmB;wDAAC,OAAM;;0EACzB,6WAAC,qIAAA,CAAA,mBAAgB;gEACf,SAAS,IAAM,gBAAgB;;kFAE/B,6WAAC,+RAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAGnC,6WAAC,qIAAA,CAAA,mBAAgB;;kFACf,6WAAC,oRAAA,CAAA,MAAG;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;4DAGjC,MAAM,OAAO,kBACZ,6WAAC,qIAAA,CAAA,mBAAgB;gEAAC,OAAO;0EACvB,cAAA,6WAAC;oEACC,MAAM,MAAM,OAAO;oEACnB,QAAO;oEACP,KAAI;;sFAEJ,6WAAC,0SAAA,CAAA,eAAY;4EAAC,WAAU;;;;;;wEAAiB;;;;;;;;;;;;0EAK/C,6WAAC,qIAAA,CAAA,mBAAgB;;kFACf,6WAAC,sRAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAGnC,6WAAC,qIAAA,CAAA,wBAAqB;;;;;0EACtB,6WAAC,qIAAA,CAAA,mBAAgB;;kFACf,6WAAC,4RAAA,CAAA,UAAO;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAGtC,6WAAC,qIAAA,CAAA,mBAAgB;gEACf,SAAS,IAAM,kBAAkB,MAAM,EAAE;gEACzC,WAAU;;kFAEV,6WAAC,8RAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;kDAO3C,6WAAC;wCAAE,WAAU;kDACV,MAAM,WAAW,IAAI;;;;;;kDAGxB,6WAAC;wCAAI,WAAU;;0DACb,6WAAC;gDAAI,WAAU;;kEACb,6WAAC;;4DAAM,MAAM,YAAY;4DAAC;;;;;;;kEAC1B,6WAAC;wDAAI,WAAU;;0EACb,6WAAC,sRAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,6WAAC;0EAAM,MAAM,MAAM,CAAC,OAAO,CAAC;;;;;;;;;;;;;;;;;;4CAI/B,MAAM,OAAO,kBACZ,6WAAC;gDAAI,WAAU;;kEACb,6WAAC,wRAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,6WAAC;kEAAM,MAAM,OAAO;;;;;;oDACnB,MAAM,WAAW,kBAChB,6WAAC;;4DAAK;4DAAQ,MAAM,WAAW;;;;;;;;;;;;;4CAKpC,MAAM,OAAO,kBACZ,6WAAC;gDAAI,WAAU;;kEACb,6WAAC,0SAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;kEACxB,6WAAC;wDACC,MAAM,MAAM,OAAO;wDACnB,QAAO;wDACP,KAAI;wDACJ,WAAU;kEAET,MAAM,OAAO,CAAC,OAAO,CAAC,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uBAjJ5C,MAAM,EAAE;;;;;;;;;uBA6JvB,iCAAiC;0BACjC,6WAAC,yHAAA,CAAA,OAAI;0BACH,cAAA,6WAAC,yHAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,6WAAC;wBAAI,WAAU;kCACZ,wBAAwB,GAAG,CAAC,CAAC,sBAC5B,6WAAC;gCAAmB,WAAU;0CAC5B,cAAA,6WAAC;oCAAI,WAAU;;sDACb,6WAAC;4CAAI,WAAU;;8DACb,6WAAC;oDAAK,WAAU;8DAAY,MAAM,IAAI;;;;;;8DACtC,6WAAC;;sEACC,6WAAC;4DAAI,WAAU;;8EACb,6WAAC;oEAAG,WAAU;8EAAiB,MAAM,IAAI;;;;;;8EACzC,6WAAC,0HAAA,CAAA,QAAK;oEACJ,SAAS,MAAM,QAAQ,GAAG,YAAY;oEACtC,WAAU;8EAET,MAAM,QAAQ,GAAG,WAAW;;;;;;gEAE9B,MAAM,SAAS,kBACd,6WAAC,0HAAA,CAAA,QAAK;oEACJ,SAAQ;oEACR,WAAU;8EACX;;;;;;;;;;;;sEAKL,6WAAC;4DAAE,WAAU;sEACV,MAAM,WAAW,IAAI;;;;;;sEAExB,6WAAC;4DAAI,WAAU;;8EACb,6WAAC;;wEAAM,MAAM,YAAY;wEAAC;;;;;;;8EAC1B,6WAAC;;wEAAK;wEAAS,MAAM,MAAM,CAAC,OAAO,CAAC;;;;;;;gEACnC,MAAM,OAAO,kBAAI,6WAAC;8EAAM,MAAM,OAAO;;;;;;gEACrC,MAAM,WAAW,kBAChB,6WAAC;;wEAAK;wEAAM,MAAM,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;sDAMrC,6WAAC;4CAAI,WAAU;;8DACb,6WAAC,2HAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,SAAS,IAAM,gBAAgB;8DAE/B,cAAA,6WAAC,0RAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;;;;;;8DAEpB,6WAAC,2HAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,SAAS,IAAM,kBAAkB,MAAM,EAAE;8DAEzC,cAAA,6WAAC,8RAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;+BAjDhB,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;YA6D3B,wBAAwB,MAAM,KAAK,mBAClC,6WAAC,yHAAA,CAAA,OAAI;0BACH,cAAA,6WAAC,yHAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,6WAAC,oSAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;sCACrB,6WAAC;4BAAG,WAAU;sCAAyC;;;;;;sCAGvD,6WAAC;4BAAE,WAAU;sCACV,eAAe,iBAAiB,SAAS,eAAe,QACrD,kDACA;;;;;;wBAEL,CAAC,eAAe,iBAAiB,SAAS,eAAe,uBACxD,6WAAC,2HAAA,CAAA,SAAM;4BAAC,WAAU;4BAAO,SAAS,IAAM,eAAe;;8CACrD,6WAAC,sRAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;;;;;;;AASjD", "debugId": null}}, {"offset": {"line": 2442, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/pages/management/CategoryManagerEnhanced.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useEffect, useState } from \"react\";\n\nimport {\n  Archive,\n  Copy,\n  Edit,\n  Eye,\n  Filter,\n  FolderOpen,\n  Grid3X3,\n  LayoutGrid,\n  List,\n  MoreHorizontal,\n  Package,\n  Pencil,\n  Plus,\n  Save,\n  Search,\n  SortAsc,\n  SortDesc,\n  Sparkles,\n  Tag,\n  Trash2,\n  TrendingUp,\n  X,\n} from \"lucide-react\";\nimport { toast } from \"sonner\";\n\nimport { Badge } from \"@/components/ui/badge\";\nimport { Button } from \"@/components/ui/button\";\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from \"@/components/ui/dropdown-menu\";\nimport { Input } from \"@/components/ui/input\";\nimport { Label } from \"@/components/ui/label\";\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from \"@/components/ui/select\";\nimport { Switch } from \"@/components/ui/switch\";\nimport { Textarea } from \"@/components/ui/textarea\";\nimport {\n  CategoryApi,\n  CreateCategoryDto,\n  UpdateCategoryDto,\n} from \"@/lib/api/categoryApi\";\n\nexport type Category = {\n  id: string;\n  name: string;\n  description: string;\n  slug: string;\n  icon?: string;\n  color?: string;\n  isActive: boolean;\n  productCount: number;\n  parentId?: string;\n  sortOrder: number;\n  createdAt: string;\n  updatedAt: string;\n};\n\n// Mock categories with enhanced data\nconst mockCategories: Category[] = [\n  {\n    id: \"cat-1\",\n    name: \"Electronics\",\n    description:\n      \"Electronic devices, gadgets, and accessories for modern living\",\n    slug: \"electronics\",\n    icon: \"📱\",\n    color: \"#3B82F6\",\n    isActive: true,\n    productCount: 324,\n    sortOrder: 1,\n    createdAt: \"2024-01-01T00:00:00Z\",\n    updatedAt: \"2024-01-15T10:30:00Z\",\n  },\n  {\n    id: \"cat-2\",\n    name: \"Clothing & Fashion\",\n    description:\n      \"Trendy clothing, shoes, and fashion accessories for all occasions\",\n    slug: \"clothing-fashion\",\n    icon: \"👕\",\n    color: \"#EF4444\",\n    isActive: true,\n    productCount: 289,\n    sortOrder: 2,\n    createdAt: \"2024-01-02T00:00:00Z\",\n    updatedAt: \"2024-01-14T16:45:00Z\",\n  },\n  {\n    id: \"cat-3\",\n    name: \"Home & Garden\",\n    description: \"Furniture, decor, and garden supplies to beautify your space\",\n    slug: \"home-garden\",\n    icon: \"🏠\",\n    color: \"#10B981\",\n    isActive: true,\n    productCount: 156,\n    sortOrder: 3,\n    createdAt: \"2024-01-03T00:00:00Z\",\n    updatedAt: \"2024-01-13T09:20:00Z\",\n  },\n  {\n    id: \"cat-4\",\n    name: \"Sports & Outdoors\",\n    description:\n      \"Equipment and gear for sports, fitness, and outdoor adventures\",\n    slug: \"sports-outdoors\",\n    icon: \"⚽\",\n    color: \"#F59E0B\",\n    isActive: true,\n    productCount: 198,\n    sortOrder: 4,\n    createdAt: \"2024-01-04T00:00:00Z\",\n    updatedAt: \"2024-01-12T14:15:00Z\",\n  },\n  {\n    id: \"cat-5\",\n    name: \"Books & Media\",\n    description: \"Books, magazines, movies, and digital media content\",\n    slug: \"books-media\",\n    icon: \"📚\",\n    color: \"#8B5CF6\",\n    isActive: false,\n    productCount: 67,\n    sortOrder: 5,\n    createdAt: \"2024-01-05T00:00:00Z\",\n    updatedAt: \"2024-01-11T11:30:00Z\",\n  },\n  {\n    id: \"cat-6\",\n    name: \"Health & Beauty\",\n    description: \"Personal care, cosmetics, and wellness products\",\n    slug: \"health-beauty\",\n    icon: \"💄\",\n    color: \"#EC4899\",\n    isActive: true,\n    productCount: 143,\n    sortOrder: 6,\n    createdAt: \"2024-01-06T00:00:00Z\",\n    updatedAt: \"2024-01-10T08:45:00Z\",\n  },\n];\n\ntype CategoryManagerProps = {\n  initialCategories?: Category[];\n  onCategoriesChange?: (categories: Category[]) => void;\n};\n\n/**\n * Enhanced component for managing product categories with professional UI\n */\nexport const CategoryManagerEnhanced = ({\n  initialCategories = [],\n  onCategoriesChange,\n}: CategoryManagerProps) => {\n  const [categories, setCategories] = useState<Category[]>(initialCategories);\n  const [loading, setLoading] = useState(true);\n  const [saving, setSaving] = useState(false);\n  const [newCategory, setNewCategory] = useState<Partial<Category>>({\n    name: \"\",\n    description: \"\",\n    icon: \"\",\n    color: \"#3B82F6\",\n    isActive: true,\n  });\n  const [editingCategoryId, setEditingCategoryId] = useState<string | null>(\n    null\n  );\n  const [editForm, setEditForm] = useState<Partial<Category>>({});\n  const [searchQuery, setSearchQuery] = useState(\"\");\n  const [filterStatus, setFilterStatus] = useState(\"all\");\n  const [sortBy, setSortBy] = useState(\"name\");\n  const [sortOrder, setSortOrder] = useState<\"asc\" | \"desc\">(\"asc\");\n  const [viewMode, setViewMode] = useState<\"grid\" | \"list\">(\"grid\");\n  const [showAddForm, setShowAddForm] = useState(false);\n\n  // Generate a slug from the category name\n  const generateSlug = (name: string) => {\n    return name\n      .toLowerCase()\n      .replace(/[^a-z0-9]+/g, \"-\")\n      .replace(/^-|-$/g, \"\");\n  };\n\n  // Filter and sort categories\n  const filteredAndSortedCategories = categories\n    .filter((category) => {\n      const matchesSearch =\n        category.name.toLowerCase().includes(searchQuery.toLowerCase()) ||\n        category.description.toLowerCase().includes(searchQuery.toLowerCase());\n      const matchesFilter =\n        filterStatus === \"all\" ||\n        (filterStatus === \"active\" && category.isActive) ||\n        (filterStatus === \"inactive\" && !category.isActive);\n      return matchesSearch && matchesFilter;\n    })\n    .sort((a, b) => {\n      let comparison = 0;\n      switch (sortBy) {\n        case \"name\":\n          comparison = a.name.localeCompare(b.name);\n          break;\n        case \"products\":\n          comparison = a.productCount - b.productCount;\n          break;\n        case \"created\":\n          comparison =\n            new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();\n          break;\n        default:\n          comparison = a.sortOrder - b.sortOrder;\n      }\n      return sortOrder === \"asc\" ? comparison : -comparison;\n    });\n\n  // Add a new category\n  const handleAddCategory = () => {\n    if (!newCategory.name) {\n      toast.error(\"Category name is required\");\n      return;\n    }\n\n    const slug = generateSlug(newCategory.name);\n\n    // Check if slug already exists\n    if (categories.some((cat) => cat.slug === slug)) {\n      toast.error(\"A category with this name already exists\");\n      return;\n    }\n\n    const newCategoryWithId: Category = {\n      id: `category-${Date.now()}`,\n      name: newCategory.name,\n      description: newCategory.description || \"\",\n      slug,\n      icon: newCategory.icon || \"📦\",\n      color: newCategory.color || \"#3B82F6\",\n      isActive: newCategory.isActive ?? true,\n      productCount: 0,\n      sortOrder: categories.length + 1,\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString(),\n    };\n\n    const updatedCategories = [...categories, newCategoryWithId];\n    setCategories(updatedCategories);\n    setNewCategory({\n      name: \"\",\n      description: \"\",\n      icon: \"\",\n      color: \"#3B82F6\",\n      isActive: true,\n    });\n    setShowAddForm(false);\n\n    // Notify parent component\n    if (onCategoriesChange) {\n      onCategoriesChange(updatedCategories);\n    }\n\n    toast.success(\"Category added successfully\");\n  };\n\n  // Start editing a category\n  const handleEditStart = (category: Category) => {\n    setEditingCategoryId(category.id);\n    setEditForm({ ...category });\n  };\n\n  // Cancel editing\n  const handleEditCancel = () => {\n    setEditingCategoryId(null);\n    setEditForm({});\n  };\n\n  // Save edited category\n  const handleEditSave = () => {\n    if (!editForm.name) {\n      toast.error(\"Category name is required\");\n      return;\n    }\n\n    const updatedCategories = categories.map((cat) =>\n      cat.id === editingCategoryId\n        ? {\n            ...cat,\n            name: editForm.name || cat.name,\n            description: editForm.description || cat.description,\n            icon: editForm.icon || cat.icon,\n            color: editForm.color || cat.color,\n            isActive: editForm.isActive ?? cat.isActive,\n            // Only update slug if name changed\n            slug:\n              cat.name !== editForm.name\n                ? generateSlug(editForm.name)\n                : cat.slug,\n            updatedAt: new Date().toISOString(),\n          }\n        : cat\n    );\n\n    setCategories(updatedCategories);\n    setEditingCategoryId(null);\n    setEditForm({});\n\n    // Notify parent component\n    if (onCategoriesChange) {\n      onCategoriesChange(updatedCategories);\n    }\n\n    toast.success(\"Category updated successfully\");\n  };\n\n  // Delete a category\n  const handleDeleteCategory = (categoryId: string) => {\n    const updatedCategories = categories.filter((cat) => cat.id !== categoryId);\n    setCategories(updatedCategories);\n\n    // Notify parent component\n    if (onCategoriesChange) {\n      onCategoriesChange(updatedCategories);\n    }\n\n    toast.success(\"Category deleted successfully\");\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header with Statistics */}\n      <div className=\"grid gap-4 md:grid-cols-4\">\n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center gap-3\">\n              <div className=\"rounded-full bg-blue-100 p-2\">\n                <LayoutGrid className=\"h-5 w-5 text-blue-600\" />\n              </div>\n              <div>\n                <p className=\"text-sm text-gray-600\">Total Categories</p>\n                <p className=\"text-2xl font-bold\">{categories.length}</p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center gap-3\">\n              <div className=\"rounded-full bg-green-100 p-2\">\n                <TrendingUp className=\"h-5 w-5 text-green-600\" />\n              </div>\n              <div>\n                <p className=\"text-sm text-gray-600\">Active Categories</p>\n                <p className=\"text-2xl font-bold\">\n                  {categories.filter((c) => c.isActive).length}\n                </p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center gap-3\">\n              <div className=\"rounded-full bg-orange-100 p-2\">\n                <Package className=\"h-5 w-5 text-orange-600\" />\n              </div>\n              <div>\n                <p className=\"text-sm text-gray-600\">Total Products</p>\n                <p className=\"text-2xl font-bold\">\n                  {categories.reduce((sum, c) => sum + c.productCount, 0)}\n                </p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center gap-3\">\n              <div className=\"rounded-full bg-purple-100 p-2\">\n                <Sparkles className=\"h-5 w-5 text-purple-600\" />\n              </div>\n              <div>\n                <p className=\"text-sm text-gray-600\">Avg Products</p>\n                <p className=\"text-2xl font-bold\">\n                  {categories.length > 0\n                    ? Math.round(\n                        categories.reduce((sum, c) => sum + c.productCount, 0) /\n                          categories.length\n                      )\n                    : 0}\n                </p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* Controls */}\n      <Card>\n        <CardContent className=\"p-6\">\n          <div className=\"flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between\">\n            {/* Search and Filters */}\n            <div className=\"flex flex-1 gap-4\">\n              <div className=\"relative max-w-md flex-1\">\n                <Search className=\"absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400\" />\n                <Input\n                  placeholder=\"Search categories...\"\n                  value={searchQuery}\n                  onChange={(e) => setSearchQuery(e.target.value)}\n                  className=\"pl-10\"\n                />\n              </div>\n\n              <Select value={filterStatus} onValueChange={setFilterStatus}>\n                <SelectTrigger className=\"w-32\">\n                  <SelectValue />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"all\">All Status</SelectItem>\n                  <SelectItem value=\"active\">Active</SelectItem>\n                  <SelectItem value=\"inactive\">Inactive</SelectItem>\n                </SelectContent>\n              </Select>\n\n              <Select value={sortBy} onValueChange={setSortBy}>\n                <SelectTrigger className=\"w-32\">\n                  <SelectValue />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"name\">Name</SelectItem>\n                  <SelectItem value=\"products\">Products</SelectItem>\n                  <SelectItem value=\"created\">Created</SelectItem>\n                </SelectContent>\n              </Select>\n            </div>\n\n            {/* View Controls */}\n            <div className=\"flex items-center gap-2\">\n              <Button\n                variant={sortOrder === \"asc\" ? \"default\" : \"outline\"}\n                size=\"sm\"\n                onClick={() =>\n                  setSortOrder(sortOrder === \"asc\" ? \"desc\" : \"asc\")\n                }\n              >\n                {sortOrder === \"asc\" ? (\n                  <SortAsc className=\"h-4 w-4\" />\n                ) : (\n                  <SortDesc className=\"h-4 w-4\" />\n                )}\n              </Button>\n\n              <div className=\"flex rounded-md border\">\n                <Button\n                  variant={viewMode === \"grid\" ? \"default\" : \"ghost\"}\n                  size=\"sm\"\n                  onClick={() => setViewMode(\"grid\")}\n                  className=\"rounded-r-none\"\n                >\n                  <Grid3X3 className=\"h-4 w-4\" />\n                </Button>\n                <Button\n                  variant={viewMode === \"list\" ? \"default\" : \"ghost\"}\n                  size=\"sm\"\n                  onClick={() => setViewMode(\"list\")}\n                  className=\"rounded-l-none\"\n                >\n                  <List className=\"h-4 w-4\" />\n                </Button>\n              </div>\n\n              <Button onClick={() => setShowAddForm(true)}>\n                <Plus className=\"mr-2 h-4 w-4\" />\n                Add Category\n              </Button>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Add Category Modal */}\n      {showAddForm && (\n        <Card className=\"border-2 border-blue-200 bg-blue-50/50\">\n          <CardHeader>\n            <CardTitle className=\"flex items-center gap-2\">\n              <Plus className=\"h-5 w-5 text-blue-600\" />\n              Add New Category\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"grid gap-4 md:grid-cols-2\">\n              <div>\n                <Label htmlFor=\"new-category-name\">Category Name *</Label>\n                <Input\n                  id=\"new-category-name\"\n                  value={newCategory.name}\n                  onChange={(e) =>\n                    setNewCategory({ ...newCategory, name: e.target.value })\n                  }\n                  placeholder=\"e.g. Electronics\"\n                  className=\"mt-1\"\n                />\n              </div>\n\n              <div>\n                <Label htmlFor=\"new-category-icon\">Icon (Emoji)</Label>\n                <Input\n                  id=\"new-category-icon\"\n                  value={newCategory.icon}\n                  onChange={(e) =>\n                    setNewCategory({ ...newCategory, icon: e.target.value })\n                  }\n                  placeholder=\"📱\"\n                  className=\"mt-1\"\n                />\n              </div>\n\n              <div className=\"md:col-span-2\">\n                <Label htmlFor=\"new-category-description\">Description</Label>\n                <Textarea\n                  id=\"new-category-description\"\n                  value={newCategory.description}\n                  onChange={(e) =>\n                    setNewCategory({\n                      ...newCategory,\n                      description: e.target.value,\n                    })\n                  }\n                  placeholder=\"Describe what products belong in this category...\"\n                  className=\"mt-1\"\n                  rows={3}\n                />\n              </div>\n\n              <div>\n                <Label htmlFor=\"new-category-color\">Color</Label>\n                <Input\n                  id=\"new-category-color\"\n                  type=\"color\"\n                  value={newCategory.color}\n                  onChange={(e) =>\n                    setNewCategory({ ...newCategory, color: e.target.value })\n                  }\n                  className=\"mt-1 h-10\"\n                />\n              </div>\n\n              <div className=\"flex items-center space-x-2 pt-6\">\n                <Switch\n                  id=\"new-category-active\"\n                  checked={newCategory.isActive}\n                  onCheckedChange={(checked) =>\n                    setNewCategory({ ...newCategory, isActive: checked })\n                  }\n                />\n                <Label htmlFor=\"new-category-active\">Active category</Label>\n              </div>\n            </div>\n\n            <div className=\"mt-6 flex gap-2\">\n              <Button onClick={handleAddCategory}>\n                <Save className=\"mr-2 h-4 w-4\" />\n                Add Category\n              </Button>\n              <Button variant=\"outline\" onClick={() => setShowAddForm(false)}>\n                <X className=\"mr-2 h-4 w-4\" />\n                Cancel\n              </Button>\n            </div>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Categories Display */}\n      {viewMode === \"grid\" ? (\n        <div className=\"grid gap-4 md:grid-cols-2 lg:grid-cols-3\">\n          {filteredAndSortedCategories.map((category) => (\n            <Card\n              key={category.id}\n              className=\"transition-shadow hover:shadow-md\"\n            >\n              <CardContent className=\"p-4\">\n                {editingCategoryId === category.id ? (\n                  // Edit form\n                  <div className=\"space-y-3\">\n                    <Input\n                      value={editForm.name || \"\"}\n                      onChange={(e) =>\n                        setEditForm({ ...editForm, name: e.target.value })\n                      }\n                      placeholder=\"Category name\"\n                    />\n                    <Textarea\n                      value={editForm.description || \"\"}\n                      onChange={(e) =>\n                        setEditForm({\n                          ...editForm,\n                          description: e.target.value,\n                        })\n                      }\n                      placeholder=\"Description\"\n                      rows={2}\n                    />\n                    <div className=\"flex gap-2\">\n                      <Button size=\"sm\" onClick={handleEditSave}>\n                        <Save className=\"mr-1 h-3 w-3\" />\n                        Save\n                      </Button>\n                      <Button\n                        size=\"sm\"\n                        variant=\"outline\"\n                        onClick={handleEditCancel}\n                      >\n                        <X className=\"h-3 w-3\" />\n                      </Button>\n                    </div>\n                  </div>\n                ) : (\n                  // Display mode\n                  <div className=\"space-y-3\">\n                    <div className=\"flex items-start justify-between\">\n                      <div className=\"flex items-center gap-2\">\n                        <span className=\"text-2xl\">{category.icon}</span>\n                        <div>\n                          <h3 className=\"font-semibold\">{category.name}</h3>\n                          <Badge\n                            variant={\n                              category.isActive ? \"default\" : \"secondary\"\n                            }\n                            className=\"text-xs\"\n                          >\n                            {category.isActive ? \"Active\" : \"Inactive\"}\n                          </Badge>\n                        </div>\n                      </div>\n\n                      <DropdownMenu>\n                        <DropdownMenuTrigger asChild>\n                          <Button variant=\"ghost\" size=\"sm\">\n                            <MoreHorizontal className=\"h-4 w-4\" />\n                          </Button>\n                        </DropdownMenuTrigger>\n                        <DropdownMenuContent align=\"end\">\n                          <DropdownMenuItem\n                            onClick={() => handleEditStart(category)}\n                          >\n                            <Edit className=\"mr-2 h-4 w-4\" />\n                            Edit\n                          </DropdownMenuItem>\n                          <DropdownMenuItem>\n                            <Eye className=\"mr-2 h-4 w-4\" />\n                            View Products\n                          </DropdownMenuItem>\n                          <DropdownMenuItem>\n                            <Copy className=\"mr-2 h-4 w-4\" />\n                            Duplicate\n                          </DropdownMenuItem>\n                          <DropdownMenuSeparator />\n                          <DropdownMenuItem>\n                            <Archive className=\"mr-2 h-4 w-4\" />\n                            Archive\n                          </DropdownMenuItem>\n                          <DropdownMenuItem\n                            onClick={() => handleDeleteCategory(category.id)}\n                            className=\"text-red-600\"\n                          >\n                            <Trash2 className=\"mr-2 h-4 w-4\" />\n                            Delete\n                          </DropdownMenuItem>\n                        </DropdownMenuContent>\n                      </DropdownMenu>\n                    </div>\n\n                    <p className=\"line-clamp-2 text-sm text-gray-600\">\n                      {category.description || \"No description provided\"}\n                    </p>\n\n                    <div className=\"flex items-center justify-between text-xs text-gray-500\">\n                      <span>{category.productCount} products</span>\n                      <span\n                        className=\"h-3 w-3 rounded-full\"\n                        style={{ backgroundColor: category.color }}\n                      ></span>\n                    </div>\n                  </div>\n                )}\n              </CardContent>\n            </Card>\n          ))}\n        </div>\n      ) : (\n        // List view\n        <Card>\n          <CardContent className=\"p-0\">\n            <div className=\"divide-y\">\n              {filteredAndSortedCategories.map((category) => (\n                <div key={category.id} className=\"p-4 hover:bg-gray-50\">\n                  {editingCategoryId === category.id ? (\n                    // Edit form\n                    <div className=\"flex items-center gap-4\">\n                      <Input\n                        value={editForm.name || \"\"}\n                        onChange={(e) =>\n                          setEditForm({ ...editForm, name: e.target.value })\n                        }\n                        placeholder=\"Category name\"\n                        className=\"flex-1\"\n                      />\n                      <Input\n                        value={editForm.description || \"\"}\n                        onChange={(e) =>\n                          setEditForm({\n                            ...editForm,\n                            description: e.target.value,\n                          })\n                        }\n                        placeholder=\"Description\"\n                        className=\"flex-1\"\n                      />\n                      <div className=\"flex gap-2\">\n                        <Button size=\"sm\" onClick={handleEditSave}>\n                          <Save className=\"h-4 w-4\" />\n                        </Button>\n                        <Button\n                          size=\"sm\"\n                          variant=\"outline\"\n                          onClick={handleEditCancel}\n                        >\n                          <X className=\"h-4 w-4\" />\n                        </Button>\n                      </div>\n                    </div>\n                  ) : (\n                    // Display mode\n                    <div className=\"flex items-center justify-between\">\n                      <div className=\"flex items-center gap-4\">\n                        <span className=\"text-2xl\">{category.icon}</span>\n                        <div>\n                          <div className=\"flex items-center gap-2\">\n                            <h3 className=\"font-semibold\">{category.name}</h3>\n                            <Badge\n                              variant={\n                                category.isActive ? \"default\" : \"secondary\"\n                              }\n                              className=\"text-xs\"\n                            >\n                              {category.isActive ? \"Active\" : \"Inactive\"}\n                            </Badge>\n                          </div>\n                          <p className=\"text-sm text-gray-600\">\n                            {category.description || \"No description\"}\n                          </p>\n                          <div className=\"mt-1 flex items-center gap-4 text-xs text-gray-500\">\n                            <span>{category.productCount} products</span>\n                            <span>Slug: {category.slug}</span>\n                            <div className=\"flex items-center gap-1\">\n                              <span>Color:</span>\n                              <span\n                                className=\"h-3 w-3 rounded-full\"\n                                style={{ backgroundColor: category.color }}\n                              ></span>\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n\n                      <div className=\"flex gap-2\">\n                        <Button\n                          size=\"sm\"\n                          variant=\"outline\"\n                          onClick={() => handleEditStart(category)}\n                        >\n                          <Pencil className=\"h-4 w-4\" />\n                        </Button>\n                        <Button\n                          size=\"sm\"\n                          variant=\"outline\"\n                          onClick={() => handleDeleteCategory(category.id)}\n                        >\n                          <Trash2 className=\"h-4 w-4\" />\n                        </Button>\n                      </div>\n                    </div>\n                  )}\n                </div>\n              ))}\n            </div>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Empty State */}\n      {filteredAndSortedCategories.length === 0 && (\n        <Card>\n          <CardContent className=\"p-12 text-center\">\n            <FolderOpen className=\"mx-auto h-12 w-12 text-gray-400\" />\n            <h3 className=\"mt-2 text-lg font-medium text-gray-900\">\n              No categories found\n            </h3>\n            <p className=\"mt-1 text-sm text-gray-500\">\n              {searchQuery || filterStatus !== \"all\"\n                ? \"Try adjusting your search or filter criteria.\"\n                : \"Get started by creating your first category.\"}\n            </p>\n            {!searchQuery && filterStatus === \"all\" && (\n              <Button className=\"mt-4\" onClick={() => setShowAddForm(true)}>\n                <Plus className=\"mr-2 h-4 w-4\" />\n                Add Your First Category\n              </Button>\n            )}\n          </CardContent>\n        </Card>\n      )}\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAwBA;AAEA;AACA;AACA;AACA;AAOA;AACA;AACA;AAOA;AACA;AAlDA;;;;;;;;;;;;;;AAwEA,qCAAqC;AACrC,MAAM,iBAA6B;IACjC;QACE,IAAI;QACJ,MAAM;QACN,aACE;QACF,MAAM;QACN,MAAM;QACN,OAAO;QACP,UAAU;QACV,cAAc;QACd,WAAW;QACX,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,aACE;QACF,MAAM;QACN,MAAM;QACN,OAAO;QACP,UAAU;QACV,cAAc;QACd,WAAW;QACX,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,MAAM;QACN,OAAO;QACP,UAAU;QACV,cAAc;QACd,WAAW;QACX,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,aACE;QACF,MAAM;QACN,MAAM;QACN,OAAO;QACP,UAAU;QACV,cAAc;QACd,WAAW;QACX,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,MAAM;QACN,OAAO;QACP,UAAU;QACV,cAAc;QACd,WAAW;QACX,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,MAAM;QACN,OAAO;QACP,UAAU;QACV,cAAc;QACd,WAAW;QACX,WAAW;QACX,WAAW;IACb;CACD;AAUM,MAAM,0BAA0B,CAAC,EACtC,oBAAoB,EAAE,EACtB,kBAAkB,EACG;IACrB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAc;IACzD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAqB;QAChE,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;QACP,UAAU;IACZ;IACA,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EACvD;IAEF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAqB,CAAC;IAC7D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAkB;IAC3D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAmB;IAC1D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,yCAAyC;IACzC,MAAM,eAAe,CAAC;QACpB,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,eAAe,KACvB,OAAO,CAAC,UAAU;IACvB;IAEA,6BAA6B;IAC7B,MAAM,8BAA8B,WACjC,MAAM,CAAC,CAAC;QACP,MAAM,gBACJ,SAAS,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OAC5D,SAAS,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;QACrE,MAAM,gBACJ,iBAAiB,SAChB,iBAAiB,YAAY,SAAS,QAAQ,IAC9C,iBAAiB,cAAc,CAAC,SAAS,QAAQ;QACpD,OAAO,iBAAiB;IAC1B,GACC,IAAI,CAAC,CAAC,GAAG;QACR,IAAI,aAAa;QACjB,OAAQ;YACN,KAAK;gBACH,aAAa,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,IAAI;gBACxC;YACF,KAAK;gBACH,aAAa,EAAE,YAAY,GAAG,EAAE,YAAY;gBAC5C;YACF,KAAK;gBACH,aACE,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO;gBACjE;YACF;gBACE,aAAa,EAAE,SAAS,GAAG,EAAE,SAAS;QAC1C;QACA,OAAO,cAAc,QAAQ,aAAa,CAAC;IAC7C;IAEF,qBAAqB;IACrB,MAAM,oBAAoB;QACxB,IAAI,CAAC,YAAY,IAAI,EAAE;YACrB,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,MAAM,OAAO,aAAa,YAAY,IAAI;QAE1C,+BAA+B;QAC/B,IAAI,WAAW,IAAI,CAAC,CAAC,MAAQ,IAAI,IAAI,KAAK,OAAO;YAC/C,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,MAAM,oBAA8B;YAClC,IAAI,CAAC,SAAS,EAAE,KAAK,GAAG,IAAI;YAC5B,MAAM,YAAY,IAAI;YACtB,aAAa,YAAY,WAAW,IAAI;YACxC;YACA,MAAM,YAAY,IAAI,IAAI;YAC1B,OAAO,YAAY,KAAK,IAAI;YAC5B,UAAU,YAAY,QAAQ,IAAI;YAClC,cAAc;YACd,WAAW,WAAW,MAAM,GAAG;YAC/B,WAAW,IAAI,OAAO,WAAW;YACjC,WAAW,IAAI,OAAO,WAAW;QACnC;QAEA,MAAM,oBAAoB;eAAI;YAAY;SAAkB;QAC5D,cAAc;QACd,eAAe;YACb,MAAM;YACN,aAAa;YACb,MAAM;YACN,OAAO;YACP,UAAU;QACZ;QACA,eAAe;QAEf,0BAA0B;QAC1B,IAAI,oBAAoB;YACtB,mBAAmB;QACrB;QAEA,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,2BAA2B;IAC3B,MAAM,kBAAkB,CAAC;QACvB,qBAAqB,SAAS,EAAE;QAChC,YAAY;YAAE,GAAG,QAAQ;QAAC;IAC5B;IAEA,iBAAiB;IACjB,MAAM,mBAAmB;QACvB,qBAAqB;QACrB,YAAY,CAAC;IACf;IAEA,uBAAuB;IACvB,MAAM,iBAAiB;QACrB,IAAI,CAAC,SAAS,IAAI,EAAE;YAClB,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,MAAM,oBAAoB,WAAW,GAAG,CAAC,CAAC,MACxC,IAAI,EAAE,KAAK,oBACP;gBACE,GAAG,GAAG;gBACN,MAAM,SAAS,IAAI,IAAI,IAAI,IAAI;gBAC/B,aAAa,SAAS,WAAW,IAAI,IAAI,WAAW;gBACpD,MAAM,SAAS,IAAI,IAAI,IAAI,IAAI;gBAC/B,OAAO,SAAS,KAAK,IAAI,IAAI,KAAK;gBAClC,UAAU,SAAS,QAAQ,IAAI,IAAI,QAAQ;gBAC3C,mCAAmC;gBACnC,MACE,IAAI,IAAI,KAAK,SAAS,IAAI,GACtB,aAAa,SAAS,IAAI,IAC1B,IAAI,IAAI;gBACd,WAAW,IAAI,OAAO,WAAW;YACnC,IACA;QAGN,cAAc;QACd,qBAAqB;QACrB,YAAY,CAAC;QAEb,0BAA0B;QAC1B,IAAI,oBAAoB;YACtB,mBAAmB;QACrB;QAEA,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,oBAAoB;IACpB,MAAM,uBAAuB,CAAC;QAC5B,MAAM,oBAAoB,WAAW,MAAM,CAAC,CAAC,MAAQ,IAAI,EAAE,KAAK;QAChE,cAAc;QAEd,0BAA0B;QAC1B,IAAI,oBAAoB;YACtB,mBAAmB;QACrB;QAEA,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,qBACE,6WAAC;QAAI,WAAU;;0BAEb,6WAAC;gBAAI,WAAU;;kCACb,6WAAC,yHAAA,CAAA,OAAI;kCACH,cAAA,6WAAC,yHAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;kDACb,cAAA,6WAAC,sSAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;kDAExB,6WAAC;;0DACC,6WAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,6WAAC;gDAAE,WAAU;0DAAsB,WAAW,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAM5D,6WAAC,yHAAA,CAAA,OAAI;kCACH,cAAA,6WAAC,yHAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;kDACb,cAAA,6WAAC,sSAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;kDAExB,6WAAC;;0DACC,6WAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,6WAAC;gDAAE,WAAU;0DACV,WAAW,MAAM,CAAC,CAAC,IAAM,EAAE,QAAQ,EAAE,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOtD,6WAAC,yHAAA,CAAA,OAAI;kCACH,cAAA,6WAAC,yHAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;kDACb,cAAA,6WAAC,4RAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;;;;;;kDAErB,6WAAC;;0DACC,6WAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,6WAAC;gDAAE,WAAU;0DACV,WAAW,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,YAAY,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO/D,6WAAC,yHAAA,CAAA,OAAI;kCACH,cAAA,6WAAC,yHAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;kDACb,cAAA,6WAAC,8RAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;kDAEtB,6WAAC;;0DACC,6WAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,6WAAC;gDAAE,WAAU;0DACV,WAAW,MAAM,GAAG,IACjB,KAAK,KAAK,CACR,WAAW,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,YAAY,EAAE,KAClD,WAAW,MAAM,IAErB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAShB,6WAAC,yHAAA,CAAA,OAAI;0BACH,cAAA,6WAAC,yHAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,6WAAC;wBAAI,WAAU;;0CAEb,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,0RAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,6WAAC,0HAAA,CAAA,QAAK;gDACJ,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gDAC9C,WAAU;;;;;;;;;;;;kDAId,6WAAC,2HAAA,CAAA,SAAM;wCAAC,OAAO;wCAAc,eAAe;;0DAC1C,6WAAC,2HAAA,CAAA,gBAAa;gDAAC,WAAU;0DACvB,cAAA,6WAAC,2HAAA,CAAA,cAAW;;;;;;;;;;0DAEd,6WAAC,2HAAA,CAAA,gBAAa;;kEACZ,6WAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAM;;;;;;kEACxB,6WAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAS;;;;;;kEAC3B,6WAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAW;;;;;;;;;;;;;;;;;;kDAIjC,6WAAC,2HAAA,CAAA,SAAM;wCAAC,OAAO;wCAAQ,eAAe;;0DACpC,6WAAC,2HAAA,CAAA,gBAAa;gDAAC,WAAU;0DACvB,cAAA,6WAAC,2HAAA,CAAA,cAAW;;;;;;;;;;0DAEd,6WAAC,2HAAA,CAAA,gBAAa;;kEACZ,6WAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAO;;;;;;kEACzB,6WAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAW;;;;;;kEAC7B,6WAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAU;;;;;;;;;;;;;;;;;;;;;;;;0CAMlC,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,2HAAA,CAAA,SAAM;wCACL,SAAS,cAAc,QAAQ,YAAY;wCAC3C,MAAK;wCACL,SAAS,IACP,aAAa,cAAc,QAAQ,SAAS;kDAG7C,cAAc,sBACb,6WAAC,kTAAA,CAAA,UAAO;4CAAC,WAAU;;;;;iEAEnB,6WAAC,qTAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;kDAIxB,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,2HAAA,CAAA,SAAM;gDACL,SAAS,aAAa,SAAS,YAAY;gDAC3C,MAAK;gDACL,SAAS,IAAM,YAAY;gDAC3B,WAAU;0DAEV,cAAA,6WAAC,gSAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;;;;;;0DAErB,6WAAC,2HAAA,CAAA,SAAM;gDACL,SAAS,aAAa,SAAS,YAAY;gDAC3C,MAAK;gDACL,SAAS,IAAM,YAAY;gDAC3B,WAAU;0DAEV,cAAA,6WAAC,sRAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAIpB,6WAAC,2HAAA,CAAA,SAAM;wCAAC,SAAS,IAAM,eAAe;;0DACpC,6WAAC,sRAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAS1C,6BACC,6WAAC,yHAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6WAAC,yHAAA,CAAA,aAAU;kCACT,cAAA,6WAAC,yHAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6WAAC,sRAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAA0B;;;;;;;;;;;;kCAI9C,6WAAC,yHAAA,CAAA,cAAW;;0CACV,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;;0DACC,6WAAC,0HAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAoB;;;;;;0DACnC,6WAAC,0HAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,OAAO,YAAY,IAAI;gDACvB,UAAU,CAAC,IACT,eAAe;wDAAE,GAAG,WAAW;wDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;oDAAC;gDAExD,aAAY;gDACZ,WAAU;;;;;;;;;;;;kDAId,6WAAC;;0DACC,6WAAC,0HAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAoB;;;;;;0DACnC,6WAAC,0HAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,OAAO,YAAY,IAAI;gDACvB,UAAU,CAAC,IACT,eAAe;wDAAE,GAAG,WAAW;wDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;oDAAC;gDAExD,aAAY;gDACZ,WAAU;;;;;;;;;;;;kDAId,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,0HAAA,CAAA,QAAK;gDAAC,SAAQ;0DAA2B;;;;;;0DAC1C,6WAAC,6HAAA,CAAA,WAAQ;gDACP,IAAG;gDACH,OAAO,YAAY,WAAW;gDAC9B,UAAU,CAAC,IACT,eAAe;wDACb,GAAG,WAAW;wDACd,aAAa,EAAE,MAAM,CAAC,KAAK;oDAC7B;gDAEF,aAAY;gDACZ,WAAU;gDACV,MAAM;;;;;;;;;;;;kDAIV,6WAAC;;0DACC,6WAAC,0HAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAqB;;;;;;0DACpC,6WAAC,0HAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,OAAO,YAAY,KAAK;gDACxB,UAAU,CAAC,IACT,eAAe;wDAAE,GAAG,WAAW;wDAAE,OAAO,EAAE,MAAM,CAAC,KAAK;oDAAC;gDAEzD,WAAU;;;;;;;;;;;;kDAId,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,2HAAA,CAAA,SAAM;gDACL,IAAG;gDACH,SAAS,YAAY,QAAQ;gDAC7B,iBAAiB,CAAC,UAChB,eAAe;wDAAE,GAAG,WAAW;wDAAE,UAAU;oDAAQ;;;;;;0DAGvD,6WAAC,0HAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAsB;;;;;;;;;;;;;;;;;;0CAIzC,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,2HAAA,CAAA,SAAM;wCAAC,SAAS;;0DACf,6WAAC,sRAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGnC,6WAAC,2HAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,SAAS,IAAM,eAAe;;0DACtD,6WAAC,gRAAA,CAAA,IAAC;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;YASvC,aAAa,uBACZ,6WAAC;gBAAI,WAAU;0BACZ,4BAA4B,GAAG,CAAC,CAAC,yBAChC,6WAAC,yHAAA,CAAA,OAAI;wBAEH,WAAU;kCAEV,cAAA,6WAAC,yHAAA,CAAA,cAAW;4BAAC,WAAU;sCACpB,sBAAsB,SAAS,EAAE,GAChC,YAAY;0CACZ,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,0HAAA,CAAA,QAAK;wCACJ,OAAO,SAAS,IAAI,IAAI;wCACxB,UAAU,CAAC,IACT,YAAY;gDAAE,GAAG,QAAQ;gDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;4CAAC;wCAElD,aAAY;;;;;;kDAEd,6WAAC,6HAAA,CAAA,WAAQ;wCACP,OAAO,SAAS,WAAW,IAAI;wCAC/B,UAAU,CAAC,IACT,YAAY;gDACV,GAAG,QAAQ;gDACX,aAAa,EAAE,MAAM,CAAC,KAAK;4CAC7B;wCAEF,aAAY;wCACZ,MAAM;;;;;;kDAER,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,2HAAA,CAAA,SAAM;gDAAC,MAAK;gDAAK,SAAS;;kEACzB,6WAAC,sRAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGnC,6WAAC,2HAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,SAAS;0DAET,cAAA,6WAAC,gRAAA,CAAA,IAAC;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;uCAKnB,eAAe;0CACf,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;;0DACb,6WAAC;gDAAI,WAAU;;kEACb,6WAAC;wDAAK,WAAU;kEAAY,SAAS,IAAI;;;;;;kEACzC,6WAAC;;0EACC,6WAAC;gEAAG,WAAU;0EAAiB,SAAS,IAAI;;;;;;0EAC5C,6WAAC,0HAAA,CAAA,QAAK;gEACJ,SACE,SAAS,QAAQ,GAAG,YAAY;gEAElC,WAAU;0EAET,SAAS,QAAQ,GAAG,WAAW;;;;;;;;;;;;;;;;;;0DAKtC,6WAAC,qIAAA,CAAA,eAAY;;kEACX,6WAAC,qIAAA,CAAA,sBAAmB;wDAAC,OAAO;kEAC1B,cAAA,6WAAC,2HAAA,CAAA,SAAM;4DAAC,SAAQ;4DAAQ,MAAK;sEAC3B,cAAA,6WAAC,oSAAA,CAAA,iBAAc;gEAAC,WAAU;;;;;;;;;;;;;;;;kEAG9B,6WAAC,qIAAA,CAAA,sBAAmB;wDAAC,OAAM;;0EACzB,6WAAC,qIAAA,CAAA,mBAAgB;gEACf,SAAS,IAAM,gBAAgB;;kFAE/B,6WAAC,+RAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAGnC,6WAAC,qIAAA,CAAA,mBAAgB;;kFACf,6WAAC,oRAAA,CAAA,MAAG;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAGlC,6WAAC,qIAAA,CAAA,mBAAgB;;kFACf,6WAAC,sRAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAGnC,6WAAC,qIAAA,CAAA,wBAAqB;;;;;0EACtB,6WAAC,qIAAA,CAAA,mBAAgB;;kFACf,6WAAC,4RAAA,CAAA,UAAO;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAGtC,6WAAC,qIAAA,CAAA,mBAAgB;gEACf,SAAS,IAAM,qBAAqB,SAAS,EAAE;gEAC/C,WAAU;;kFAEV,6WAAC,8RAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;kDAO3C,6WAAC;wCAAE,WAAU;kDACV,SAAS,WAAW,IAAI;;;;;;kDAG3B,6WAAC;wCAAI,WAAU;;0DACb,6WAAC;;oDAAM,SAAS,YAAY;oDAAC;;;;;;;0DAC7B,6WAAC;gDACC,WAAU;gDACV,OAAO;oDAAE,iBAAiB,SAAS,KAAK;gDAAC;;;;;;;;;;;;;;;;;;;;;;;uBAvG9C,SAAS,EAAE;;;;;;;;;uBAiHtB,YAAY;0BACZ,6WAAC,yHAAA,CAAA,OAAI;0BACH,cAAA,6WAAC,yHAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,6WAAC;wBAAI,WAAU;kCACZ,4BAA4B,GAAG,CAAC,CAAC,yBAChC,6WAAC;gCAAsB,WAAU;0CAC9B,sBAAsB,SAAS,EAAE,GAChC,YAAY;8CACZ,6WAAC;oCAAI,WAAU;;sDACb,6WAAC,0HAAA,CAAA,QAAK;4CACJ,OAAO,SAAS,IAAI,IAAI;4CACxB,UAAU,CAAC,IACT,YAAY;oDAAE,GAAG,QAAQ;oDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;gDAAC;4CAElD,aAAY;4CACZ,WAAU;;;;;;sDAEZ,6WAAC,0HAAA,CAAA,QAAK;4CACJ,OAAO,SAAS,WAAW,IAAI;4CAC/B,UAAU,CAAC,IACT,YAAY;oDACV,GAAG,QAAQ;oDACX,aAAa,EAAE,MAAM,CAAC,KAAK;gDAC7B;4CAEF,aAAY;4CACZ,WAAU;;;;;;sDAEZ,6WAAC;4CAAI,WAAU;;8DACb,6WAAC,2HAAA,CAAA,SAAM;oDAAC,MAAK;oDAAK,SAAS;8DACzB,cAAA,6WAAC,sRAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;8DAElB,6WAAC,2HAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,SAAS;8DAET,cAAA,6WAAC,gRAAA,CAAA,IAAC;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;2CAKnB,eAAe;8CACf,6WAAC;oCAAI,WAAU;;sDACb,6WAAC;4CAAI,WAAU;;8DACb,6WAAC;oDAAK,WAAU;8DAAY,SAAS,IAAI;;;;;;8DACzC,6WAAC;;sEACC,6WAAC;4DAAI,WAAU;;8EACb,6WAAC;oEAAG,WAAU;8EAAiB,SAAS,IAAI;;;;;;8EAC5C,6WAAC,0HAAA,CAAA,QAAK;oEACJ,SACE,SAAS,QAAQ,GAAG,YAAY;oEAElC,WAAU;8EAET,SAAS,QAAQ,GAAG,WAAW;;;;;;;;;;;;sEAGpC,6WAAC;4DAAE,WAAU;sEACV,SAAS,WAAW,IAAI;;;;;;sEAE3B,6WAAC;4DAAI,WAAU;;8EACb,6WAAC;;wEAAM,SAAS,YAAY;wEAAC;;;;;;;8EAC7B,6WAAC;;wEAAK;wEAAO,SAAS,IAAI;;;;;;;8EAC1B,6WAAC;oEAAI,WAAU;;sFACb,6WAAC;sFAAK;;;;;;sFACN,6WAAC;4EACC,WAAU;4EACV,OAAO;gFAAE,iBAAiB,SAAS,KAAK;4EAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAOnD,6WAAC;4CAAI,WAAU;;8DACb,6WAAC,2HAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,SAAS,IAAM,gBAAgB;8DAE/B,cAAA,6WAAC,0RAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;;;;;;8DAEpB,6WAAC,2HAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,SAAS,IAAM,qBAAqB,SAAS,EAAE;8DAE/C,cAAA,6WAAC,8RAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;+BAnFlB,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;YAgG9B,4BAA4B,MAAM,KAAK,mBACtC,6WAAC,yHAAA,CAAA,OAAI;0BACH,cAAA,6WAAC,yHAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,6WAAC,sSAAA,CAAA,aAAU;4BAAC,WAAU;;;;;;sCACtB,6WAAC;4BAAG,WAAU;sCAAyC;;;;;;sCAGvD,6WAAC;4BAAE,WAAU;sCACV,eAAe,iBAAiB,QAC7B,kDACA;;;;;;wBAEL,CAAC,eAAe,iBAAiB,uBAChC,6WAAC,2HAAA,CAAA,SAAM;4BAAC,WAAU;4BAAO,SAAS,IAAM,eAAe;;8CACrD,6WAAC,sRAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;;;;;;;AASjD", "debugId": null}}, {"offset": {"line": 4120, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/pages/management/ColorManagerEnhanced.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\n\nimport {\n  BarChart3,\n  Check,\n  ChevronDown,\n  ChevronUp,\n  Edit,\n  Eye,\n  EyeOff,\n  Filter,\n  Grid3X3,\n  MoreHorizontal,\n  Palette,\n  Plus,\n  Search,\n  Sparkles,\n  Trash2,\n  <PERSON><PERSON>dingUp,\n  X,\n} from \"lucide-react\";\nimport { toast } from \"sonner\";\n\nimport { Badge } from \"@/components/ui/badge\";\nimport { Button } from \"@/components/ui/button\";\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from \"@/components/ui/dropdown-menu\";\nimport { Input } from \"@/components/ui/input\";\nimport { Label } from \"@/components/ui/label\";\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from \"@/components/ui/select\";\nimport { Switch } from \"@/components/ui/switch\";\nimport { Textarea } from \"@/components/ui/textarea\";\n\n// Utility function to generate slug from name\nconst generateSlug = (name: string): string => {\n  return name\n    .toLowerCase()\n    .replace(/[^a-z0-9]+/g, \"-\")\n    .replace(/(^-|-$)/g, \"\");\n};\n\nexport type Color = {\n  id: string;\n  name: string;\n  description: string;\n  slug: string;\n  hexCode: string;\n  rgbCode?: string;\n  category: \"primary\" | \"secondary\" | \"neutral\" | \"accent\" | \"pastel\";\n  isActive: boolean;\n  isPopular: boolean;\n  productCount: number;\n  sortOrder: number;\n  createdAt: string;\n  updatedAt: string;\n};\n\n// Mock colors with enhanced data\nconst mockColors: Color[] = [\n  {\n    id: \"color-1\",\n    name: \"Classic Black\",\n    description:\n      \"Timeless black color perfect for elegant and professional products\",\n    slug: \"classic-black\",\n    hexCode: \"#000000\",\n    rgbCode: \"rgb(0, 0, 0)\",\n    category: \"neutral\",\n    isActive: true,\n    isPopular: true,\n    productCount: 245,\n    sortOrder: 1,\n    createdAt: \"2024-01-01T00:00:00Z\",\n    updatedAt: \"2024-01-15T10:30:00Z\",\n  },\n  {\n    id: \"color-2\",\n    name: \"Pure White\",\n    description: \"Clean white color ideal for minimalist and modern designs\",\n    slug: \"pure-white\",\n    hexCode: \"#FFFFFF\",\n    rgbCode: \"rgb(255, 255, 255)\",\n    category: \"neutral\",\n    isActive: true,\n    isPopular: true,\n    productCount: 198,\n    sortOrder: 2,\n    createdAt: \"2024-01-02T00:00:00Z\",\n    updatedAt: \"2024-01-14T16:45:00Z\",\n  },\n  {\n    id: \"color-3\",\n    name: \"Ocean Blue\",\n    description:\n      \"Deep blue reminiscent of ocean depths, perfect for tech products\",\n    slug: \"ocean-blue\",\n    hexCode: \"#1E40AF\",\n    rgbCode: \"rgb(30, 64, 175)\",\n    category: \"primary\",\n    isActive: true,\n    isPopular: true,\n    productCount: 156,\n    sortOrder: 3,\n    createdAt: \"2024-01-03T00:00:00Z\",\n    updatedAt: \"2024-01-13T12:20:00Z\",\n  },\n  {\n    id: \"color-4\",\n    name: \"Forest Green\",\n    description:\n      \"Natural green color representing sustainability and eco-friendliness\",\n    slug: \"forest-green\",\n    hexCode: \"#059669\",\n    rgbCode: \"rgb(5, 150, 105)\",\n    category: \"secondary\",\n    isActive: true,\n    isPopular: false,\n    productCount: 89,\n    sortOrder: 4,\n    createdAt: \"2024-01-04T00:00:00Z\",\n    updatedAt: \"2024-01-12T09:15:00Z\",\n  },\n  {\n    id: \"color-5\",\n    name: \"Sunset Orange\",\n    description: \"Vibrant orange that captures attention and energy\",\n    slug: \"sunset-orange\",\n    hexCode: \"#EA580C\",\n    rgbCode: \"rgb(234, 88, 12)\",\n    category: \"accent\",\n    isActive: false,\n    isPopular: false,\n    productCount: 34,\n    sortOrder: 5,\n    createdAt: \"2024-01-05T00:00:00Z\",\n    updatedAt: \"2024-01-11T11:30:00Z\",\n  },\n  {\n    id: \"color-6\",\n    name: \"Soft Pink\",\n    description: \"Gentle pink color perfect for beauty and lifestyle products\",\n    slug: \"soft-pink\",\n    hexCode: \"#F472B6\",\n    rgbCode: \"rgb(244, 114, 182)\",\n    category: \"pastel\",\n    isActive: true,\n    isPopular: true,\n    productCount: 67,\n    sortOrder: 6,\n    createdAt: \"2024-01-06T00:00:00Z\",\n    updatedAt: \"2024-01-10T08:45:00Z\",\n  },\n];\n\ntype ColorManagerProps = {\n  initialColors?: Color[];\n  onColorsChange?: (colors: Color[]) => void;\n};\n\n/**\n * Enhanced component for managing product colors with professional UI\n */\nexport const ColorManagerEnhanced = ({\n  initialColors = mockColors,\n  onColorsChange,\n}: ColorManagerProps) => {\n  const [colors, setColors] = useState<Color[]>(initialColors);\n  const [newColor, setNewColor] = useState<Partial<Color>>({\n    name: \"\",\n    description: \"\",\n    hexCode: \"#000000\",\n    category: \"neutral\",\n    isActive: true,\n    isPopular: false,\n  });\n  const [editingColorId, setEditingColorId] = useState<string | null>(null);\n  const [searchQuery, setSearchQuery] = useState(\"\");\n  const [filterCategory, setFilterCategory] = useState<string>(\"all\");\n  const [filterStatus, setFilterStatus] = useState<string>(\"all\");\n  const [sortBy, setSortBy] = useState<string>(\"name\");\n  const [viewMode, setViewMode] = useState<\"grid\" | \"list\">(\"grid\");\n  const [showAddForm, setShowAddForm] = useState(false);\n\n  // Notify parent component of changes\n  const notifyChange = (updatedColors: Color[]) => {\n    setColors(updatedColors);\n    onColorsChange?.(updatedColors);\n  };\n\n  // Filter and sort colors\n  const filteredColors = colors\n    .filter((color) => {\n      const matchesSearch = color.name\n        .toLowerCase()\n        .includes(searchQuery.toLowerCase());\n      const matchesCategory =\n        filterCategory === \"all\" || color.category === filterCategory;\n      const matchesStatus =\n        filterStatus === \"all\" ||\n        (filterStatus === \"active\" && color.isActive) ||\n        (filterStatus === \"inactive\" && !color.isActive);\n      return matchesSearch && matchesCategory && matchesStatus;\n    })\n    .sort((a, b) => {\n      switch (sortBy) {\n        case \"name\":\n          return a.name.localeCompare(b.name);\n        case \"products\":\n          return b.productCount - a.productCount;\n        case \"created\":\n          return (\n            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()\n          );\n        default:\n          return a.sortOrder - b.sortOrder;\n      }\n    });\n\n  // Statistics\n  const stats = {\n    total: colors.length,\n    active: colors.filter((c) => c.isActive).length,\n    popular: colors.filter((c) => c.isPopular).length,\n    totalProducts: colors.reduce((sum, c) => sum + c.productCount, 0),\n  };\n\n  // Add a new color\n  const handleAddColor = () => {\n    if (!newColor.name) {\n      toast.error(\"Color name is required\");\n      return;\n    }\n\n    if (!newColor.hexCode || !/^#[0-9A-F]{6}$/i.test(newColor.hexCode)) {\n      toast.error(\"Valid hex color code is required (e.g., #FF0000)\");\n      return;\n    }\n\n    const slug = generateSlug(newColor.name);\n\n    // Check if slug already exists\n    if (colors.some((color) => color.slug === slug)) {\n      toast.error(\"A color with this name already exists\");\n      return;\n    }\n\n    // Convert hex to RGB\n    const hex = newColor.hexCode.replace(\"#\", \"\");\n    const r = parseInt(hex.substr(0, 2), 16);\n    const g = parseInt(hex.substr(2, 2), 16);\n    const b = parseInt(hex.substr(4, 2), 16);\n    const rgbCode = `rgb(${r}, ${g}, ${b})`;\n\n    const newColorWithId: Color = {\n      id: `color-${Date.now()}`,\n      name: newColor.name,\n      description: newColor.description || \"\",\n      slug,\n      hexCode: newColor.hexCode.toUpperCase(),\n      rgbCode,\n      category: newColor.category || \"neutral\",\n      isActive: newColor.isActive ?? true,\n      isPopular: newColor.isPopular ?? false,\n      productCount: 0,\n      sortOrder: colors.length + 1,\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString(),\n    };\n\n    const updatedColors = [...colors, newColorWithId];\n    notifyChange(updatedColors);\n    setNewColor({\n      name: \"\",\n      description: \"\",\n      hexCode: \"#000000\",\n      category: \"neutral\",\n      isActive: true,\n      isPopular: false,\n    });\n    setShowAddForm(false);\n    toast.success(\"Color added successfully!\");\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Statistics Dashboard */}\n      <div className=\"grid gap-4 md:grid-cols-4\">\n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-sm font-medium text-gray-600\">\n              Total Colors\n            </CardTitle>\n            <Palette className=\"h-4 w-4 text-blue-600\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold text-gray-900\">\n              {stats.total}\n            </div>\n            <div className=\"mt-1 flex items-center text-xs text-gray-500\">\n              <TrendingUp className=\"mr-1 h-3 w-3 text-green-500\" />\n              <span>+2 this month</span>\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-sm font-medium text-gray-600\">\n              Active Colors\n            </CardTitle>\n            <Eye className=\"h-4 w-4 text-green-600\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold text-gray-900\">\n              {stats.active}\n            </div>\n            <div className=\"mt-1 text-xs text-gray-500\">\n              {Math.round((stats.active / stats.total) * 100)}% of total\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-sm font-medium text-gray-600\">\n              Popular Colors\n            </CardTitle>\n            <Sparkles className=\"h-4 w-4 text-purple-600\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold text-gray-900\">\n              {stats.popular}\n            </div>\n            <div className=\"mt-1 text-xs text-gray-500\">High demand colors</div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-sm font-medium text-gray-600\">\n              Total Products\n            </CardTitle>\n            <BarChart3 className=\"h-4 w-4 text-orange-600\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold text-gray-900\">\n              {stats.totalProducts}\n            </div>\n            <div className=\"mt-1 text-xs text-gray-500\">\n              Using color variants\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* Controls */}\n      <div className=\"flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between\">\n        <div className=\"flex flex-1 gap-2\">\n          <div className=\"relative max-w-sm flex-1\">\n            <Search className=\"absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400\" />\n            <Input\n              placeholder=\"Search colors...\"\n              value={searchQuery}\n              onChange={(e) => setSearchQuery(e.target.value)}\n              className=\"pl-10\"\n            />\n          </div>\n          <Select value={filterCategory} onValueChange={setFilterCategory}>\n            <SelectTrigger className=\"w-[140px]\">\n              <Filter className=\"mr-2 h-4 w-4\" />\n              <SelectValue />\n            </SelectTrigger>\n            <SelectContent>\n              <SelectItem value=\"all\">All Categories</SelectItem>\n              <SelectItem value=\"primary\">Primary</SelectItem>\n              <SelectItem value=\"secondary\">Secondary</SelectItem>\n              <SelectItem value=\"neutral\">Neutral</SelectItem>\n              <SelectItem value=\"accent\">Accent</SelectItem>\n              <SelectItem value=\"pastel\">Pastel</SelectItem>\n            </SelectContent>\n          </Select>\n          <Select value={filterStatus} onValueChange={setFilterStatus}>\n            <SelectTrigger className=\"w-[120px]\">\n              <SelectValue />\n            </SelectTrigger>\n            <SelectContent>\n              <SelectItem value=\"all\">All Status</SelectItem>\n              <SelectItem value=\"active\">Active</SelectItem>\n              <SelectItem value=\"inactive\">Inactive</SelectItem>\n            </SelectContent>\n          </Select>\n        </div>\n\n        <div className=\"flex gap-2\">\n          <Select value={sortBy} onValueChange={setSortBy}>\n            <SelectTrigger className=\"w-[140px]\">\n              <SelectValue />\n            </SelectTrigger>\n            <SelectContent>\n              <SelectItem value=\"name\">Sort by Name</SelectItem>\n              <SelectItem value=\"products\">Sort by Products</SelectItem>\n              <SelectItem value=\"created\">Sort by Created</SelectItem>\n            </SelectContent>\n          </Select>\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={() => setViewMode(viewMode === \"grid\" ? \"list\" : \"grid\")}\n          >\n            <Grid3X3 className=\"h-4 w-4\" />\n          </Button>\n          <Button\n            size=\"sm\"\n            onClick={() => setShowAddForm(!showAddForm)}\n            className=\"bg-purple-600 hover:bg-purple-700\"\n          >\n            <Plus className=\"mr-2 h-4 w-4\" />\n            Add Color\n          </Button>\n        </div>\n      </div>\n\n      {/* Add Color Form */}\n      {showAddForm && (\n        <Card className=\"border-purple-200 bg-purple-50\">\n          <CardHeader>\n            <CardTitle className=\"flex items-center gap-2 text-purple-900\">\n              <Plus className=\"h-5 w-5\" />\n              Add New Color\n            </CardTitle>\n          </CardHeader>\n          <CardContent className=\"space-y-4\">\n            <div className=\"grid gap-4 md:grid-cols-2\">\n              <div>\n                <Label htmlFor=\"color-name\">Color Name *</Label>\n                <Input\n                  id=\"color-name\"\n                  value={newColor.name}\n                  onChange={(e) =>\n                    setNewColor({ ...newColor, name: e.target.value })\n                  }\n                  placeholder=\"e.g., Ocean Blue\"\n                />\n              </div>\n              <div>\n                <Label htmlFor=\"color-hex\">Hex Code *</Label>\n                <div className=\"flex gap-2\">\n                  <Input\n                    id=\"color-hex\"\n                    value={newColor.hexCode}\n                    onChange={(e) =>\n                      setNewColor({ ...newColor, hexCode: e.target.value })\n                    }\n                    placeholder=\"#000000\"\n                    className=\"flex-1\"\n                  />\n                  <div\n                    className=\"h-10 w-16 rounded border-2 border-gray-300\"\n                    style={{ backgroundColor: newColor.hexCode }}\n                  />\n                </div>\n              </div>\n            </div>\n            <div>\n              <Label htmlFor=\"color-description\">Description</Label>\n              <Textarea\n                id=\"color-description\"\n                value={newColor.description}\n                onChange={(e) =>\n                  setNewColor({ ...newColor, description: e.target.value })\n                }\n                placeholder=\"Describe this color and its best use cases...\"\n                rows={2}\n              />\n            </div>\n            <div className=\"grid gap-4 md:grid-cols-3\">\n              <div>\n                <Label htmlFor=\"color-category\">Category</Label>\n                <Select\n                  value={newColor.category}\n                  onValueChange={(value) =>\n                    setNewColor({\n                      ...newColor,\n                      category: value as Color[\"category\"],\n                    })\n                  }\n                >\n                  <SelectTrigger>\n                    <SelectValue />\n                  </SelectTrigger>\n                  <SelectContent>\n                    <SelectItem value=\"primary\">Primary</SelectItem>\n                    <SelectItem value=\"secondary\">Secondary</SelectItem>\n                    <SelectItem value=\"neutral\">Neutral</SelectItem>\n                    <SelectItem value=\"accent\">Accent</SelectItem>\n                    <SelectItem value=\"pastel\">Pastel</SelectItem>\n                  </SelectContent>\n                </Select>\n              </div>\n              <div className=\"flex items-center space-x-2\">\n                <Switch\n                  id=\"color-active\"\n                  checked={newColor.isActive}\n                  onCheckedChange={(checked) =>\n                    setNewColor({ ...newColor, isActive: checked })\n                  }\n                />\n                <Label htmlFor=\"color-active\">Active</Label>\n              </div>\n              <div className=\"flex items-center space-x-2\">\n                <Switch\n                  id=\"color-popular\"\n                  checked={newColor.isPopular}\n                  onCheckedChange={(checked) =>\n                    setNewColor({ ...newColor, isPopular: checked })\n                  }\n                />\n                <Label htmlFor=\"color-popular\">Popular</Label>\n              </div>\n            </div>\n            <div className=\"flex gap-2 pt-2\">\n              <Button\n                onClick={handleAddColor}\n                className=\"bg-purple-600 hover:bg-purple-700\"\n              >\n                <Check className=\"mr-2 h-4 w-4\" />\n                Add Color\n              </Button>\n              <Button variant=\"outline\" onClick={() => setShowAddForm(false)}>\n                <X className=\"mr-2 h-4 w-4\" />\n                Cancel\n              </Button>\n            </div>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Colors Grid/List */}\n      <div\n        className={\n          viewMode === \"grid\"\n            ? \"grid gap-4 md:grid-cols-2 lg:grid-cols-3\"\n            : \"space-y-3\"\n        }\n      >\n        {filteredColors.map((color) => (\n          <Card\n            key={color.id}\n            className={`transition-all hover:shadow-md ${\n              !color.isActive ? \"opacity-60\" : \"\"\n            }`}\n          >\n            <CardContent className=\"p-4\">\n              <div className=\"flex items-start justify-between\">\n                <div className=\"flex items-center gap-3\">\n                  <div\n                    className=\"h-12 w-12 rounded-lg border-2 border-gray-200 shadow-sm\"\n                    style={{ backgroundColor: color.hexCode }}\n                  />\n                  <div>\n                    <h3 className=\"font-semibold text-gray-900\">\n                      {color.name}\n                    </h3>\n                    <p className=\"text-sm text-gray-600\">{color.hexCode}</p>\n                    <div className=\"mt-1 flex items-center gap-2\">\n                      <Badge\n                        variant=\"outline\"\n                        className={`text-xs ${\n                          color.category === \"primary\"\n                            ? \"border-blue-200 text-blue-700\"\n                            : color.category === \"secondary\"\n                              ? \"border-green-200 text-green-700\"\n                              : color.category === \"neutral\"\n                                ? \"border-gray-200 text-gray-700\"\n                                : color.category === \"accent\"\n                                  ? \"border-orange-200 text-orange-700\"\n                                  : \"border-pink-200 text-pink-700\"\n                        }`}\n                      >\n                        {color.category}\n                      </Badge>\n                      {color.isPopular && (\n                        <Badge variant=\"secondary\" className=\"text-xs\">\n                          <Sparkles className=\"mr-1 h-3 w-3\" />\n                          Popular\n                        </Badge>\n                      )}\n                    </div>\n                  </div>\n                </div>\n                <DropdownMenu>\n                  <DropdownMenuTrigger asChild>\n                    <Button variant=\"ghost\" size=\"sm\">\n                      <MoreHorizontal className=\"h-4 w-4\" />\n                    </Button>\n                  </DropdownMenuTrigger>\n                  <DropdownMenuContent align=\"end\">\n                    <DropdownMenuItem>\n                      <Edit className=\"mr-2 h-4 w-4\" />\n                      Edit\n                    </DropdownMenuItem>\n                    <DropdownMenuItem>\n                      {color.isActive ? (\n                        <>\n                          <EyeOff className=\"mr-2 h-4 w-4\" />\n                          Deactivate\n                        </>\n                      ) : (\n                        <>\n                          <Eye className=\"mr-2 h-4 w-4\" />\n                          Activate\n                        </>\n                      )}\n                    </DropdownMenuItem>\n                    <DropdownMenuSeparator />\n                    <DropdownMenuItem className=\"text-red-600\">\n                      <Trash2 className=\"mr-2 h-4 w-4\" />\n                      Delete\n                    </DropdownMenuItem>\n                  </DropdownMenuContent>\n                </DropdownMenu>\n              </div>\n              {color.description && (\n                <p className=\"mt-2 text-sm text-gray-600\">\n                  {color.description}\n                </p>\n              )}\n              <div className=\"mt-3 flex items-center justify-between text-sm text-gray-500\">\n                <span>{color.productCount} products</span>\n                <span\n                  className={color.isActive ? \"text-green-600\" : \"text-red-600\"}\n                >\n                  {color.isActive ? \"Active\" : \"Inactive\"}\n                </span>\n              </div>\n            </CardContent>\n          </Card>\n        ))}\n      </div>\n\n      {filteredColors.length === 0 && (\n        <Card>\n          <CardContent className=\"py-12 text-center\">\n            <Palette className=\"mx-auto h-12 w-12 text-gray-400\" />\n            <h3 className=\"mt-4 text-lg font-semibold text-gray-900\">\n              No colors found\n            </h3>\n            <p className=\"mt-2 text-gray-600\">\n              {searchQuery || filterCategory !== \"all\" || filterStatus !== \"all\"\n                ? \"Try adjusting your search or filters\"\n                : \"Get started by adding your first color\"}\n            </p>\n            {!searchQuery &&\n              filterCategory === \"all\" &&\n              filterStatus === \"all\" && (\n                <Button\n                  className=\"mt-4 bg-purple-600 hover:bg-purple-700\"\n                  onClick={() => setShowAddForm(true)}\n                >\n                  <Plus className=\"mr-2 h-4 w-4\" />\n                  Add Your First Color\n                </Button>\n              )}\n          </CardContent>\n        </Card>\n      )}\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAmBA;AAEA;AACA;AACA;AACA;AAOA;AACA;AACA;AAOA;AACA;AA7CA;;;;;;;;;;;;;;AA+CA,8CAA8C;AAC9C,MAAM,eAAe,CAAC;IACpB,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,eAAe,KACvB,OAAO,CAAC,YAAY;AACzB;AAkBA,iCAAiC;AACjC,MAAM,aAAsB;IAC1B;QACE,IAAI;QACJ,MAAM;QACN,aACE;QACF,MAAM;QACN,SAAS;QACT,SAAS;QACT,UAAU;QACV,UAAU;QACV,WAAW;QACX,cAAc;QACd,WAAW;QACX,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,SAAS;QACT,SAAS;QACT,UAAU;QACV,UAAU;QACV,WAAW;QACX,cAAc;QACd,WAAW;QACX,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,aACE;QACF,MAAM;QACN,SAAS;QACT,SAAS;QACT,UAAU;QACV,UAAU;QACV,WAAW;QACX,cAAc;QACd,WAAW;QACX,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,aACE;QACF,MAAM;QACN,SAAS;QACT,SAAS;QACT,UAAU;QACV,UAAU;QACV,WAAW;QACX,cAAc;QACd,WAAW;QACX,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,SAAS;QACT,SAAS;QACT,UAAU;QACV,UAAU;QACV,WAAW;QACX,cAAc;QACd,WAAW;QACX,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,SAAS;QACT,SAAS;QACT,UAAU;QACV,UAAU;QACV,WAAW;QACX,cAAc;QACd,WAAW;QACX,WAAW;QACX,WAAW;IACb;CACD;AAUM,MAAM,uBAAuB,CAAC,EACnC,gBAAgB,UAAU,EAC1B,cAAc,EACI;IAClB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAW;IAC9C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAkB;QACvD,MAAM;QACN,aAAa;QACb,SAAS;QACT,UAAU;QACV,UAAU;QACV,WAAW;IACb;IACA,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAU;IAC7D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAU;IACzD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAU;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAmB;IAC1D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,qCAAqC;IACrC,MAAM,eAAe,CAAC;QACpB,UAAU;QACV,iBAAiB;IACnB;IAEA,yBAAyB;IACzB,MAAM,iBAAiB,OACpB,MAAM,CAAC,CAAC;QACP,MAAM,gBAAgB,MAAM,IAAI,CAC7B,WAAW,GACX,QAAQ,CAAC,YAAY,WAAW;QACnC,MAAM,kBACJ,mBAAmB,SAAS,MAAM,QAAQ,KAAK;QACjD,MAAM,gBACJ,iBAAiB,SAChB,iBAAiB,YAAY,MAAM,QAAQ,IAC3C,iBAAiB,cAAc,CAAC,MAAM,QAAQ;QACjD,OAAO,iBAAiB,mBAAmB;IAC7C,GACC,IAAI,CAAC,CAAC,GAAG;QACR,OAAQ;YACN,KAAK;gBACH,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,IAAI;YACpC,KAAK;gBACH,OAAO,EAAE,YAAY,GAAG,EAAE,YAAY;YACxC,KAAK;gBACH,OACE,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO;YAEnE;gBACE,OAAO,EAAE,SAAS,GAAG,EAAE,SAAS;QACpC;IACF;IAEF,aAAa;IACb,MAAM,QAAQ;QACZ,OAAO,OAAO,MAAM;QACpB,QAAQ,OAAO,MAAM,CAAC,CAAC,IAAM,EAAE,QAAQ,EAAE,MAAM;QAC/C,SAAS,OAAO,MAAM,CAAC,CAAC,IAAM,EAAE,SAAS,EAAE,MAAM;QACjD,eAAe,OAAO,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,YAAY,EAAE;IACjE;IAEA,kBAAkB;IAClB,MAAM,iBAAiB;QACrB,IAAI,CAAC,SAAS,IAAI,EAAE;YAClB,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI,CAAC,SAAS,OAAO,IAAI,CAAC,kBAAkB,IAAI,CAAC,SAAS,OAAO,GAAG;YAClE,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,MAAM,OAAO,aAAa,SAAS,IAAI;QAEvC,+BAA+B;QAC/B,IAAI,OAAO,IAAI,CAAC,CAAC,QAAU,MAAM,IAAI,KAAK,OAAO;YAC/C,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,qBAAqB;QACrB,MAAM,MAAM,SAAS,OAAO,CAAC,OAAO,CAAC,KAAK;QAC1C,MAAM,IAAI,SAAS,IAAI,MAAM,CAAC,GAAG,IAAI;QACrC,MAAM,IAAI,SAAS,IAAI,MAAM,CAAC,GAAG,IAAI;QACrC,MAAM,IAAI,SAAS,IAAI,MAAM,CAAC,GAAG,IAAI;QACrC,MAAM,UAAU,CAAC,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;QAEvC,MAAM,iBAAwB;YAC5B,IAAI,CAAC,MAAM,EAAE,KAAK,GAAG,IAAI;YACzB,MAAM,SAAS,IAAI;YACnB,aAAa,SAAS,WAAW,IAAI;YACrC;YACA,SAAS,SAAS,OAAO,CAAC,WAAW;YACrC;YACA,UAAU,SAAS,QAAQ,IAAI;YAC/B,UAAU,SAAS,QAAQ,IAAI;YAC/B,WAAW,SAAS,SAAS,IAAI;YACjC,cAAc;YACd,WAAW,OAAO,MAAM,GAAG;YAC3B,WAAW,IAAI,OAAO,WAAW;YACjC,WAAW,IAAI,OAAO,WAAW;QACnC;QAEA,MAAM,gBAAgB;eAAI;YAAQ;SAAe;QACjD,aAAa;QACb,YAAY;YACV,MAAM;YACN,aAAa;YACb,SAAS;YACT,UAAU;YACV,UAAU;YACV,WAAW;QACb;QACA,eAAe;QACf,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,qBACE,6WAAC;QAAI,WAAU;;0BAEb,6WAAC;gBAAI,WAAU;;kCACb,6WAAC,yHAAA,CAAA,OAAI;;0CACH,6WAAC,yHAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,6WAAC,yHAAA,CAAA,YAAS;wCAAC,WAAU;kDAAoC;;;;;;kDAGzD,6WAAC,4RAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;;;;;;;0CAErB,6WAAC,yHAAA,CAAA,cAAW;;kDACV,6WAAC;wCAAI,WAAU;kDACZ,MAAM,KAAK;;;;;;kDAEd,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,sSAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;0DACtB,6WAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;kCAKZ,6WAAC,yHAAA,CAAA,OAAI;;0CACH,6WAAC,yHAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,6WAAC,yHAAA,CAAA,YAAS;wCAAC,WAAU;kDAAoC;;;;;;kDAGzD,6WAAC,oRAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;;;;;;;0CAEjB,6WAAC,yHAAA,CAAA,cAAW;;kDACV,6WAAC;wCAAI,WAAU;kDACZ,MAAM,MAAM;;;;;;kDAEf,6WAAC;wCAAI,WAAU;;4CACZ,KAAK,KAAK,CAAC,AAAC,MAAM,MAAM,GAAG,MAAM,KAAK,GAAI;4CAAK;;;;;;;;;;;;;;;;;;;kCAKtD,6WAAC,yHAAA,CAAA,OAAI;;0CACH,6WAAC,yHAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,6WAAC,yHAAA,CAAA,YAAS;wCAAC,WAAU;kDAAoC;;;;;;kDAGzD,6WAAC,8RAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;0CAEtB,6WAAC,yHAAA,CAAA,cAAW;;kDACV,6WAAC;wCAAI,WAAU;kDACZ,MAAM,OAAO;;;;;;kDAEhB,6WAAC;wCAAI,WAAU;kDAA6B;;;;;;;;;;;;;;;;;;kCAIhD,6WAAC,yHAAA,CAAA,OAAI;;0CACH,6WAAC,yHAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,6WAAC,yHAAA,CAAA,YAAS;wCAAC,WAAU;kDAAoC;;;;;;kDAGzD,6WAAC,sSAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;;0CAEvB,6WAAC,yHAAA,CAAA,cAAW;;kDACV,6WAAC;wCAAI,WAAU;kDACZ,MAAM,aAAa;;;;;;kDAEtB,6WAAC;wCAAI,WAAU;kDAA6B;;;;;;;;;;;;;;;;;;;;;;;;0BAQlD,6WAAC;gBAAI,WAAU;;kCACb,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,0RAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6WAAC,0HAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wCAC9C,WAAU;;;;;;;;;;;;0CAGd,6WAAC,2HAAA,CAAA,SAAM;gCAAC,OAAO;gCAAgB,eAAe;;kDAC5C,6WAAC,2HAAA,CAAA,gBAAa;wCAAC,WAAU;;0DACvB,6WAAC,0RAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,6WAAC,2HAAA,CAAA,cAAW;;;;;;;;;;;kDAEd,6WAAC,2HAAA,CAAA,gBAAa;;0DACZ,6WAAC,2HAAA,CAAA,aAAU;gDAAC,OAAM;0DAAM;;;;;;0DACxB,6WAAC,2HAAA,CAAA,aAAU;gDAAC,OAAM;0DAAU;;;;;;0DAC5B,6WAAC,2HAAA,CAAA,aAAU;gDAAC,OAAM;0DAAY;;;;;;0DAC9B,6WAAC,2HAAA,CAAA,aAAU;gDAAC,OAAM;0DAAU;;;;;;0DAC5B,6WAAC,2HAAA,CAAA,aAAU;gDAAC,OAAM;0DAAS;;;;;;0DAC3B,6WAAC,2HAAA,CAAA,aAAU;gDAAC,OAAM;0DAAS;;;;;;;;;;;;;;;;;;0CAG/B,6WAAC,2HAAA,CAAA,SAAM;gCAAC,OAAO;gCAAc,eAAe;;kDAC1C,6WAAC,2HAAA,CAAA,gBAAa;wCAAC,WAAU;kDACvB,cAAA,6WAAC,2HAAA,CAAA,cAAW;;;;;;;;;;kDAEd,6WAAC,2HAAA,CAAA,gBAAa;;0DACZ,6WAAC,2HAAA,CAAA,aAAU;gDAAC,OAAM;0DAAM;;;;;;0DACxB,6WAAC,2HAAA,CAAA,aAAU;gDAAC,OAAM;0DAAS;;;;;;0DAC3B,6WAAC,2HAAA,CAAA,aAAU;gDAAC,OAAM;0DAAW;;;;;;;;;;;;;;;;;;;;;;;;kCAKnC,6WAAC;wBAAI,WAAU;;0CACb,6WAAC,2HAAA,CAAA,SAAM;gCAAC,OAAO;gCAAQ,eAAe;;kDACpC,6WAAC,2HAAA,CAAA,gBAAa;wCAAC,WAAU;kDACvB,cAAA,6WAAC,2HAAA,CAAA,cAAW;;;;;;;;;;kDAEd,6WAAC,2HAAA,CAAA,gBAAa;;0DACZ,6WAAC,2HAAA,CAAA,aAAU;gDAAC,OAAM;0DAAO;;;;;;0DACzB,6WAAC,2HAAA,CAAA,aAAU;gDAAC,OAAM;0DAAW;;;;;;0DAC7B,6WAAC,2HAAA,CAAA,aAAU;gDAAC,OAAM;0DAAU;;;;;;;;;;;;;;;;;;0CAGhC,6WAAC,2HAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,YAAY,aAAa,SAAS,SAAS;0CAE1D,cAAA,6WAAC,gSAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;;;;;;0CAErB,6WAAC,2HAAA,CAAA,SAAM;gCACL,MAAK;gCACL,SAAS,IAAM,eAAe,CAAC;gCAC/B,WAAU;;kDAEV,6WAAC,sRAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;YAOtC,6BACC,6WAAC,yHAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6WAAC,yHAAA,CAAA,aAAU;kCACT,cAAA,6WAAC,yHAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6WAAC,sRAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;;;;;kCAIhC,6WAAC,yHAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;;0DACC,6WAAC,0HAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAa;;;;;;0DAC5B,6WAAC,0HAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,OAAO,SAAS,IAAI;gDACpB,UAAU,CAAC,IACT,YAAY;wDAAE,GAAG,QAAQ;wDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;oDAAC;gDAElD,aAAY;;;;;;;;;;;;kDAGhB,6WAAC;;0DACC,6WAAC,0HAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAY;;;;;;0DAC3B,6WAAC;gDAAI,WAAU;;kEACb,6WAAC,0HAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,OAAO,SAAS,OAAO;wDACvB,UAAU,CAAC,IACT,YAAY;gEAAE,GAAG,QAAQ;gEAAE,SAAS,EAAE,MAAM,CAAC,KAAK;4DAAC;wDAErD,aAAY;wDACZ,WAAU;;;;;;kEAEZ,6WAAC;wDACC,WAAU;wDACV,OAAO;4DAAE,iBAAiB,SAAS,OAAO;wDAAC;;;;;;;;;;;;;;;;;;;;;;;;0CAKnD,6WAAC;;kDACC,6WAAC,0HAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAoB;;;;;;kDACnC,6WAAC,6HAAA,CAAA,WAAQ;wCACP,IAAG;wCACH,OAAO,SAAS,WAAW;wCAC3B,UAAU,CAAC,IACT,YAAY;gDAAE,GAAG,QAAQ;gDAAE,aAAa,EAAE,MAAM,CAAC,KAAK;4CAAC;wCAEzD,aAAY;wCACZ,MAAM;;;;;;;;;;;;0CAGV,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;;0DACC,6WAAC,0HAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAiB;;;;;;0DAChC,6WAAC,2HAAA,CAAA,SAAM;gDACL,OAAO,SAAS,QAAQ;gDACxB,eAAe,CAAC,QACd,YAAY;wDACV,GAAG,QAAQ;wDACX,UAAU;oDACZ;;kEAGF,6WAAC,2HAAA,CAAA,gBAAa;kEACZ,cAAA,6WAAC,2HAAA,CAAA,cAAW;;;;;;;;;;kEAEd,6WAAC,2HAAA,CAAA,gBAAa;;0EACZ,6WAAC,2HAAA,CAAA,aAAU;gEAAC,OAAM;0EAAU;;;;;;0EAC5B,6WAAC,2HAAA,CAAA,aAAU;gEAAC,OAAM;0EAAY;;;;;;0EAC9B,6WAAC,2HAAA,CAAA,aAAU;gEAAC,OAAM;0EAAU;;;;;;0EAC5B,6WAAC,2HAAA,CAAA,aAAU;gEAAC,OAAM;0EAAS;;;;;;0EAC3B,6WAAC,2HAAA,CAAA,aAAU;gEAAC,OAAM;0EAAS;;;;;;;;;;;;;;;;;;;;;;;;kDAIjC,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,2HAAA,CAAA,SAAM;gDACL,IAAG;gDACH,SAAS,SAAS,QAAQ;gDAC1B,iBAAiB,CAAC,UAChB,YAAY;wDAAE,GAAG,QAAQ;wDAAE,UAAU;oDAAQ;;;;;;0DAGjD,6WAAC,0HAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAe;;;;;;;;;;;;kDAEhC,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,2HAAA,CAAA,SAAM;gDACL,IAAG;gDACH,SAAS,SAAS,SAAS;gDAC3B,iBAAiB,CAAC,UAChB,YAAY;wDAAE,GAAG,QAAQ;wDAAE,WAAW;oDAAQ;;;;;;0DAGlD,6WAAC,0HAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAgB;;;;;;;;;;;;;;;;;;0CAGnC,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,2HAAA,CAAA,SAAM;wCACL,SAAS;wCACT,WAAU;;0DAEV,6WAAC,wRAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGpC,6WAAC,2HAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,SAAS,IAAM,eAAe;;0DACtD,6WAAC,gRAAA,CAAA,IAAC;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;0BASxC,6WAAC;gBACC,WACE,aAAa,SACT,6CACA;0BAGL,eAAe,GAAG,CAAC,CAAC,sBACnB,6WAAC,yHAAA,CAAA,OAAI;wBAEH,WAAW,CAAC,+BAA+B,EACzC,CAAC,MAAM,QAAQ,GAAG,eAAe,IACjC;kCAEF,cAAA,6WAAC,yHAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6WAAC;oCAAI,WAAU;;sDACb,6WAAC;4CAAI,WAAU;;8DACb,6WAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,iBAAiB,MAAM,OAAO;oDAAC;;;;;;8DAE1C,6WAAC;;sEACC,6WAAC;4DAAG,WAAU;sEACX,MAAM,IAAI;;;;;;sEAEb,6WAAC;4DAAE,WAAU;sEAAyB,MAAM,OAAO;;;;;;sEACnD,6WAAC;4DAAI,WAAU;;8EACb,6WAAC,0HAAA,CAAA,QAAK;oEACJ,SAAQ;oEACR,WAAW,CAAC,QAAQ,EAClB,MAAM,QAAQ,KAAK,YACf,kCACA,MAAM,QAAQ,KAAK,cACjB,oCACA,MAAM,QAAQ,KAAK,YACjB,kCACA,MAAM,QAAQ,KAAK,WACjB,sCACA,iCACV;8EAED,MAAM,QAAQ;;;;;;gEAEhB,MAAM,SAAS,kBACd,6WAAC,0HAAA,CAAA,QAAK;oEAAC,SAAQ;oEAAY,WAAU;;sFACnC,6WAAC,8RAAA,CAAA,WAAQ;4EAAC,WAAU;;;;;;wEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;sDAO/C,6WAAC,qIAAA,CAAA,eAAY;;8DACX,6WAAC,qIAAA,CAAA,sBAAmB;oDAAC,OAAO;8DAC1B,cAAA,6WAAC,2HAAA,CAAA,SAAM;wDAAC,SAAQ;wDAAQ,MAAK;kEAC3B,cAAA,6WAAC,oSAAA,CAAA,iBAAc;4DAAC,WAAU;;;;;;;;;;;;;;;;8DAG9B,6WAAC,qIAAA,CAAA,sBAAmB;oDAAC,OAAM;;sEACzB,6WAAC,qIAAA,CAAA,mBAAgB;;8EACf,6WAAC,+RAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;sEAGnC,6WAAC,qIAAA,CAAA,mBAAgB;sEACd,MAAM,QAAQ,iBACb;;kFACE,6WAAC,8RAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;oEAAiB;;6FAIrC;;kFACE,6WAAC,oRAAA,CAAA,MAAG;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;sEAKtC,6WAAC,qIAAA,CAAA,wBAAqB;;;;;sEACtB,6WAAC,qIAAA,CAAA,mBAAgB;4DAAC,WAAU;;8EAC1B,6WAAC,8RAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;gCAM1C,MAAM,WAAW,kBAChB,6WAAC;oCAAE,WAAU;8CACV,MAAM,WAAW;;;;;;8CAGtB,6WAAC;oCAAI,WAAU;;sDACb,6WAAC;;gDAAM,MAAM,YAAY;gDAAC;;;;;;;sDAC1B,6WAAC;4CACC,WAAW,MAAM,QAAQ,GAAG,mBAAmB;sDAE9C,MAAM,QAAQ,GAAG,WAAW;;;;;;;;;;;;;;;;;;uBArF9B,MAAM,EAAE;;;;;;;;;;YA6FlB,eAAe,MAAM,KAAK,mBACzB,6WAAC,yHAAA,CAAA,OAAI;0BACH,cAAA,6WAAC,yHAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,6WAAC,4RAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;sCACnB,6WAAC;4BAAG,WAAU;sCAA2C;;;;;;sCAGzD,6WAAC;4BAAE,WAAU;sCACV,eAAe,mBAAmB,SAAS,iBAAiB,QACzD,yCACA;;;;;;wBAEL,CAAC,eACA,mBAAmB,SACnB,iBAAiB,uBACf,6WAAC,2HAAA,CAAA,SAAM;4BACL,WAAU;4BACV,SAAS,IAAM,eAAe;;8CAE9B,6WAAC,sRAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;;;;;;;AASnD", "debugId": null}}, {"offset": {"line": 5577, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/pages/management/MaterialManagerEnhanced.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\n\nimport {\n  Archive,\n  Atom,\n  Copy,\n  Edit,\n  Eye,\n  Filter,\n  Grid3X3,\n  Layers,\n  List,\n  MoreHorizontal,\n  Package,\n  Pencil,\n  Plus,\n  Recycle,\n  Save,\n  Search,\n  Shield,\n  SortAsc,\n  SortDesc,\n  Sparkles,\n  Tag,\n  Trash2,\n  TrendingUp,\n  X,\n} from \"lucide-react\";\nimport { toast } from \"sonner\";\n\nimport { Badge } from \"@/components/ui/badge\";\nimport { Button } from \"@/components/ui/button\";\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from \"@/components/ui/dropdown-menu\";\nimport { Input } from \"@/components/ui/input\";\nimport { Label } from \"@/components/ui/label\";\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from \"@/components/ui/select\";\nimport { Switch } from \"@/components/ui/switch\";\nimport { Textarea } from \"@/components/ui/textarea\";\n\nexport type Material = {\n  id: string;\n  name: string;\n  description: string;\n  slug: string;\n  icon?: string;\n  color?: string;\n  type: \"natural\" | \"synthetic\" | \"composite\" | \"recycled\";\n  isActive: boolean;\n  isEcoFriendly: boolean;\n  isDurable: boolean;\n  productCount: number;\n  properties: string[];\n  careInstructions?: string;\n  origin?: string;\n  sortOrder: number;\n  createdAt: string;\n  updatedAt: string;\n};\n\n// Mock materials with enhanced data\nconst mockMaterials: Material[] = [\n  {\n    id: \"mat-1\",\n    name: \"Organic Cotton\",\n    description:\n      \"100% organic cotton grown without harmful chemicals or pesticides\",\n    slug: \"organic-cotton\",\n    icon: \"🌱\",\n    color: \"#10B981\",\n    type: \"natural\",\n    isActive: true,\n    isEcoFriendly: true,\n    isDurable: true,\n    productCount: 156,\n    properties: [\"Breathable\", \"Soft\", \"Hypoallergenic\", \"Biodegradable\"],\n    careInstructions: \"Machine wash cold, tumble dry low\",\n    origin: \"India\",\n    sortOrder: 1,\n    createdAt: \"2024-01-01T00:00:00Z\",\n    updatedAt: \"2024-01-15T10:30:00Z\",\n  },\n  {\n    id: \"mat-2\",\n    name: \"Recycled Polyester\",\n    description: \"High-quality polyester made from recycled plastic bottles\",\n    slug: \"recycled-polyester\",\n    icon: \"♻️\",\n    color: \"#3B82F6\",\n    type: \"recycled\",\n    isActive: true,\n    isEcoFriendly: true,\n    isDurable: true,\n    productCount: 234,\n    properties: [\"Water-resistant\", \"Quick-dry\", \"Lightweight\", \"Durable\"],\n    careInstructions: \"Machine wash warm, hang dry\",\n    origin: \"Global\",\n    sortOrder: 2,\n    createdAt: \"2024-01-02T00:00:00Z\",\n    updatedAt: \"2024-01-14T16:45:00Z\",\n  },\n  {\n    id: \"mat-3\",\n    name: \"Genuine Leather\",\n    description: \"Premium full-grain leather from ethically sourced cattle\",\n    slug: \"genuine-leather\",\n    icon: \"🐄\",\n    color: \"#92400E\",\n    type: \"natural\",\n    isActive: true,\n    isEcoFriendly: false,\n    isDurable: true,\n    productCount: 89,\n    properties: [\"Durable\", \"Flexible\", \"Ages beautifully\", \"Water-resistant\"],\n    careInstructions: \"Clean with leather conditioner, avoid water\",\n    origin: \"Italy\",\n    sortOrder: 3,\n    createdAt: \"2024-01-03T00:00:00Z\",\n    updatedAt: \"2024-01-13T09:20:00Z\",\n  },\n  {\n    id: \"mat-4\",\n    name: \"Bamboo Fiber\",\n    description:\n      \"Sustainable bamboo fiber with natural antibacterial properties\",\n    slug: \"bamboo-fiber\",\n    icon: \"🎋\",\n    color: \"#059669\",\n    type: \"natural\",\n    isActive: true,\n    isEcoFriendly: true,\n    isDurable: false,\n    productCount: 67,\n    properties: [\"Antibacterial\", \"Moisture-wicking\", \"Soft\", \"Renewable\"],\n    careInstructions: \"Gentle machine wash, air dry\",\n    origin: \"China\",\n    sortOrder: 4,\n    createdAt: \"2024-01-04T00:00:00Z\",\n    updatedAt: \"2024-01-12T14:15:00Z\",\n  },\n  {\n    id: \"mat-5\",\n    name: \"Carbon Fiber\",\n    description: \"Ultra-lightweight and strong carbon fiber composite material\",\n    slug: \"carbon-fiber\",\n    icon: \"⚫\",\n    color: \"#1F2937\",\n    type: \"composite\",\n    isActive: true,\n    isEcoFriendly: false,\n    isDurable: true,\n    productCount: 23,\n    properties: [\n      \"Ultra-light\",\n      \"High strength\",\n      \"Corrosion resistant\",\n      \"Conductive\",\n    ],\n    careInstructions: \"Wipe clean with dry cloth, avoid harsh chemicals\",\n    origin: \"Japan\",\n    sortOrder: 5,\n    createdAt: \"2024-01-05T00:00:00Z\",\n    updatedAt: \"2024-01-11T11:30:00Z\",\n  },\n  {\n    id: \"mat-6\",\n    name: \"Merino Wool\",\n    description:\n      \"Premium merino wool known for its softness and temperature regulation\",\n    slug: \"merino-wool\",\n    icon: \"🐑\",\n    color: \"#F59E0B\",\n    type: \"natural\",\n    isActive: false,\n    isEcoFriendly: true,\n    isDurable: true,\n    productCount: 45,\n    properties: [\n      \"Temperature regulating\",\n      \"Odor resistant\",\n      \"Soft\",\n      \"Moisture-wicking\",\n    ],\n    careInstructions: \"Hand wash or gentle cycle, lay flat to dry\",\n    origin: \"New Zealand\",\n    sortOrder: 6,\n    createdAt: \"2024-01-06T00:00:00Z\",\n    updatedAt: \"2024-01-10T08:45:00Z\",\n  },\n];\n\ntype MaterialManagerProps = {\n  initialMaterials?: Material[];\n  onMaterialsChange?: (materials: Material[]) => void;\n};\n\n/**\n * Enhanced component for managing product materials with professional UI\n */\nexport const MaterialManagerEnhanced = ({\n  initialMaterials = mockMaterials,\n  onMaterialsChange,\n}: MaterialManagerProps) => {\n  const [materials, setMaterials] = useState<Material[]>(initialMaterials);\n  const [newMaterial, setNewMaterial] = useState<Partial<Material>>({\n    name: \"\",\n    description: \"\",\n    icon: \"\",\n    color: \"#10B981\",\n    type: \"natural\",\n    isActive: true,\n    isEcoFriendly: false,\n    isDurable: true,\n    properties: [],\n    careInstructions: \"\",\n    origin: \"\",\n  });\n  const [editingMaterialId, setEditingMaterialId] = useState<string | null>(\n    null\n  );\n  const [editForm, setEditForm] = useState<Partial<Material>>({});\n  const [searchQuery, setSearchQuery] = useState(\"\");\n  const [filterStatus, setFilterStatus] = useState(\"all\");\n  const [filterType, setFilterType] = useState(\"all\");\n  const [filterEco, setFilterEco] = useState(\"all\");\n  const [sortBy, setSortBy] = useState(\"name\");\n  const [sortOrder, setSortOrder] = useState<\"asc\" | \"desc\">(\"asc\");\n  const [viewMode, setViewMode] = useState<\"grid\" | \"list\">(\"grid\");\n  const [showAddForm, setShowAddForm] = useState(false);\n\n  // Generate a slug from the material name\n  const generateSlug = (name: string) => {\n    return name\n      .toLowerCase()\n      .replace(/[^a-z0-9]+/g, \"-\")\n      .replace(/^-|-$/g, \"\");\n  };\n\n  // Filter and sort materials\n  const filteredAndSortedMaterials = materials\n    .filter((material) => {\n      const matchesSearch =\n        material.name.toLowerCase().includes(searchQuery.toLowerCase()) ||\n        material.description\n          .toLowerCase()\n          .includes(searchQuery.toLowerCase()) ||\n        material.properties.some((prop) =>\n          prop.toLowerCase().includes(searchQuery.toLowerCase())\n        );\n\n      const matchesStatus =\n        filterStatus === \"all\" ||\n        (filterStatus === \"active\" && material.isActive) ||\n        (filterStatus === \"inactive\" && !material.isActive);\n\n      const matchesType = filterType === \"all\" || material.type === filterType;\n\n      const matchesEco =\n        filterEco === \"all\" ||\n        (filterEco === \"eco\" && material.isEcoFriendly) ||\n        (filterEco === \"non-eco\" && !material.isEcoFriendly);\n\n      return matchesSearch && matchesStatus && matchesType && matchesEco;\n    })\n    .sort((a, b) => {\n      let comparison = 0;\n      switch (sortBy) {\n        case \"name\":\n          comparison = a.name.localeCompare(b.name);\n          break;\n        case \"products\":\n          comparison = a.productCount - b.productCount;\n          break;\n        case \"type\":\n          comparison = a.type.localeCompare(b.type);\n          break;\n        case \"created\":\n          comparison =\n            new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();\n          break;\n        default:\n          comparison = a.sortOrder - b.sortOrder;\n      }\n      return sortOrder === \"asc\" ? comparison : -comparison;\n    });\n\n  // Add a new material\n  const handleAddMaterial = () => {\n    if (!newMaterial.name) {\n      toast.error(\"Material name is required\");\n      return;\n    }\n\n    const slug = generateSlug(newMaterial.name);\n\n    // Check if slug already exists\n    if (materials.some((material) => material.slug === slug)) {\n      toast.error(\"A material with this name already exists\");\n      return;\n    }\n\n    const newMaterialWithId: Material = {\n      id: `material-${Date.now()}`,\n      name: newMaterial.name,\n      description: newMaterial.description || \"\",\n      slug,\n      icon: newMaterial.icon || \"🧵\",\n      color: newMaterial.color || \"#10B981\",\n      type: newMaterial.type || \"natural\",\n      isActive: newMaterial.isActive ?? true,\n      isEcoFriendly: newMaterial.isEcoFriendly ?? false,\n      isDurable: newMaterial.isDurable ?? true,\n      productCount: 0,\n      properties: newMaterial.properties || [],\n      careInstructions: newMaterial.careInstructions || \"\",\n      origin: newMaterial.origin || \"\",\n      sortOrder: materials.length + 1,\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString(),\n    };\n\n    const updatedMaterials = [...materials, newMaterialWithId];\n    setMaterials(updatedMaterials);\n    setNewMaterial({\n      name: \"\",\n      description: \"\",\n      icon: \"\",\n      color: \"#10B981\",\n      type: \"natural\",\n      isActive: true,\n      isEcoFriendly: false,\n      isDurable: true,\n      properties: [],\n      careInstructions: \"\",\n      origin: \"\",\n    });\n    setShowAddForm(false);\n\n    // Notify parent component\n    if (onMaterialsChange) {\n      onMaterialsChange(updatedMaterials);\n    }\n\n    toast.success(\"Material added successfully\");\n  };\n\n  // Start editing a material\n  const handleEditStart = (material: Material) => {\n    setEditingMaterialId(material.id);\n    setEditForm({ ...material });\n  };\n\n  // Cancel editing\n  const handleEditCancel = () => {\n    setEditingMaterialId(null);\n    setEditForm({});\n  };\n\n  // Save edited material\n  const handleEditSave = () => {\n    if (!editForm.name) {\n      toast.error(\"Material name is required\");\n      return;\n    }\n\n    const updatedMaterials = materials.map((material) =>\n      material.id === editingMaterialId\n        ? {\n            ...material,\n            name: editForm.name || material.name,\n            description: editForm.description || material.description,\n            icon: editForm.icon || material.icon,\n            color: editForm.color || material.color,\n            type: editForm.type || material.type,\n            isActive: editForm.isActive ?? material.isActive,\n            isEcoFriendly: editForm.isEcoFriendly ?? material.isEcoFriendly,\n            isDurable: editForm.isDurable ?? material.isDurable,\n            properties: editForm.properties || material.properties,\n            careInstructions:\n              editForm.careInstructions || material.careInstructions,\n            origin: editForm.origin || material.origin,\n            // Only update slug if name changed\n            slug:\n              material.name !== editForm.name\n                ? generateSlug(editForm.name)\n                : material.slug,\n            updatedAt: new Date().toISOString(),\n          }\n        : material\n    );\n\n    setMaterials(updatedMaterials);\n    setEditingMaterialId(null);\n    setEditForm({});\n\n    // Notify parent component\n    if (onMaterialsChange) {\n      onMaterialsChange(updatedMaterials);\n    }\n\n    toast.success(\"Material updated successfully\");\n  };\n\n  // Delete a material\n  const handleDeleteMaterial = (materialId: string) => {\n    const updatedMaterials = materials.filter(\n      (material) => material.id !== materialId\n    );\n    setMaterials(updatedMaterials);\n\n    // Notify parent component\n    if (onMaterialsChange) {\n      onMaterialsChange(updatedMaterials);\n    }\n\n    toast.success(\"Material deleted successfully\");\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header with Statistics */}\n      <div className=\"grid gap-4 md:grid-cols-4\">\n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center gap-3\">\n              <div className=\"rounded-full bg-purple-100 p-2\">\n                <Layers className=\"h-5 w-5 text-purple-600\" />\n              </div>\n              <div>\n                <p className=\"text-sm text-gray-600\">Total Materials</p>\n                <p className=\"text-2xl font-bold\">{materials.length}</p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center gap-3\">\n              <div className=\"rounded-full bg-green-100 p-2\">\n                <Recycle className=\"h-5 w-5 text-green-600\" />\n              </div>\n              <div>\n                <p className=\"text-sm text-gray-600\">Eco-Friendly</p>\n                <p className=\"text-2xl font-bold\">\n                  {materials.filter((m) => m.isEcoFriendly).length}\n                </p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center gap-3\">\n              <div className=\"rounded-full bg-blue-100 p-2\">\n                <Shield className=\"h-5 w-5 text-blue-600\" />\n              </div>\n              <div>\n                <p className=\"text-sm text-gray-600\">Durable</p>\n                <p className=\"text-2xl font-bold\">\n                  {materials.filter((m) => m.isDurable).length}\n                </p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center gap-3\">\n              <div className=\"rounded-full bg-orange-100 p-2\">\n                <Atom className=\"h-5 w-5 text-orange-600\" />\n              </div>\n              <div>\n                <p className=\"text-sm text-gray-600\">Natural</p>\n                <p className=\"text-2xl font-bold\">\n                  {materials.filter((m) => m.type === \"natural\").length}\n                </p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* Controls */}\n      <Card>\n        <CardContent className=\"p-6\">\n          <div className=\"flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between\">\n            {/* Search and Filters */}\n            <div className=\"flex flex-1 gap-4\">\n              <div className=\"relative max-w-md flex-1\">\n                <Search className=\"absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400\" />\n                <Input\n                  placeholder=\"Search materials...\"\n                  value={searchQuery}\n                  onChange={(e) => setSearchQuery(e.target.value)}\n                  className=\"pl-10\"\n                />\n              </div>\n\n              <Select value={filterStatus} onValueChange={setFilterStatus}>\n                <SelectTrigger className=\"w-32\">\n                  <SelectValue />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"all\">All Status</SelectItem>\n                  <SelectItem value=\"active\">Active</SelectItem>\n                  <SelectItem value=\"inactive\">Inactive</SelectItem>\n                </SelectContent>\n              </Select>\n\n              <Select value={filterType} onValueChange={setFilterType}>\n                <SelectTrigger className=\"w-32\">\n                  <SelectValue />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"all\">All Types</SelectItem>\n                  <SelectItem value=\"natural\">Natural</SelectItem>\n                  <SelectItem value=\"synthetic\">Synthetic</SelectItem>\n                  <SelectItem value=\"composite\">Composite</SelectItem>\n                  <SelectItem value=\"recycled\">Recycled</SelectItem>\n                </SelectContent>\n              </Select>\n\n              <Select value={filterEco} onValueChange={setFilterEco}>\n                <SelectTrigger className=\"w-32\">\n                  <SelectValue />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"all\">All Eco</SelectItem>\n                  <SelectItem value=\"eco\">Eco-Friendly</SelectItem>\n                  <SelectItem value=\"non-eco\">Standard</SelectItem>\n                </SelectContent>\n              </Select>\n            </div>\n\n            {/* View Controls */}\n            <div className=\"flex items-center gap-2\">\n              <Button\n                variant={sortOrder === \"asc\" ? \"default\" : \"outline\"}\n                size=\"sm\"\n                onClick={() =>\n                  setSortOrder(sortOrder === \"asc\" ? \"desc\" : \"asc\")\n                }\n              >\n                {sortOrder === \"asc\" ? (\n                  <SortAsc className=\"h-4 w-4\" />\n                ) : (\n                  <SortDesc className=\"h-4 w-4\" />\n                )}\n              </Button>\n\n              <div className=\"flex rounded-md border\">\n                <Button\n                  variant={viewMode === \"grid\" ? \"default\" : \"ghost\"}\n                  size=\"sm\"\n                  onClick={() => setViewMode(\"grid\")}\n                  className=\"rounded-r-none\"\n                >\n                  <Grid3X3 className=\"h-4 w-4\" />\n                </Button>\n                <Button\n                  variant={viewMode === \"list\" ? \"default\" : \"ghost\"}\n                  size=\"sm\"\n                  onClick={() => setViewMode(\"list\")}\n                  className=\"rounded-l-none\"\n                >\n                  <List className=\"h-4 w-4\" />\n                </Button>\n              </div>\n\n              <Button onClick={() => setShowAddForm(true)}>\n                <Plus className=\"mr-2 h-4 w-4\" />\n                Add Material\n              </Button>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Materials Display - Grid View */}\n      <div className=\"grid gap-4 md:grid-cols-2 lg:grid-cols-3\">\n        {filteredAndSortedMaterials.map((material) => (\n          <Card key={material.id} className=\"transition-shadow hover:shadow-md\">\n            <CardContent className=\"p-4\">\n              <div className=\"space-y-3\">\n                <div className=\"flex items-start justify-between\">\n                  <div className=\"flex items-center gap-3\">\n                    <span className=\"text-3xl\">{material.icon}</span>\n                    <div>\n                      <h3 className=\"font-semibold\">{material.name}</h3>\n                      <div className=\"flex flex-wrap gap-1\">\n                        <Badge\n                          variant={material.isActive ? \"default\" : \"secondary\"}\n                          className=\"text-xs\"\n                        >\n                          {material.isActive ? \"Active\" : \"Inactive\"}\n                        </Badge>\n                        <Badge\n                          variant=\"outline\"\n                          className=\"text-xs\"\n                          style={{\n                            borderColor: material.color,\n                            color: material.color,\n                          }}\n                        >\n                          {material.type}\n                        </Badge>\n                        {material.isEcoFriendly && (\n                          <Badge\n                            variant=\"outline\"\n                            className=\"border-green-200 text-xs text-green-600\"\n                          >\n                            Eco-Friendly\n                          </Badge>\n                        )}\n                        {material.isDurable && (\n                          <Badge\n                            variant=\"outline\"\n                            className=\"border-blue-200 text-xs text-blue-600\"\n                          >\n                            Durable\n                          </Badge>\n                        )}\n                      </div>\n                    </div>\n                  </div>\n\n                  <DropdownMenu>\n                    <DropdownMenuTrigger asChild>\n                      <Button variant=\"ghost\" size=\"sm\">\n                        <MoreHorizontal className=\"h-4 w-4\" />\n                      </Button>\n                    </DropdownMenuTrigger>\n                    <DropdownMenuContent align=\"end\">\n                      <DropdownMenuItem\n                        onClick={() => handleEditStart(material)}\n                      >\n                        <Edit className=\"mr-2 h-4 w-4\" />\n                        Edit\n                      </DropdownMenuItem>\n                      <DropdownMenuItem>\n                        <Eye className=\"mr-2 h-4 w-4\" />\n                        View Products\n                      </DropdownMenuItem>\n                      <DropdownMenuItem>\n                        <Copy className=\"mr-2 h-4 w-4\" />\n                        Duplicate\n                      </DropdownMenuItem>\n                      <DropdownMenuSeparator />\n                      <DropdownMenuItem>\n                        <Archive className=\"mr-2 h-4 w-4\" />\n                        Archive\n                      </DropdownMenuItem>\n                      <DropdownMenuItem\n                        onClick={() => handleDeleteMaterial(material.id)}\n                        className=\"text-red-600\"\n                      >\n                        <Trash2 className=\"mr-2 h-4 w-4\" />\n                        Delete\n                      </DropdownMenuItem>\n                    </DropdownMenuContent>\n                  </DropdownMenu>\n                </div>\n\n                <p className=\"line-clamp-2 text-sm text-gray-600\">\n                  {material.description || \"No description provided\"}\n                </p>\n\n                <div className=\"space-y-2 text-xs text-gray-500\">\n                  <div className=\"flex items-center justify-between\">\n                    <span>{material.productCount} products</span>\n                    {material.origin && <span>Origin: {material.origin}</span>}\n                  </div>\n\n                  {material.properties.length > 0 && (\n                    <div className=\"flex flex-wrap gap-1\">\n                      {material.properties\n                        .slice(0, 3)\n                        .map((property, index) => (\n                          <span\n                            key={index}\n                            className=\"rounded bg-gray-100 px-2 py-1 text-xs\"\n                          >\n                            {property}\n                          </span>\n                        ))}\n                      {material.properties.length > 3 && (\n                        <span className=\"rounded bg-gray-100 px-2 py-1 text-xs\">\n                          +{material.properties.length - 3} more\n                        </span>\n                      )}\n                    </div>\n                  )}\n\n                  {material.careInstructions && (\n                    <p className=\"text-xs italic text-gray-400\">\n                      Care: {material.careInstructions}\n                    </p>\n                  )}\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        ))}\n      </div>\n\n      {/* Empty State */}\n      {filteredAndSortedMaterials.length === 0 && (\n        <Card>\n          <CardContent className=\"p-12 text-center\">\n            <Layers className=\"mx-auto h-12 w-12 text-gray-400\" />\n            <h3 className=\"mt-2 text-lg font-medium text-gray-900\">\n              No materials found\n            </h3>\n            <p className=\"mt-1 text-sm text-gray-500\">\n              {searchQuery ||\n              filterStatus !== \"all\" ||\n              filterType !== \"all\" ||\n              filterEco !== \"all\"\n                ? \"Try adjusting your search or filter criteria.\"\n                : \"Get started by adding your first material.\"}\n            </p>\n            {!searchQuery &&\n              filterStatus === \"all\" &&\n              filterType === \"all\" &&\n              filterEco === \"all\" && (\n                <Button className=\"mt-4\" onClick={() => setShowAddForm(true)}>\n                  <Plus className=\"mr-2 h-4 w-4\" />\n                  Add Your First Material\n                </Button>\n              )}\n          </CardContent>\n        </Card>\n      )}\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA0BA;AAEA;AACA;AACA;AACA;AAOA;AAEA;AA5CA;;;;;;;;;;;AA0EA,oCAAoC;AACpC,MAAM,gBAA4B;IAChC;QACE,IAAI;QACJ,MAAM;QACN,aACE;QACF,MAAM;QACN,MAAM;QACN,OAAO;QACP,MAAM;QACN,UAAU;QACV,eAAe;QACf,WAAW;QACX,cAAc;QACd,YAAY;YAAC;YAAc;YAAQ;YAAkB;SAAgB;QACrE,kBAAkB;QAClB,QAAQ;QACR,WAAW;QACX,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,MAAM;QACN,OAAO;QACP,MAAM;QACN,UAAU;QACV,eAAe;QACf,WAAW;QACX,cAAc;QACd,YAAY;YAAC;YAAmB;YAAa;YAAe;SAAU;QACtE,kBAAkB;QAClB,QAAQ;QACR,WAAW;QACX,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,MAAM;QACN,OAAO;QACP,MAAM;QACN,UAAU;QACV,eAAe;QACf,WAAW;QACX,cAAc;QACd,YAAY;YAAC;YAAW;YAAY;YAAoB;SAAkB;QAC1E,kBAAkB;QAClB,QAAQ;QACR,WAAW;QACX,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,aACE;QACF,MAAM;QACN,MAAM;QACN,OAAO;QACP,MAAM;QACN,UAAU;QACV,eAAe;QACf,WAAW;QACX,cAAc;QACd,YAAY;YAAC;YAAiB;YAAoB;YAAQ;SAAY;QACtE,kBAAkB;QAClB,QAAQ;QACR,WAAW;QACX,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,MAAM;QACN,OAAO;QACP,MAAM;QACN,UAAU;QACV,eAAe;QACf,WAAW;QACX,cAAc;QACd,YAAY;YACV;YACA;YACA;YACA;SACD;QACD,kBAAkB;QAClB,QAAQ;QACR,WAAW;QACX,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,aACE;QACF,MAAM;QACN,MAAM;QACN,OAAO;QACP,MAAM;QACN,UAAU;QACV,eAAe;QACf,WAAW;QACX,cAAc;QACd,YAAY;YACV;YACA;YACA;YACA;SACD;QACD,kBAAkB;QAClB,QAAQ;QACR,WAAW;QACX,WAAW;QACX,WAAW;IACb;CACD;AAUM,MAAM,0BAA0B,CAAC,EACtC,mBAAmB,aAAa,EAChC,iBAAiB,EACI;IACrB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAc;IACvD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAqB;QAChE,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;QACP,MAAM;QACN,UAAU;QACV,eAAe;QACf,WAAW;QACX,YAAY,EAAE;QACd,kBAAkB;QAClB,QAAQ;IACV;IACA,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EACvD;IAEF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAqB,CAAC;IAC7D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAkB;IAC3D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAmB;IAC1D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,yCAAyC;IACzC,MAAM,eAAe,CAAC;QACpB,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,eAAe,KACvB,OAAO,CAAC,UAAU;IACvB;IAEA,4BAA4B;IAC5B,MAAM,6BAA6B,UAChC,MAAM,CAAC,CAAC;QACP,MAAM,gBACJ,SAAS,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OAC5D,SAAS,WAAW,CACjB,WAAW,GACX,QAAQ,CAAC,YAAY,WAAW,OACnC,SAAS,UAAU,CAAC,IAAI,CAAC,CAAC,OACxB,KAAK,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;QAGvD,MAAM,gBACJ,iBAAiB,SAChB,iBAAiB,YAAY,SAAS,QAAQ,IAC9C,iBAAiB,cAAc,CAAC,SAAS,QAAQ;QAEpD,MAAM,cAAc,eAAe,SAAS,SAAS,IAAI,KAAK;QAE9D,MAAM,aACJ,cAAc,SACb,cAAc,SAAS,SAAS,aAAa,IAC7C,cAAc,aAAa,CAAC,SAAS,aAAa;QAErD,OAAO,iBAAiB,iBAAiB,eAAe;IAC1D,GACC,IAAI,CAAC,CAAC,GAAG;QACR,IAAI,aAAa;QACjB,OAAQ;YACN,KAAK;gBACH,aAAa,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,IAAI;gBACxC;YACF,KAAK;gBACH,aAAa,EAAE,YAAY,GAAG,EAAE,YAAY;gBAC5C;YACF,KAAK;gBACH,aAAa,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,IAAI;gBACxC;YACF,KAAK;gBACH,aACE,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO;gBACjE;YACF;gBACE,aAAa,EAAE,SAAS,GAAG,EAAE,SAAS;QAC1C;QACA,OAAO,cAAc,QAAQ,aAAa,CAAC;IAC7C;IAEF,qBAAqB;IACrB,MAAM,oBAAoB;QACxB,IAAI,CAAC,YAAY,IAAI,EAAE;YACrB,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,MAAM,OAAO,aAAa,YAAY,IAAI;QAE1C,+BAA+B;QAC/B,IAAI,UAAU,IAAI,CAAC,CAAC,WAAa,SAAS,IAAI,KAAK,OAAO;YACxD,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,MAAM,oBAA8B;YAClC,IAAI,CAAC,SAAS,EAAE,KAAK,GAAG,IAAI;YAC5B,MAAM,YAAY,IAAI;YACtB,aAAa,YAAY,WAAW,IAAI;YACxC;YACA,MAAM,YAAY,IAAI,IAAI;YAC1B,OAAO,YAAY,KAAK,IAAI;YAC5B,MAAM,YAAY,IAAI,IAAI;YAC1B,UAAU,YAAY,QAAQ,IAAI;YAClC,eAAe,YAAY,aAAa,IAAI;YAC5C,WAAW,YAAY,SAAS,IAAI;YACpC,cAAc;YACd,YAAY,YAAY,UAAU,IAAI,EAAE;YACxC,kBAAkB,YAAY,gBAAgB,IAAI;YAClD,QAAQ,YAAY,MAAM,IAAI;YAC9B,WAAW,UAAU,MAAM,GAAG;YAC9B,WAAW,IAAI,OAAO,WAAW;YACjC,WAAW,IAAI,OAAO,WAAW;QACnC;QAEA,MAAM,mBAAmB;eAAI;YAAW;SAAkB;QAC1D,aAAa;QACb,eAAe;YACb,MAAM;YACN,aAAa;YACb,MAAM;YACN,OAAO;YACP,MAAM;YACN,UAAU;YACV,eAAe;YACf,WAAW;YACX,YAAY,EAAE;YACd,kBAAkB;YAClB,QAAQ;QACV;QACA,eAAe;QAEf,0BAA0B;QAC1B,IAAI,mBAAmB;YACrB,kBAAkB;QACpB;QAEA,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,2BAA2B;IAC3B,MAAM,kBAAkB,CAAC;QACvB,qBAAqB,SAAS,EAAE;QAChC,YAAY;YAAE,GAAG,QAAQ;QAAC;IAC5B;IAEA,iBAAiB;IACjB,MAAM,mBAAmB;QACvB,qBAAqB;QACrB,YAAY,CAAC;IACf;IAEA,uBAAuB;IACvB,MAAM,iBAAiB;QACrB,IAAI,CAAC,SAAS,IAAI,EAAE;YAClB,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,MAAM,mBAAmB,UAAU,GAAG,CAAC,CAAC,WACtC,SAAS,EAAE,KAAK,oBACZ;gBACE,GAAG,QAAQ;gBACX,MAAM,SAAS,IAAI,IAAI,SAAS,IAAI;gBACpC,aAAa,SAAS,WAAW,IAAI,SAAS,WAAW;gBACzD,MAAM,SAAS,IAAI,IAAI,SAAS,IAAI;gBACpC,OAAO,SAAS,KAAK,IAAI,SAAS,KAAK;gBACvC,MAAM,SAAS,IAAI,IAAI,SAAS,IAAI;gBACpC,UAAU,SAAS,QAAQ,IAAI,SAAS,QAAQ;gBAChD,eAAe,SAAS,aAAa,IAAI,SAAS,aAAa;gBAC/D,WAAW,SAAS,SAAS,IAAI,SAAS,SAAS;gBACnD,YAAY,SAAS,UAAU,IAAI,SAAS,UAAU;gBACtD,kBACE,SAAS,gBAAgB,IAAI,SAAS,gBAAgB;gBACxD,QAAQ,SAAS,MAAM,IAAI,SAAS,MAAM;gBAC1C,mCAAmC;gBACnC,MACE,SAAS,IAAI,KAAK,SAAS,IAAI,GAC3B,aAAa,SAAS,IAAI,IAC1B,SAAS,IAAI;gBACnB,WAAW,IAAI,OAAO,WAAW;YACnC,IACA;QAGN,aAAa;QACb,qBAAqB;QACrB,YAAY,CAAC;QAEb,0BAA0B;QAC1B,IAAI,mBAAmB;YACrB,kBAAkB;QACpB;QAEA,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,oBAAoB;IACpB,MAAM,uBAAuB,CAAC;QAC5B,MAAM,mBAAmB,UAAU,MAAM,CACvC,CAAC,WAAa,SAAS,EAAE,KAAK;QAEhC,aAAa;QAEb,0BAA0B;QAC1B,IAAI,mBAAmB;YACrB,kBAAkB;QACpB;QAEA,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,qBACE,6WAAC;QAAI,WAAU;;0BAEb,6WAAC;gBAAI,WAAU;;kCACb,6WAAC,yHAAA,CAAA,OAAI;kCACH,cAAA,6WAAC,yHAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;kDACb,cAAA,6WAAC,0RAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;kDAEpB,6WAAC;;0DACC,6WAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,6WAAC;gDAAE,WAAU;0DAAsB,UAAU,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAM3D,6WAAC,yHAAA,CAAA,OAAI;kCACH,cAAA,6WAAC,yHAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;kDACb,cAAA,6WAAC,4RAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;;;;;;kDAErB,6WAAC;;0DACC,6WAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,6WAAC;gDAAE,WAAU;0DACV,UAAU,MAAM,CAAC,CAAC,IAAM,EAAE,aAAa,EAAE,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO1D,6WAAC,yHAAA,CAAA,OAAI;kCACH,cAAA,6WAAC,yHAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;kDACb,cAAA,6WAAC,0RAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;kDAEpB,6WAAC;;0DACC,6WAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,6WAAC;gDAAE,WAAU;0DACV,UAAU,MAAM,CAAC,CAAC,IAAM,EAAE,SAAS,EAAE,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOtD,6WAAC,yHAAA,CAAA,OAAI;kCACH,cAAA,6WAAC,yHAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;kDACb,cAAA,6WAAC,sRAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;kDAElB,6WAAC;;0DACC,6WAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,6WAAC;gDAAE,WAAU;0DACV,UAAU,MAAM,CAAC,CAAC,IAAM,EAAE,IAAI,KAAK,WAAW,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASjE,6WAAC,yHAAA,CAAA,OAAI;0BACH,cAAA,6WAAC,yHAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,6WAAC;wBAAI,WAAU;;0CAEb,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,0RAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,6WAAC,0HAAA,CAAA,QAAK;gDACJ,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gDAC9C,WAAU;;;;;;;;;;;;kDAId,6WAAC,2HAAA,CAAA,SAAM;wCAAC,OAAO;wCAAc,eAAe;;0DAC1C,6WAAC,2HAAA,CAAA,gBAAa;gDAAC,WAAU;0DACvB,cAAA,6WAAC,2HAAA,CAAA,cAAW;;;;;;;;;;0DAEd,6WAAC,2HAAA,CAAA,gBAAa;;kEACZ,6WAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAM;;;;;;kEACxB,6WAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAS;;;;;;kEAC3B,6WAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAW;;;;;;;;;;;;;;;;;;kDAIjC,6WAAC,2HAAA,CAAA,SAAM;wCAAC,OAAO;wCAAY,eAAe;;0DACxC,6WAAC,2HAAA,CAAA,gBAAa;gDAAC,WAAU;0DACvB,cAAA,6WAAC,2HAAA,CAAA,cAAW;;;;;;;;;;0DAEd,6WAAC,2HAAA,CAAA,gBAAa;;kEACZ,6WAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAM;;;;;;kEACxB,6WAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAU;;;;;;kEAC5B,6WAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAY;;;;;;kEAC9B,6WAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAY;;;;;;kEAC9B,6WAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAW;;;;;;;;;;;;;;;;;;kDAIjC,6WAAC,2HAAA,CAAA,SAAM;wCAAC,OAAO;wCAAW,eAAe;;0DACvC,6WAAC,2HAAA,CAAA,gBAAa;gDAAC,WAAU;0DACvB,cAAA,6WAAC,2HAAA,CAAA,cAAW;;;;;;;;;;0DAEd,6WAAC,2HAAA,CAAA,gBAAa;;kEACZ,6WAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAM;;;;;;kEACxB,6WAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAM;;;;;;kEACxB,6WAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAU;;;;;;;;;;;;;;;;;;;;;;;;0CAMlC,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,2HAAA,CAAA,SAAM;wCACL,SAAS,cAAc,QAAQ,YAAY;wCAC3C,MAAK;wCACL,SAAS,IACP,aAAa,cAAc,QAAQ,SAAS;kDAG7C,cAAc,sBACb,6WAAC,kTAAA,CAAA,UAAO;4CAAC,WAAU;;;;;iEAEnB,6WAAC,qTAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;kDAIxB,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,2HAAA,CAAA,SAAM;gDACL,SAAS,aAAa,SAAS,YAAY;gDAC3C,MAAK;gDACL,SAAS,IAAM,YAAY;gDAC3B,WAAU;0DAEV,cAAA,6WAAC,gSAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;;;;;;0DAErB,6WAAC,2HAAA,CAAA,SAAM;gDACL,SAAS,aAAa,SAAS,YAAY;gDAC3C,MAAK;gDACL,SAAS,IAAM,YAAY;gDAC3B,WAAU;0DAEV,cAAA,6WAAC,sRAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAIpB,6WAAC,2HAAA,CAAA,SAAM;wCAAC,SAAS,IAAM,eAAe;;0DACpC,6WAAC,sRAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS3C,6WAAC;gBAAI,WAAU;0BACZ,2BAA2B,GAAG,CAAC,CAAC,yBAC/B,6WAAC,yHAAA,CAAA,OAAI;wBAAmB,WAAU;kCAChC,cAAA,6WAAC,yHAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;;0DACb,6WAAC;gDAAI,WAAU;;kEACb,6WAAC;wDAAK,WAAU;kEAAY,SAAS,IAAI;;;;;;kEACzC,6WAAC;;0EACC,6WAAC;gEAAG,WAAU;0EAAiB,SAAS,IAAI;;;;;;0EAC5C,6WAAC;gEAAI,WAAU;;kFACb,6WAAC,0HAAA,CAAA,QAAK;wEACJ,SAAS,SAAS,QAAQ,GAAG,YAAY;wEACzC,WAAU;kFAET,SAAS,QAAQ,GAAG,WAAW;;;;;;kFAElC,6WAAC,0HAAA,CAAA,QAAK;wEACJ,SAAQ;wEACR,WAAU;wEACV,OAAO;4EACL,aAAa,SAAS,KAAK;4EAC3B,OAAO,SAAS,KAAK;wEACvB;kFAEC,SAAS,IAAI;;;;;;oEAEf,SAAS,aAAa,kBACrB,6WAAC,0HAAA,CAAA,QAAK;wEACJ,SAAQ;wEACR,WAAU;kFACX;;;;;;oEAIF,SAAS,SAAS,kBACjB,6WAAC,0HAAA,CAAA,QAAK;wEACJ,SAAQ;wEACR,WAAU;kFACX;;;;;;;;;;;;;;;;;;;;;;;;0DAQT,6WAAC,qIAAA,CAAA,eAAY;;kEACX,6WAAC,qIAAA,CAAA,sBAAmB;wDAAC,OAAO;kEAC1B,cAAA,6WAAC,2HAAA,CAAA,SAAM;4DAAC,SAAQ;4DAAQ,MAAK;sEAC3B,cAAA,6WAAC,oSAAA,CAAA,iBAAc;gEAAC,WAAU;;;;;;;;;;;;;;;;kEAG9B,6WAAC,qIAAA,CAAA,sBAAmB;wDAAC,OAAM;;0EACzB,6WAAC,qIAAA,CAAA,mBAAgB;gEACf,SAAS,IAAM,gBAAgB;;kFAE/B,6WAAC,+RAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAGnC,6WAAC,qIAAA,CAAA,mBAAgB;;kFACf,6WAAC,oRAAA,CAAA,MAAG;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAGlC,6WAAC,qIAAA,CAAA,mBAAgB;;kFACf,6WAAC,sRAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAGnC,6WAAC,qIAAA,CAAA,wBAAqB;;;;;0EACtB,6WAAC,qIAAA,CAAA,mBAAgB;;kFACf,6WAAC,4RAAA,CAAA,UAAO;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAGtC,6WAAC,qIAAA,CAAA,mBAAgB;gEACf,SAAS,IAAM,qBAAqB,SAAS,EAAE;gEAC/C,WAAU;;kFAEV,6WAAC,8RAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;kDAO3C,6WAAC;wCAAE,WAAU;kDACV,SAAS,WAAW,IAAI;;;;;;kDAG3B,6WAAC;wCAAI,WAAU;;0DACb,6WAAC;gDAAI,WAAU;;kEACb,6WAAC;;4DAAM,SAAS,YAAY;4DAAC;;;;;;;oDAC5B,SAAS,MAAM,kBAAI,6WAAC;;4DAAK;4DAAS,SAAS,MAAM;;;;;;;;;;;;;4CAGnD,SAAS,UAAU,CAAC,MAAM,GAAG,mBAC5B,6WAAC;gDAAI,WAAU;;oDACZ,SAAS,UAAU,CACjB,KAAK,CAAC,GAAG,GACT,GAAG,CAAC,CAAC,UAAU,sBACd,6WAAC;4DAEC,WAAU;sEAET;2DAHI;;;;;oDAMV,SAAS,UAAU,CAAC,MAAM,GAAG,mBAC5B,6WAAC;wDAAK,WAAU;;4DAAwC;4DACpD,SAAS,UAAU,CAAC,MAAM,GAAG;4DAAE;;;;;;;;;;;;;4CAMxC,SAAS,gBAAgB,kBACxB,6WAAC;gDAAE,WAAU;;oDAA+B;oDACnC,SAAS,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;uBAlHjC,SAAS,EAAE;;;;;;;;;;YA6HzB,2BAA2B,MAAM,KAAK,mBACrC,6WAAC,yHAAA,CAAA,OAAI;0BACH,cAAA,6WAAC,yHAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,6WAAC,0RAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;sCAClB,6WAAC;4BAAG,WAAU;sCAAyC;;;;;;sCAGvD,6WAAC;4BAAE,WAAU;sCACV,eACD,iBAAiB,SACjB,eAAe,SACf,cAAc,QACV,kDACA;;;;;;wBAEL,CAAC,eACA,iBAAiB,SACjB,eAAe,SACf,cAAc,uBACZ,6WAAC,2HAAA,CAAA,SAAM;4BAAC,WAAU;4BAAO,SAAS,IAAM,eAAe;;8CACrD,6WAAC,sRAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;;;;;;;AASnD", "debugId": null}}, {"offset": {"line": 6910, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Tabs = TabsPrimitive.Root\r\n\r\nconst TabsList = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.List>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.List\r\n    ref={ref}\r\n    className={cn(\r\n      \"inline-flex h-9 items-center justify-center rounded-lg bg-muted p-1 text-muted-foreground\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTabsList.displayName = TabsPrimitive.List.displayName\r\n\r\nconst TabsTrigger = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.Trigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName\r\n\r\nconst TabsContent = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.Content\r\n    ref={ref}\r\n    className={cn(\r\n      \"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTabsContent.displayName = TabsPrimitive.Content.displayName\r\n\r\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\r\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,OAAO,6QAAA,CAAA,OAAkB;AAE/B,MAAM,yBAAW,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC,6QAAA,CAAA,OAAkB;QACjB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,6FACA;QAED,GAAG,KAAK;;;;;;AAGb,SAAS,WAAW,GAAG,6QAAA,CAAA,OAAkB,CAAC,WAAW;AAErD,MAAM,4BAAc,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC,6QAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,kYACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,6QAAA,CAAA,UAAqB,CAAC,WAAW;AAE3D,MAAM,4BAAc,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC,6QAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,mIACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,6QAAA,CAAA,UAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 6963, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/app/admin/products/catalog-settings/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useEffect, useState } from \"react\";\n\nimport {\n  <PERSON>R<PERSON>,\n  <PERSON><PERSON><PERSON>3,\n  Download,\n  Filter,\n  Hammer,\n  Layers,\n  LayoutGrid,\n  Package,\n  Palette,\n  Plus,\n  Search,\n  Sparkles,\n  Tag,\n  TrendingUp,\n  Upload,\n} from \"lucide-react\";\nimport { useSearchParams } from \"next/navigation\";\n\nimport { PageHeaderWrapper } from \"@/components/common/PageHeaderWrapper\";\nimport { BrandManagerEnhanced } from \"@/components/pages/management/BrandManagerEnhanced\";\nimport { CategoryManagerEnhanced } from \"@/components/pages/management/CategoryManagerEnhanced\";\nimport { ColorManagerEnhanced } from \"@/components/pages/management/ColorManagerEnhanced\";\nimport { MaterialManagerEnhanced } from \"@/components/pages/management/MaterialManagerEnhanced\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { Button } from \"@/components/ui/button\";\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Ta<PERSON>, Ta<PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from \"@/components/ui/tabs\";\n\nexport default function CatalogSettings() {\n  const [activeTab, setActiveTab] = useState(\"overview\");\n  const [searchQuery, setSearchQuery] = useState(\"\");\n  const searchParams = useSearchParams();\n\n  // Handle URL parameters for direct tab navigation\n  useEffect(() => {\n    const tab = searchParams.get(\"tab\");\n    if (\n      tab &&\n      [\"overview\", \"categories\", \"brands\", \"materials\", \"colors\"].includes(tab)\n    ) {\n      setActiveTab(tab);\n    }\n  }, [searchParams]);\n\n  // Unified stats for all catalog metadata\n  const overallStats = [\n    {\n      label: \"Total Categories\",\n      value: \"18\",\n      change: \"+2\",\n      trend: \"up\",\n      icon: LayoutGrid,\n    },\n    {\n      label: \"Total Brands\",\n      value: \"24\",\n      change: \"+3\",\n      trend: \"up\",\n      icon: Tag,\n    },\n    {\n      label: \"Total Materials\",\n      value: \"32\",\n      change: \"+4\",\n      trend: \"up\",\n      icon: Hammer,\n    },\n    {\n      label: \"Total Colors\",\n      value: \"28\",\n      change: \"+2\",\n      trend: \"up\",\n      icon: Palette,\n    },\n  ];\n\n  const quickActions = [\n    { label: \"Import Data\", icon: Upload, action: () => console.log(\"Import\") },\n    {\n      label: \"Export All\",\n      icon: Download,\n      action: () => console.log(\"Export\"),\n    },\n    {\n      label: \"Catalog Analytics\",\n      icon: BarChart3,\n      action: () => console.log(\"Analytics\"),\n    },\n  ];\n\n  // Cross-reference data showing relationships\n  const catalogRelationships = [\n    {\n      category: \"Electronics\",\n      brands: [\"Apple\", \"Samsung\", \"Sony\"],\n      materials: [\"Aluminum\", \"Glass\", \"Plastic\"],\n      colors: [\"Black\", \"White\", \"Silver\"],\n      products: 324,\n    },\n    {\n      category: \"Clothing\",\n      brands: [\"Nike\", \"Adidas\", \"H&M\"],\n      materials: [\"Cotton\", \"Polyester\", \"Wool\"],\n      colors: [\"Red\", \"Blue\", \"Green\"],\n      products: 289,\n    },\n    {\n      category: \"Home & Garden\",\n      brands: [\"IKEA\", \"Home Depot\", \"Wayfair\"],\n      materials: [\"Wood\", \"Metal\", \"Fabric\"],\n      colors: [\"Brown\", \"White\", \"Gray\"],\n      products: 156,\n    },\n  ];\n\n  return (\n    <>\n      <PageHeaderWrapper\n        title=\"Catalog Settings\"\n        description=\"Manage categories, brands, and materials for your product catalog in one unified interface\"\n      >\n        <div className=\"flex gap-2\">\n          {quickActions.map((action, index) => (\n            <Button\n              key={index}\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={action.action}\n              className=\"hidden sm:flex\"\n            >\n              <action.icon className=\"mr-2 h-4 w-4\" />\n              {action.label}\n            </Button>\n          ))}\n          <Button size=\"sm\">\n            <Plus className=\"mr-2 h-4 w-4\" />\n            Quick Add\n          </Button>\n        </div>\n      </PageHeaderWrapper>\n\n      <div className=\"container mx-auto mt-6 space-y-6\">\n        <Tabs value={activeTab} onValueChange={setActiveTab} className=\"w-full\">\n          <TabsList className=\"grid w-full grid-cols-5\">\n            <TabsTrigger value=\"overview\">Overview</TabsTrigger>\n            <TabsTrigger value=\"categories\">Categories</TabsTrigger>\n            <TabsTrigger value=\"brands\">Brands</TabsTrigger>\n            <TabsTrigger value=\"materials\">Materials</TabsTrigger>\n            <TabsTrigger value=\"colors\">Colors</TabsTrigger>\n          </TabsList>\n\n          {/* Overview Tab */}\n          <TabsContent value=\"overview\" className=\"space-y-6\">\n            {/* Overall Stats */}\n            <div className=\"grid gap-4 md:grid-cols-2 lg:grid-cols-4\">\n              {overallStats.map((stat, index) => (\n                <Card key={index}>\n                  <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n                    <CardTitle className=\"text-sm font-medium text-gray-600\">\n                      {stat.label}\n                    </CardTitle>\n                    <stat.icon className=\"h-4 w-4 text-blue-600\" />\n                  </CardHeader>\n                  <CardContent>\n                    <div className=\"text-2xl font-bold text-gray-900\">\n                      {stat.value}\n                    </div>\n                    <div className=\"mt-1 flex items-center text-xs text-gray-500\">\n                      {stat.trend === \"up\" && (\n                        <TrendingUp className=\"mr-1 h-3 w-3 text-green-500\" />\n                      )}\n                      <span>{stat.change}</span>\n                      {stat.trend === \"up\" && (\n                        <span className=\"ml-1\">this month</span>\n                      )}\n                    </div>\n                  </CardContent>\n                </Card>\n              ))}\n            </div>\n\n            {/* Catalog Relationships */}\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"flex items-center gap-2\">\n                  <Layers className=\"h-5 w-5 text-blue-600\" />\n                  Catalog Relationships\n                </CardTitle>\n                <p className=\"text-sm text-gray-600\">\n                  See how categories, brands, and materials work together in\n                  your catalog\n                </p>\n              </CardHeader>\n              <CardContent>\n                <div className=\"space-y-4\">\n                  {catalogRelationships.map((item, index) => (\n                    <div\n                      key={index}\n                      className=\"rounded-lg border bg-gray-50 p-4\"\n                    >\n                      <div className=\"mb-3 flex items-center justify-between\">\n                        <div className=\"flex items-center gap-2\">\n                          <LayoutGrid className=\"h-5 w-5 text-blue-600\" />\n                          <span className=\"font-semibold text-gray-900\">\n                            {item.category}\n                          </span>\n                          <Badge variant=\"secondary\">\n                            {item.products} products\n                          </Badge>\n                        </div>\n                      </div>\n\n                      <div className=\"grid gap-4 md:grid-cols-2\">\n                        <div>\n                          <div className=\"mb-2 flex items-center gap-2\">\n                            <Tag className=\"h-4 w-4 text-green-600\" />\n                            <span className=\"text-sm font-medium text-gray-700\">\n                              Brands\n                            </span>\n                          </div>\n                          <div className=\"flex flex-wrap gap-1\">\n                            {item.brands.map((brand, idx) => (\n                              <Badge\n                                key={idx}\n                                variant=\"outline\"\n                                className=\"text-xs\"\n                              >\n                                {brand}\n                              </Badge>\n                            ))}\n                          </div>\n                        </div>\n\n                        <div>\n                          <div className=\"mb-2 flex items-center gap-2\">\n                            <Hammer className=\"h-4 w-4 text-purple-600\" />\n                            <span className=\"text-sm font-medium text-gray-700\">\n                              Materials\n                            </span>\n                          </div>\n                          <div className=\"flex flex-wrap gap-1\">\n                            {item.materials.map((material, idx) => (\n                              <Badge\n                                key={idx}\n                                variant=\"outline\"\n                                className=\"text-xs\"\n                              >\n                                {material}\n                              </Badge>\n                            ))}\n                          </div>\n                        </div>\n                      </div>\n\n                      <div className=\"mt-4\">\n                        <div className=\"mb-2 flex items-center gap-2\">\n                          <Palette className=\"h-4 w-4 text-pink-600\" />\n                          <span className=\"text-sm font-medium text-gray-700\">\n                            Colors\n                          </span>\n                        </div>\n                        <div className=\"flex flex-wrap gap-1\">\n                          {item.colors.map((color, idx) => (\n                            <Badge\n                              key={idx}\n                              variant=\"outline\"\n                              className=\"text-xs\"\n                            >\n                              {color}\n                            </Badge>\n                          ))}\n                        </div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </CardContent>\n            </Card>\n\n            {/* Quick Actions Grid */}\n            <div className=\"grid gap-4 md:grid-cols-4\">\n              <Card\n                className=\"cursor-pointer transition-shadow hover:shadow-md\"\n                onClick={() => setActiveTab(\"categories\")}\n              >\n                <CardContent className=\"p-6 text-center\">\n                  <LayoutGrid className=\"mx-auto mb-3 h-8 w-8 text-blue-600\" />\n                  <h3 className=\"mb-2 font-semibold text-gray-900\">\n                    Manage Categories\n                  </h3>\n                  <p className=\"mb-3 text-sm text-gray-600\">\n                    Organize your product catalog\n                  </p>\n                  <div className=\"flex items-center justify-center text-sm text-blue-600\">\n                    <span>Go to Categories</span>\n                    <ArrowRight className=\"ml-1 h-4 w-4\" />\n                  </div>\n                </CardContent>\n              </Card>\n\n              <Card\n                className=\"cursor-pointer transition-shadow hover:shadow-md\"\n                onClick={() => setActiveTab(\"brands\")}\n              >\n                <CardContent className=\"p-6 text-center\">\n                  <Tag className=\"mx-auto mb-3 h-8 w-8 text-green-600\" />\n                  <h3 className=\"mb-2 font-semibold text-gray-900\">\n                    Manage Brands\n                  </h3>\n                  <p className=\"mb-3 text-sm text-gray-600\">\n                    Add and organize product brands\n                  </p>\n                  <div className=\"flex items-center justify-center text-sm text-green-600\">\n                    <span>Go to Brands</span>\n                    <ArrowRight className=\"ml-1 h-4 w-4\" />\n                  </div>\n                </CardContent>\n              </Card>\n\n              <Card\n                className=\"cursor-pointer transition-shadow hover:shadow-md\"\n                onClick={() => setActiveTab(\"materials\")}\n              >\n                <CardContent className=\"p-6 text-center\">\n                  <Hammer className=\"mx-auto mb-3 h-8 w-8 text-purple-600\" />\n                  <h3 className=\"mb-2 font-semibold text-gray-900\">\n                    Manage Materials\n                  </h3>\n                  <p className=\"mb-3 text-sm text-gray-600\">\n                    Define product materials\n                  </p>\n                  <div className=\"flex items-center justify-center text-sm text-purple-600\">\n                    <span>Go to Materials</span>\n                    <ArrowRight className=\"ml-1 h-4 w-4\" />\n                  </div>\n                </CardContent>\n              </Card>\n\n              <Card\n                className=\"cursor-pointer transition-shadow hover:shadow-md\"\n                onClick={() => setActiveTab(\"colors\")}\n              >\n                <CardContent className=\"p-6 text-center\">\n                  <Palette className=\"mx-auto mb-3 h-8 w-8 text-pink-600\" />\n                  <h3 className=\"mb-2 font-semibold text-gray-900\">\n                    Manage Colors\n                  </h3>\n                  <p className=\"mb-3 text-sm text-gray-600\">\n                    Define product color options\n                  </p>\n                  <div className=\"flex items-center justify-center text-sm text-pink-600\">\n                    <span>Go to Colors</span>\n                    <ArrowRight className=\"ml-1 h-4 w-4\" />\n                  </div>\n                </CardContent>\n              </Card>\n            </div>\n          </TabsContent>\n\n          {/* Categories Tab */}\n          <TabsContent value=\"categories\" className=\"space-y-6\">\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"flex items-center gap-2\">\n                  <LayoutGrid className=\"h-5 w-5 text-blue-600\" />\n                  Category Management\n                </CardTitle>\n                <p className=\"text-sm text-gray-600\">\n                  Organize your products with categories and subcategories for\n                  better navigation\n                </p>\n              </CardHeader>\n              <CardContent>\n                <CategoryManagerEnhanced />\n              </CardContent>\n            </Card>\n          </TabsContent>\n\n          {/* Brands Tab */}\n          <TabsContent value=\"brands\" className=\"space-y-6\">\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"flex items-center gap-2\">\n                  <Tag className=\"h-5 w-5 text-green-600\" />\n                  Brand Management\n                </CardTitle>\n                <p className=\"text-sm text-gray-600\">\n                  Create, organize, and manage product brands for your catalog\n                </p>\n              </CardHeader>\n              <CardContent>\n                <BrandManagerEnhanced />\n              </CardContent>\n            </Card>\n          </TabsContent>\n\n          {/* Materials Tab */}\n          <TabsContent value=\"materials\" className=\"space-y-6\">\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"flex items-center gap-2\">\n                  <Hammer className=\"h-5 w-5 text-purple-600\" />\n                  Material Management\n                </CardTitle>\n                <p className=\"text-sm text-gray-600\">\n                  Define and manage materials used in your products for better\n                  specifications\n                </p>\n              </CardHeader>\n              <CardContent>\n                <MaterialManagerEnhanced />\n              </CardContent>\n            </Card>\n          </TabsContent>\n\n          {/* Colors Tab */}\n          <TabsContent value=\"colors\" className=\"space-y-6\">\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"flex items-center gap-2\">\n                  <Palette className=\"h-5 w-5 text-pink-600\" />\n                  Color Management\n                </CardTitle>\n                <p className=\"text-sm text-gray-600\">\n                  Create and manage color options for your products to enhance\n                  customer choice and visual appeal\n                </p>\n              </CardHeader>\n              <CardContent>\n                <ColorManagerEnhanced />\n              </CardContent>\n            </Card>\n          </TabsContent>\n        </Tabs>\n      </div>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiBA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AA/BA;;;;;;;;;;;;;;AAiCe,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,eAAe,CAAA,GAAA,iQAAA,CAAA,kBAAe,AAAD;IAEnC,kDAAkD;IAClD,CAAA,GAAA,oUAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,MAAM,aAAa,GAAG,CAAC;QAC7B,IACE,OACA;YAAC;YAAY;YAAc;YAAU;YAAa;SAAS,CAAC,QAAQ,CAAC,MACrE;YACA,aAAa;QACf;IACF,GAAG;QAAC;KAAa;IAEjB,yCAAyC;IACzC,MAAM,eAAe;QACnB;YACE,OAAO;YACP,OAAO;YACP,QAAQ;YACR,OAAO;YACP,MAAM,sSAAA,CAAA,aAAU;QAClB;QACA;YACE,OAAO;YACP,OAAO;YACP,QAAQ;YACR,OAAO;YACP,MAAM,oRAAA,CAAA,MAAG;QACX;QACA;YACE,OAAO;YACP,OAAO;YACP,QAAQ;YACR,OAAO;YACP,MAAM,0RAAA,CAAA,SAAM;QACd;QACA;YACE,OAAO;YACP,OAAO;YACP,QAAQ;YACR,OAAO;YACP,MAAM,4RAAA,CAAA,UAAO;QACf;KACD;IAED,MAAM,eAAe;QACnB;YAAE,OAAO;YAAe,MAAM,0RAAA,CAAA,SAAM;YAAE,QAAQ,IAAM,QAAQ,GAAG,CAAC;QAAU;QAC1E;YACE,OAAO;YACP,MAAM,8RAAA,CAAA,WAAQ;YACd,QAAQ,IAAM,QAAQ,GAAG,CAAC;QAC5B;QACA;YACE,OAAO;YACP,MAAM,sSAAA,CAAA,YAAS;YACf,QAAQ,IAAM,QAAQ,GAAG,CAAC;QAC5B;KACD;IAED,6CAA6C;IAC7C,MAAM,uBAAuB;QAC3B;YACE,UAAU;YACV,QAAQ;gBAAC;gBAAS;gBAAW;aAAO;YACpC,WAAW;gBAAC;gBAAY;gBAAS;aAAU;YAC3C,QAAQ;gBAAC;gBAAS;gBAAS;aAAS;YACpC,UAAU;QACZ;QACA;YACE,UAAU;YACV,QAAQ;gBAAC;gBAAQ;gBAAU;aAAM;YACjC,WAAW;gBAAC;gBAAU;gBAAa;aAAO;YAC1C,QAAQ;gBAAC;gBAAO;gBAAQ;aAAQ;YAChC,UAAU;QACZ;QACA;YACE,UAAU;YACV,QAAQ;gBAAC;gBAAQ;gBAAc;aAAU;YACzC,WAAW;gBAAC;gBAAQ;gBAAS;aAAS;YACtC,QAAQ;gBAAC;gBAAS;gBAAS;aAAO;YAClC,UAAU;QACZ;KACD;IAED,qBACE;;0BACE,6WAAC,0IAAA,CAAA,oBAAiB;gBAChB,OAAM;gBACN,aAAY;0BAEZ,cAAA,6WAAC;oBAAI,WAAU;;wBACZ,aAAa,GAAG,CAAC,CAAC,QAAQ,sBACzB,6WAAC,2HAAA,CAAA,SAAM;gCAEL,SAAQ;gCACR,MAAK;gCACL,SAAS,OAAO,MAAM;gCACtB,WAAU;;kDAEV,6WAAC,OAAO,IAAI;wCAAC,WAAU;;;;;;oCACtB,OAAO,KAAK;;+BAPR;;;;;sCAUT,6WAAC,2HAAA,CAAA,SAAM;4BAAC,MAAK;;8CACX,6WAAC,sRAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;0BAMvC,6WAAC;gBAAI,WAAU;0BACb,cAAA,6WAAC,yHAAA,CAAA,OAAI;oBAAC,OAAO;oBAAW,eAAe;oBAAc,WAAU;;sCAC7D,6WAAC,yHAAA,CAAA,WAAQ;4BAAC,WAAU;;8CAClB,6WAAC,yHAAA,CAAA,cAAW;oCAAC,OAAM;8CAAW;;;;;;8CAC9B,6WAAC,yHAAA,CAAA,cAAW;oCAAC,OAAM;8CAAa;;;;;;8CAChC,6WAAC,yHAAA,CAAA,cAAW;oCAAC,OAAM;8CAAS;;;;;;8CAC5B,6WAAC,yHAAA,CAAA,cAAW;oCAAC,OAAM;8CAAY;;;;;;8CAC/B,6WAAC,yHAAA,CAAA,cAAW;oCAAC,OAAM;8CAAS;;;;;;;;;;;;sCAI9B,6WAAC,yHAAA,CAAA,cAAW;4BAAC,OAAM;4BAAW,WAAU;;8CAEtC,6WAAC;oCAAI,WAAU;8CACZ,aAAa,GAAG,CAAC,CAAC,MAAM,sBACvB,6WAAC,yHAAA,CAAA,OAAI;;8DACH,6WAAC,yHAAA,CAAA,aAAU;oDAAC,WAAU;;sEACpB,6WAAC,yHAAA,CAAA,YAAS;4DAAC,WAAU;sEAClB,KAAK,KAAK;;;;;;sEAEb,6WAAC,KAAK,IAAI;4DAAC,WAAU;;;;;;;;;;;;8DAEvB,6WAAC,yHAAA,CAAA,cAAW;;sEACV,6WAAC;4DAAI,WAAU;sEACZ,KAAK,KAAK;;;;;;sEAEb,6WAAC;4DAAI,WAAU;;gEACZ,KAAK,KAAK,KAAK,sBACd,6WAAC,sSAAA,CAAA,aAAU;oEAAC,WAAU;;;;;;8EAExB,6WAAC;8EAAM,KAAK,MAAM;;;;;;gEACjB,KAAK,KAAK,KAAK,sBACd,6WAAC;oEAAK,WAAU;8EAAO;;;;;;;;;;;;;;;;;;;2CAjBpB;;;;;;;;;;8CA0Bf,6WAAC,yHAAA,CAAA,OAAI;;sDACH,6WAAC,yHAAA,CAAA,aAAU;;8DACT,6WAAC,yHAAA,CAAA,YAAS;oDAAC,WAAU;;sEACnB,6WAAC,0RAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;wDAA0B;;;;;;;8DAG9C,6WAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAKvC,6WAAC,yHAAA,CAAA,cAAW;sDACV,cAAA,6WAAC;gDAAI,WAAU;0DACZ,qBAAqB,GAAG,CAAC,CAAC,MAAM,sBAC/B,6WAAC;wDAEC,WAAU;;0EAEV,6WAAC;gEAAI,WAAU;0EACb,cAAA,6WAAC;oEAAI,WAAU;;sFACb,6WAAC,sSAAA,CAAA,aAAU;4EAAC,WAAU;;;;;;sFACtB,6WAAC;4EAAK,WAAU;sFACb,KAAK,QAAQ;;;;;;sFAEhB,6WAAC,0HAAA,CAAA,QAAK;4EAAC,SAAQ;;gFACZ,KAAK,QAAQ;gFAAC;;;;;;;;;;;;;;;;;;0EAKrB,6WAAC;gEAAI,WAAU;;kFACb,6WAAC;;0FACC,6WAAC;gFAAI,WAAU;;kGACb,6WAAC,oRAAA,CAAA,MAAG;wFAAC,WAAU;;;;;;kGACf,6WAAC;wFAAK,WAAU;kGAAoC;;;;;;;;;;;;0FAItD,6WAAC;gFAAI,WAAU;0FACZ,KAAK,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,oBACvB,6WAAC,0HAAA,CAAA,QAAK;wFAEJ,SAAQ;wFACR,WAAU;kGAET;uFAJI;;;;;;;;;;;;;;;;kFAUb,6WAAC;;0FACC,6WAAC;gFAAI,WAAU;;kGACb,6WAAC,0RAAA,CAAA,SAAM;wFAAC,WAAU;;;;;;kGAClB,6WAAC;wFAAK,WAAU;kGAAoC;;;;;;;;;;;;0FAItD,6WAAC;gFAAI,WAAU;0FACZ,KAAK,SAAS,CAAC,GAAG,CAAC,CAAC,UAAU,oBAC7B,6WAAC,0HAAA,CAAA,QAAK;wFAEJ,SAAQ;wFACR,WAAU;kGAET;uFAJI;;;;;;;;;;;;;;;;;;;;;;0EAWf,6WAAC;gEAAI,WAAU;;kFACb,6WAAC;wEAAI,WAAU;;0FACb,6WAAC,4RAAA,CAAA,UAAO;gFAAC,WAAU;;;;;;0FACnB,6WAAC;gFAAK,WAAU;0FAAoC;;;;;;;;;;;;kFAItD,6WAAC;wEAAI,WAAU;kFACZ,KAAK,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,oBACvB,6WAAC,0HAAA,CAAA,QAAK;gFAEJ,SAAQ;gFACR,WAAU;0FAET;+EAJI;;;;;;;;;;;;;;;;;uDAnER;;;;;;;;;;;;;;;;;;;;;8CAmFf,6WAAC;oCAAI,WAAU;;sDACb,6WAAC,yHAAA,CAAA,OAAI;4CACH,WAAU;4CACV,SAAS,IAAM,aAAa;sDAE5B,cAAA,6WAAC,yHAAA,CAAA,cAAW;gDAAC,WAAU;;kEACrB,6WAAC,sSAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;kEACtB,6WAAC;wDAAG,WAAU;kEAAmC;;;;;;kEAGjD,6WAAC;wDAAE,WAAU;kEAA6B;;;;;;kEAG1C,6WAAC;wDAAI,WAAU;;0EACb,6WAAC;0EAAK;;;;;;0EACN,6WAAC,sSAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;sDAK5B,6WAAC,yHAAA,CAAA,OAAI;4CACH,WAAU;4CACV,SAAS,IAAM,aAAa;sDAE5B,cAAA,6WAAC,yHAAA,CAAA,cAAW;gDAAC,WAAU;;kEACrB,6WAAC,oRAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;kEACf,6WAAC;wDAAG,WAAU;kEAAmC;;;;;;kEAGjD,6WAAC;wDAAE,WAAU;kEAA6B;;;;;;kEAG1C,6WAAC;wDAAI,WAAU;;0EACb,6WAAC;0EAAK;;;;;;0EACN,6WAAC,sSAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;sDAK5B,6WAAC,yHAAA,CAAA,OAAI;4CACH,WAAU;4CACV,SAAS,IAAM,aAAa;sDAE5B,cAAA,6WAAC,yHAAA,CAAA,cAAW;gDAAC,WAAU;;kEACrB,6WAAC,0RAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,6WAAC;wDAAG,WAAU;kEAAmC;;;;;;kEAGjD,6WAAC;wDAAE,WAAU;kEAA6B;;;;;;kEAG1C,6WAAC;wDAAI,WAAU;;0EACb,6WAAC;0EAAK;;;;;;0EACN,6WAAC,sSAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;sDAK5B,6WAAC,yHAAA,CAAA,OAAI;4CACH,WAAU;4CACV,SAAS,IAAM,aAAa;sDAE5B,cAAA,6WAAC,yHAAA,CAAA,cAAW;gDAAC,WAAU;;kEACrB,6WAAC,4RAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;kEACnB,6WAAC;wDAAG,WAAU;kEAAmC;;;;;;kEAGjD,6WAAC;wDAAE,WAAU;kEAA6B;;;;;;kEAG1C,6WAAC;wDAAI,WAAU;;0EACb,6WAAC;0EAAK;;;;;;0EACN,6WAAC,sSAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQhC,6WAAC,yHAAA,CAAA,cAAW;4BAAC,OAAM;4BAAa,WAAU;sCACxC,cAAA,6WAAC,yHAAA,CAAA,OAAI;;kDACH,6WAAC,yHAAA,CAAA,aAAU;;0DACT,6WAAC,yHAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,6WAAC,sSAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;oDAA0B;;;;;;;0DAGlD,6WAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAKvC,6WAAC,yHAAA,CAAA,cAAW;kDACV,cAAA,6WAAC,6JAAA,CAAA,0BAAuB;;;;;;;;;;;;;;;;;;;;;sCAM9B,6WAAC,yHAAA,CAAA,cAAW;4BAAC,OAAM;4BAAS,WAAU;sCACpC,cAAA,6WAAC,yHAAA,CAAA,OAAI;;kDACH,6WAAC,yHAAA,CAAA,aAAU;;0DACT,6WAAC,yHAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,6WAAC,oRAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;oDAA2B;;;;;;;0DAG5C,6WAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAIvC,6WAAC,yHAAA,CAAA,cAAW;kDACV,cAAA,6WAAC,0JAAA,CAAA,uBAAoB;;;;;;;;;;;;;;;;;;;;;sCAM3B,6WAAC,yHAAA,CAAA,cAAW;4BAAC,OAAM;4BAAY,WAAU;sCACvC,cAAA,6WAAC,yHAAA,CAAA,OAAI;;kDACH,6WAAC,yHAAA,CAAA,aAAU;;0DACT,6WAAC,yHAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,6WAAC,0RAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAA4B;;;;;;;0DAGhD,6WAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAKvC,6WAAC,yHAAA,CAAA,cAAW;kDACV,cAAA,6WAAC,6JAAA,CAAA,0BAAuB;;;;;;;;;;;;;;;;;;;;;sCAM9B,6WAAC,yHAAA,CAAA,cAAW;4BAAC,OAAM;4BAAS,WAAU;sCACpC,cAAA,6WAAC,yHAAA,CAAA,OAAI;;kDACH,6WAAC,yHAAA,CAAA,aAAU;;0DACT,6WAAC,yHAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,6WAAC,4RAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;oDAA0B;;;;;;;0DAG/C,6WAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAKvC,6WAAC,yHAAA,CAAA,cAAW;kDACV,cAAA,6WAAC,0JAAA,CAAA,uBAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrC", "debugId": null}}]}