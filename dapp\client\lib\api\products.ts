/**
 * API functions for product operations
 * Handles all HTTP requests to the backend product endpoints
 */

// Types for API responses
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  meta?: {
    total: number;
    page: number;
    limit: number;
    pages: number;
  };
}

export interface ProductFilters {
  search?: string;
  category?: string;
  brand?: string;
  minPrice?: number;
  maxPrice?: number;
  condition?: string;
  inStock?: boolean;
  tags?: string[];
  status?: string;
  sortBy?: "price" | "createdAt" | "name";
  sortOrder?: "asc" | "desc";
  page?: number;
  limit?: number;
}

// Get API base URL from environment or default to localhost
const getApiBaseUrl = (): string => {
  if (typeof window !== "undefined") {
    // Client-side: use current origin or environment variable
    return (
      process.env.NEXT_PUBLIC_API_URL ||
      `${window.location.protocol}//${window.location.hostname}:3001`
    );
  }
  // Server-side: use environment variable or default
  return process.env.NEXT_PUBLIC_API_URL || "http://localhost:3001";
};

const API_BASE_URL = getApiBaseUrl();

/**
 * Generic fetch wrapper with error handling
 */
async function apiRequest<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<ApiResponse<T>> {
  const url = `${API_BASE_URL}/api${endpoint}`;

  const defaultOptions: RequestInit = {
    headers: {
      "Content-Type": "application/json",
      ...options.headers,
    },
    ...options,
  };

  console.log(`🌐 Making API request to: ${url}`);
  console.log(`🌐 Request options:`, defaultOptions);

  try {
    const response = await fetch(url, defaultOptions);

    console.log(`🌐 Response status: ${response.status}`);
    console.log(`🌐 Response ok: ${response.ok}`);

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`🌐 Response error text:`, errorText);
      throw new Error(
        `HTTP error! status: ${response.status}, message: ${errorText}`
      );
    }

    const data = await response.json();
    console.log(`🌐 Response data:`, data);
    return data;
  } catch (error) {
    console.error(`🌐 API request failed for ${endpoint}:`, error);
    throw error;
  }
}

/**
 * Get all products with optional filtering
 */
export async function getProducts(
  filters: ProductFilters = {}
): Promise<ApiResponse<any[]>> {
  const searchParams = new URLSearchParams();

  // Add filters to search params
  Object.entries(filters).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== "") {
      if (Array.isArray(value)) {
        value.forEach((item) => searchParams.append(key, item.toString()));
      } else {
        searchParams.append(key, value.toString());
      }
    }
  });

  const queryString = searchParams.toString();
  const endpoint = queryString ? `/products?${queryString}` : "/products";

  return apiRequest<any[]>(endpoint);
}

/**
 * Get a single product by ID
 */
export async function getProductById(id: string): Promise<ApiResponse<any>> {
  return apiRequest<any>(`/products/${id}`);
}

/**
 * Get a product for editing (with safe field list)
 */
export async function getProductForEdit(id: string): Promise<ApiResponse<any>> {
  return apiRequest<any>(`/products/${id}/edit`);
}

/**
 * Create a new product
 */
export async function createProduct(
  productData: any
): Promise<ApiResponse<any>> {
  console.log("🌐 API createProduct called with data:", productData);
  console.log("🌐 API Base URL:", API_BASE_URL);

  try {
    const result = await apiRequest<any>("/products", {
      method: "POST",
      body: JSON.stringify(productData),
    });
    console.log("🌐 API createProduct response:", result);
    return result;
  } catch (error) {
    console.error("🌐 API createProduct error:", error);
    throw error;
  }
}

/**
 * Update an existing product
 */
export async function updateProduct(
  id: string,
  productData: any
): Promise<ApiResponse<any>> {
  return apiRequest<any>(`/products/${id}`, {
    method: "PUT",
    body: JSON.stringify(productData),
  });
}

/**
 * Update product with safe fields (PATCH)
 */
export async function updateProductSafe(
  id: string,
  productData: any
): Promise<ApiResponse<any>> {
  return apiRequest<any>(`/products/${id}/edit`, {
    method: "PATCH",
    body: JSON.stringify(productData),
  });
}

/**
 * Delete a product
 */
export async function deleteProduct(id: string): Promise<ApiResponse<boolean>> {
  return apiRequest<boolean>(`/products/${id}`, {
    method: "DELETE",
  });
}

/**
 * Health check for API connection
 */
export async function checkApiHealth(): Promise<boolean> {
  try {
    const response = await fetch(`${API_BASE_URL}/api`);
    return response.ok;
  } catch (error) {
    console.error("API health check failed:", error);
    return false;
  }
}
