import {
  AlertCircle,
  Calendar,
  CheckCircle,
  Clock,
  CreditCard,
  FileText,
  Mail,
  Package,
  Phone,
  RotateCcw,
  Shield,
  Truck,
} from "lucide-react";
import {
  FieldErrors,
  UseFormRegister,
  UseFormSetValue,
  UseFormWatch,
} from "react-hook-form";

import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import { ProductFormData } from "@/schemas/productSchema";

import { FormField } from "../../../common/FormField";
import { CollapsibleSection } from "../../../ui/collapsible-section";

type WarrantySectionProps = {
  register: UseFormRegister<ProductFormData>;
  errors: FieldErrors<ProductFormData>;
  setValue: UseFormSetValue<ProductFormData>;
  watch: UseFormWatch<ProductFormData>;
};

const warrantyPeriods = [
  { value: "30-days", label: "30 Days" },
  { value: "90-days", label: "90 Days" },
  { value: "6-months", label: "6 Months" },
  { value: "1-year", label: "1 Year" },
  { value: "2-years", label: "2 Years" },
  { value: "3-years", label: "3 Years" },
  { value: "5-years", label: "5 Years" },
  { value: "lifetime", label: "Lifetime" },
  { value: "no-warranty", label: "No Warranty" },
];

const returnPeriods = [
  { value: "7-days", label: "7 Days" },
  { value: "14-days", label: "14 Days" },
  { value: "30-days", label: "30 Days" },
  { value: "60-days", label: "60 Days" },
  { value: "90-days", label: "90 Days" },
  { value: "no-returns", label: "No Returns" },
];

export const WarrantySection: React.FC<WarrantySectionProps> = ({
  register,
  errors,
  setValue,
  watch,
}) => {
  const warrantyPeriod = watch("warrantyPeriod");
  const returnPeriod = watch("returnPeriod");
  const hasWarranty = warrantyPeriod && warrantyPeriod !== "no-warranty";
  const allowsReturns = returnPeriod && returnPeriod !== "no-returns";

  return (
    <div className="space-y-8">
      {/* Section Header */}
      <div className="flex items-center gap-3 border-b border-gray-200 pb-4">
        <div className="rounded-full bg-blue-100 p-2">
          <Shield className="h-5 w-5 text-blue-600" />
        </div>
        <div>
          <h2 className="text-2xl font-bold text-gray-900">
            Warranty & Returns
          </h2>
          <p className="text-gray-600">
            Configure warranty coverage and return policies
          </p>
        </div>
      </div>

      {/* Warranty Information Card */}
      <Card className="border-l-4 border-l-blue-500">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Shield className="h-5 w-5 text-blue-600" />
            Warranty Coverage
            <Badge variant="secondary" className="text-xs">
              Optional
            </Badge>
          </CardTitle>
          <p className="text-sm text-gray-600">
            Define warranty terms and coverage for this product
          </p>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Warranty Period */}
            <FormField
              id="warrantyPeriod"
              label="Warranty Period"
              optional={true}
            >
              <Select
                onValueChange={(value) => setValue("warrantyPeriod", value)}
                value={warrantyPeriod}
              >
                <SelectTrigger className="border-2 focus:border-blue-500">
                  <SelectValue placeholder="Select warranty period..." />
                </SelectTrigger>
                <SelectContent>
                  {warrantyPeriods.map((period) => (
                    <SelectItem key={period.value} value={period.value}>
                      <div className="flex items-center gap-2">
                        {period.value === "no-warranty" ? (
                          <AlertCircle className="h-4 w-4 text-red-500" />
                        ) : (
                          <Shield className="h-4 w-4 text-blue-500" />
                        )}
                        <span className="font-medium">{period.label}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </FormField>

            {/* Warranty Terms */}
            {hasWarranty && (
              <FormField
                id="warrantyTerms"
                label="Warranty Terms & Conditions"
                optional={true}
              >
                <Textarea
                  id="warrantyTerms"
                  {...register("warrantyTerms")}
                  placeholder="Describe what is covered under warranty, exclusions, and claim process..."
                  rows={4}
                  className="resize-none border-2 focus:border-blue-500"
                />
              </FormField>
            )}

            {/* Warranty Status Display */}
            {warrantyPeriod && (
              <div
                className={`rounded-lg border p-3 ${
                  hasWarranty
                    ? "border-blue-200 bg-blue-50"
                    : "border-gray-200 bg-gray-50"
                }`}
              >
                <div className="flex items-center gap-2">
                  {hasWarranty ? (
                    <CheckCircle className="h-4 w-4 text-blue-600" />
                  ) : (
                    <AlertCircle className="h-4 w-4 text-gray-600" />
                  )}
                  <span
                    className={`font-medium ${
                      hasWarranty ? "text-blue-900" : "text-gray-900"
                    }`}
                  >
                    {hasWarranty
                      ? `Warranty Coverage: ${warrantyPeriods.find((p) => p.value === warrantyPeriod)?.label}`
                      : "No Warranty Coverage"}
                  </span>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Return Policy Card */}
      <Card className="border-l-4 border-l-green-500">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <RotateCcw className="h-5 w-5 text-green-600" />
            Return Policy
            <Badge variant="secondary" className="text-xs">
              Optional
            </Badge>
          </CardTitle>
          <p className="text-sm text-gray-600">
            Set return window and conditions for customer returns
          </p>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Return Period */}
            <FormField id="returnPeriod" label="Return Period" optional={true}>
              <Select
                onValueChange={(value) => setValue("returnPeriod", value)}
                value={returnPeriod}
              >
                <SelectTrigger className="border-2 focus:border-green-500">
                  <SelectValue placeholder="Select return period..." />
                </SelectTrigger>
                <SelectContent>
                  {returnPeriods.map((period) => (
                    <SelectItem key={period.value} value={period.value}>
                      <div className="flex items-center gap-2">
                        {period.value === "no-returns" ? (
                          <AlertCircle className="h-4 w-4 text-red-500" />
                        ) : (
                          <RotateCcw className="h-4 w-4 text-green-500" />
                        )}
                        <span className="font-medium">{period.label}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </FormField>

            {/* Return Policy Details */}
            {allowsReturns && (
              <FormField
                id="returnPolicy"
                label="Return Policy Details"
                optional={true}
              >
                <Textarea
                  id="returnPolicy"
                  {...register("returnPolicy")}
                  placeholder="Describe return conditions, who pays shipping, refund process, item condition requirements..."
                  rows={4}
                  className="resize-none border-2 focus:border-green-500"
                />
              </FormField>
            )}

            {/* Return Status Display */}
            {returnPeriod && (
              <div
                className={`rounded-lg border p-3 ${
                  allowsReturns
                    ? "border-green-200 bg-green-50"
                    : "border-gray-200 bg-gray-50"
                }`}
              >
                <div className="flex items-center gap-2">
                  {allowsReturns ? (
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  ) : (
                    <AlertCircle className="h-4 w-4 text-gray-600" />
                  )}
                  <span
                    className={`font-medium ${
                      allowsReturns ? "text-green-900" : "text-gray-900"
                    }`}
                  >
                    {allowsReturns
                      ? `Returns Accepted: ${returnPeriods.find((p) => p.value === returnPeriod)?.label} window`
                      : "No Returns Accepted"}
                  </span>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Customer Support - Collapsible */}
      <CollapsibleSection
        title="Customer Support"
        description="Provide support contact information for warranty and returns"
        icon={<Phone className="h-5 w-5 text-purple-600" />}
        borderColor="border-l-purple-500"
        defaultOpen={false}
        isOptional={true}
      >
        <div className="space-y-4">
          {/* Support Email */}
          <FormField id="supportEmail" label="Support Email" optional={true}>
            <Input
              id="supportEmail"
              type="email"
              {...register("supportEmail")}
              placeholder="<EMAIL>"
              className="border-2 focus:border-purple-500"
            />
          </FormField>

          {/* Support Phone */}
          <FormField id="supportPhone" label="Support Phone" optional={true}>
            <Input
              id="supportPhone"
              {...register("supportPhone")}
              placeholder="+****************"
              className="border-2 focus:border-purple-500"
            />
          </FormField>

          {/* Support Hours */}
          <FormField id="supportHours" label="Support Hours" optional={true}>
            <Input
              id="supportHours"
              {...register("supportHours")}
              placeholder="Monday-Friday 9AM-5PM EST"
              className="border-2 focus:border-purple-500"
            />
          </FormField>
        </div>
      </CollapsibleSection>

      {/* Policy Summary Card */}
      <Card className="border-blue-200 bg-gradient-to-r from-blue-50 to-green-50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-blue-800">
            <FileText className="h-5 w-5" />
            Policy Summary
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2">
            <div className="flex items-center gap-3">
              <div className="rounded-full bg-blue-100 p-2">
                <Shield className="h-4 w-4 text-blue-600" />
              </div>
              <div>
                <h4 className="font-medium text-blue-900">Warranty</h4>
                <p className="text-sm text-blue-700">
                  {hasWarranty
                    ? `${warrantyPeriods.find((p) => p.value === warrantyPeriod)?.label} coverage`
                    : "No warranty"}
                </p>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <div className="rounded-full bg-green-100 p-2">
                <RotateCcw className="h-4 w-4 text-green-600" />
              </div>
              <div>
                <h4 className="font-medium text-green-900">Returns</h4>
                <p className="text-sm text-green-700">
                  {allowsReturns
                    ? `${returnPeriods.find((p) => p.value === returnPeriod)?.label} return window`
                    : "No returns"}
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
