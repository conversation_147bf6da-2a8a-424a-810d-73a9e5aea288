import { Request, Response } from "express";
import { StatusCodes } from "http-status-codes";
import { CategoryService } from "../../services/category.service";
import { CreateCategoryDto, UpdateCategoryDto } from "../../types/category.types";

/**
 * Controller for handling category-related HTTP requests
 */
export class CategoryController {
  private categoryService: CategoryService;

  constructor() {
    this.categoryService = new CategoryService();
  }

  /**
   * Create a new category
   * POST /api/categories
   */
  createCategory = async (req: Request, res: Response): Promise<void> => {
    try {
      const categoryData: CreateCategoryDto = req.body;
      const category = await this.categoryService.createCategory(categoryData);
      
      res.status(StatusCodes.CREATED).json({
        success: true,
        data: category,
        message: "Category created successfully",
      });
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to create category";

      res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: errorMessage,
      });
    }
  };

  /**
   * Get all categories with optional filtering
   * GET /api/categories
   */
  getCategories = async (req: Request, res: Response): Promise<void> => {
    try {
      const filters = {
        isActive: req.query.isActive === "true" ? true : req.query.isActive === "false" ? false : undefined,
        parentId: req.query.parentId as string,
        search: req.query.search as string,
      };

      const categories = await this.categoryService.getCategories(filters);
      
      res.status(StatusCodes.OK).json({
        success: true,
        data: categories,
        count: categories.length,
      });
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to fetch categories";

      res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: errorMessage,
      });
    }
  };

  /**
   * Get a category by ID
   * GET /api/categories/:id
   */
  getCategoryById = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const category = await this.categoryService.getCategoryById(id);

      if (!category) {
        res.status(StatusCodes.NOT_FOUND).json({
          success: false,
          message: "Category not found",
        });
        return;
      }

      res.status(StatusCodes.OK).json({
        success: true,
        data: category,
      });
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to fetch category";

      res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: errorMessage,
      });
    }
  };

  /**
   * Get a category by slug
   * GET /api/categories/slug/:slug
   */
  getCategoryBySlug = async (req: Request, res: Response): Promise<void> => {
    try {
      const { slug } = req.params;
      const category = await this.categoryService.getCategoryBySlug(slug);

      if (!category) {
        res.status(StatusCodes.NOT_FOUND).json({
          success: false,
          message: "Category not found",
        });
        return;
      }

      res.status(StatusCodes.OK).json({
        success: true,
        data: category,
      });
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to fetch category";

      res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: errorMessage,
      });
    }
  };

  /**
   * Update a category
   * PUT /api/categories/:id
   */
  updateCategory = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const updateData: UpdateCategoryDto = req.body;
      
      const category = await this.categoryService.updateCategory(id, updateData);

      if (!category) {
        res.status(StatusCodes.NOT_FOUND).json({
          success: false,
          message: "Category not found",
        });
        return;
      }

      res.status(StatusCodes.OK).json({
        success: true,
        data: category,
        message: "Category updated successfully",
      });
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to update category";

      res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: errorMessage,
      });
    }
  };

  /**
   * Delete a category
   * DELETE /api/categories/:id
   */
  deleteCategory = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const deleted = await this.categoryService.deleteCategory(id);

      if (!deleted) {
        res.status(StatusCodes.NOT_FOUND).json({
          success: false,
          message: "Category not found",
        });
        return;
      }

      res.status(StatusCodes.OK).json({
        success: true,
        message: "Category deleted successfully",
      });
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to delete category";

      res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: errorMessage,
      });
    }
  };
}
