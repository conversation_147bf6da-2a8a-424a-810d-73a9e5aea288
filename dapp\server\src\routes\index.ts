import { Express, Request, Response } from "express";
import mongoose from "mongoose";

import { categoryRoutes } from "./category.routes";
import { productRoutes } from "./product.routes";

/**
 * Register all API routes
 * @param app - Express application instance
 */
export const registerRoutes = (app: Express): void => {
  // Base API route - provides information about available endpoints
  app.get("/api", (req: Request, res: Response) => {
    res.json({
      success: true,
      message: "Welcome to the E-Commerce API",
      version: "1.0.0",
      endpoints: {
        products: {
          base: "/api/products",
          methods: {
            GET: "Get all products or a specific product by ID",
            POST: "Create a new product",
            PUT: "Update an existing product",
            DELETE: "Delete a product",
          },
        },
        categories: {
          base: "/api/categories",
          methods: {
            GET: "Get all categories or a specific category by ID/slug",
            POST: "Create a new category",
            PUT: "Update an existing category",
            DELETE: "Delete a category",
          },
        },
        // Add more endpoints as they are implemented
      },
      documentation: `${req.protocol}://${req.get("host")}/api-docs`,
    });
  });

  // API routes
  app.use("/api/products", productRoutes);
  app.use("/api/categories", categoryRoutes);

  // Add more routes as needed
  // app.use('/api/users', userRoutes);
  // app.use('/api/orders', orderRoutes);

  // Debug route will be added later
};
