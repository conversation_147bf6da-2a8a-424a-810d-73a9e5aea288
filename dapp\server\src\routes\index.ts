import { Express, Request, Response } from "express";
import mongoose from "mongoose";

import { productRoutes } from "./product.routes";

/**
 * Register all API routes
 * @param app - Express application instance
 */
export const registerRoutes = (app: Express): void => {
  // Base API route - provides information about available endpoints
  app.get("/api", (req: Request, res: Response) => {
    res.json({
      success: true,
      message: "Welcome to the E-Commerce API",
      version: "1.0.0",
      endpoints: {
        products: {
          base: "/api/products",
          methods: {
            GET: "Get all products or a specific product by ID",
            POST: "Create a new product",
            PUT: "Update an existing product",
            DELETE: "Delete a product",
          },
        },
        // Add more endpoints as they are implemented
      },
      documentation: `${req.protocol}://${req.get("host")}/api-docs`,
    });
  });

  // API routes
  app.use("/api/products", productRoutes);

  // Add more routes as needed
  // app.use('/api/users', userRoutes);
  // app.use('/api/orders', orderRoutes);

  // Debug route to check database connection and collections
  app.get("/api/debug/db", (_req: Request, res: Response) => {
    try {
      // Check if the database connection is established
      if (!mongoose.connection) {
        return res.status(500).json({
          success: false,
          message: "Database connection not established",
          connectionState: "unknown",
        });
      }

      // Return basic connection info
      return res.json({
        success: true,
        data: {
          connectionState: mongoose.connection.readyState,
          connected: mongoose.connection.readyState === 1,
          databaseName: mongoose.connection.name,
        },
      });
    } catch (error) {
      return res.status(500).json({
        success: false,
        message:
          error instanceof Error
            ? error.message
            : "Failed to get database debug info",
      });
    }
  });
};
