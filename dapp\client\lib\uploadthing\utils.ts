import { toast } from "sonner";

export const deleteImageFromServer = async (
  fileKey: string
): Promise<boolean> => {
  try {
    const response = await fetch(`/api/uploadthing/delete`, {
      method: "DELETE",
      body: JSON.stringify({ fileKey }),
      headers: {
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      throw new Error("Failed to delete image");
    }

    return true;
  } catch (error) {
    console.error("Error deleting image:", error);
    toast.error("Failed to delete image from server");

    return false;
  }
};
