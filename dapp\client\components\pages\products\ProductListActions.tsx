"use client";

import React from "react";

import { FileDown, Plus, RefreshCw, Upload } from "lucide-react";
import { useRouter } from "next/navigation";

import { Button } from "@/components/ui/button";

interface ProductListActionsProps {
  onRefresh?: () => void;
  loading?: boolean;
}

export const ProductListActions = ({
  onRefresh,
  loading = false,
}: ProductListActionsProps) => {
  const router = useRouter();

  const handleExport = () => console.log("Exporting...");
  const handleImport = () => console.log("Importing...");
  const handleRefresh = () => onRefresh?.();
  const handleCreate = () => router.push("/admin/products/add");

  return (
    <div className="flex gap-2">
      <Button
        variant="outline"
        size="sm"
        onClick={handleRefresh}
        disabled={loading}
      >
        <RefreshCw
          className={`mr-2 h-4 w-4 ${loading ? "animate-spin" : ""}`}
        />
        Refresh
      </Button>

      <Button variant="outline" size="sm" onClick={handleImport}>
        <Upload className="mr-2 h-4 w-4" />
        Import
      </Button>

      <Button variant="outline" size="sm" onClick={handleExport}>
        <FileDown className="mr-2 h-4 w-4" />
        Export
      </Button>

      <Button size="sm" onClick={handleCreate}>
        <Plus className="mr-2 h-4 w-4" />
        Add Product
      </Button>
    </div>
  );
};
