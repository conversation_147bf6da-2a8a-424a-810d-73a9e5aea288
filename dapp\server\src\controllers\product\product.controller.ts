import { Request, Response } from "express";
import { StatusCodes } from "http-status-codes";

import { ProductService } from "../../services/product.service";
import {
  ProductCondition,
  ProductFilters,
  ProductStatus,
} from "../../types/product.types";

/**
 * Controller for handling product-related HTTP requests
 */
export class ProductController {
  private productService: ProductService;

  constructor() {
    this.productService = new ProductService();
  }

  /**
   * Create a new product
   * @param req - Express request object
   * @param res - Express response object
   */
  createProduct = async (req: Request, res: Response): Promise<void> => {
    try {
      const product = await this.productService.createProduct(req.body);
      res.status(StatusCodes.CREATED).json({
        success: true,
        data: product,
      });
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to create product";

      res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: errorMessage,
      });
    }
  };

  /**
   * Get a product by ID
   * @param req - Express request object
   * @param res - Express response object
   */
  getProductById = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const product = await this.productService.getProductById(id);

      if (!product) {
        res.status(StatusCodes.NOT_FOUND).json({
          success: false,
          message: "Product not found",
        });
        return;
      }

      res.status(StatusCodes.OK).json({
        success: true,
        data: product,
      });
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to fetch product";

      res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: errorMessage,
      });
    }
  };

  /**
   * Get products with optional filtering
   * @param req - Express request object
   * @param res - Express response object
   */
  getProducts = async (req: Request, res: Response): Promise<void> => {
    try {
      // Extract filter parameters from query
      const filters: ProductFilters = {
        search: req.query.search as string,
        category: req.query.category as string,
        brand: req.query.brand as string,
        minPrice: req.query.minPrice ? Number(req.query.minPrice) : undefined,
        maxPrice: req.query.maxPrice ? Number(req.query.maxPrice) : undefined,
        condition: (req.query.condition as string)
          ? (req.query.condition as string as ProductCondition)
          : undefined,
        inStock: req.query.inStock === "true",
        tags: req.query.tags
          ? (req.query.tags as string).split(",")
          : undefined,
        status: (req.query.status as string)
          ? (req.query.status as string as ProductStatus)
          : undefined,
        sortBy: (req.query.sortBy as string)
          ? (req.query.sortBy as string as "price" | "createdAt" | "name")
          : "createdAt",
        sortOrder: (req.query.sortOrder as string)
          ? (req.query.sortOrder as string as "asc" | "desc")
          : "desc",
        page: req.query.page ? Number(req.query.page) : 1,
        limit: req.query.limit ? Number(req.query.limit) : 10,
      };

      const { products, total } =
        await this.productService.getProducts(filters);

      res.status(StatusCodes.OK).json({
        success: true,
        data: products,
        meta: {
          total,
          page: filters.page || 1,
          limit: filters.limit || 10,
          pages: Math.ceil(total / (filters.limit || 10)),
        },
      });
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to fetch products";

      res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: errorMessage,
      });
    }
  };

  /**
   * Update a product
   * @param req - Express request object
   * @param res - Express response object
   */
  updateProduct = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const product = await this.productService.updateProduct(id, req.body);

      if (!product) {
        res.status(StatusCodes.NOT_FOUND).json({
          success: false,
          message: "Product not found",
        });
        return;
      }

      res.status(StatusCodes.OK).json({
        success: true,
        data: product,
      });
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to update product";

      res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: errorMessage,
      });
    }
  };

  /**
   * Delete a product
   * @param req - Express request object
   * @param res - Express response object
   */
  deleteProduct = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const deleted = await this.productService.deleteProduct(id);

      if (!deleted) {
        res.status(StatusCodes.NOT_FOUND).json({
          success: false,
          message: "Product not found",
        });
        return;
      }

      res.status(StatusCodes.OK).json({
        success: true,
        message: "Product deleted successfully",
      });
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to delete product";

      res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: errorMessage,
      });
    }
  };

  /**
   * Get product for editing - returns product data with editable fields list
   * @param req - Express request object
   * @param res - Express response object
   */
  getProductForEdit = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const product = await this.productService.getProductById(id);

      if (!product) {
        res.status(StatusCodes.NOT_FOUND).json({
          success: false,
          message: "Product not found",
        });
        return;
      }

      // Define commonly editable fields (excluding ObjectId fields to avoid casting errors)
      const editableFields = [
        "name",
        "brand",
        "model",
        "description",
        "shortDescription",
        "price",
        "originalPrice",
        "currency",
        "saleEndsAt",
        "costPrice",
        "stock",
        "condition",
        "isPublished",
        "lowStockThreshold",
        "category",
        "subcategory",
        "material",
        "yearMade",
        "tags",
        "color",
        "size",
        "mainImage",
        "images",
        "location",
        "shippingCost",
        "shippingTime",
        "freeShipping",
        "status",
        "ageRestriction",
        "availableFrom",
        "availableUntil",
        "featured",
        "sticky",
        "downloadable",
        "virtual",
      ];

      res.status(StatusCodes.OK).json({
        success: true,
        data: product,
        editableFields,
        message:
          "Product retrieved for editing. Use PATCH /api/products/{id}/edit for safe updates.",
      });
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to fetch product for editing";
      res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: errorMessage,
      });
    }
  };

  /**
   * Update product with enhanced validation (PATCH method)
   * @param req - Express request object
   * @param res - Express response object
   */
  patchProduct = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const updateData = req.body;

      // Filter out ObjectId fields that might cause casting errors
      const safeUpdateData = { ...updateData };
      const objectIdFields = [
        "sellerId",
        "shopId",
        "relatedProducts",
        "crossSellProducts",
        "upSellProducts",
        "bundledProducts",
        "lastModifiedBy",
      ];

      // Remove ObjectId fields if they contain invalid values
      objectIdFields.forEach((field) => {
        if (safeUpdateData[field]) {
          if (
            typeof safeUpdateData[field] === "string" &&
            safeUpdateData[field] === "string"
          ) {
            delete safeUpdateData[field];
          } else if (Array.isArray(safeUpdateData[field])) {
            // Check if array contains placeholder strings
            const hasPlaceholders = safeUpdateData[field].some(
              (item: any) => typeof item === "string" && item === "string"
            );
            if (hasPlaceholders) {
              delete safeUpdateData[field];
            }
          }
        }
      });

      // Track which fields are being updated
      const updatedFields = Object.keys(safeUpdateData);

      const product = await this.productService.updateProduct(
        id,
        safeUpdateData
      );

      if (!product) {
        res.status(StatusCodes.NOT_FOUND).json({
          success: false,
          message: "Product not found",
        });
        return;
      }

      res.status(StatusCodes.OK).json({
        success: true,
        data: product,
        updatedFields,
        message: `Successfully updated ${updatedFields.length} field(s)`,
      });
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to update product";
      res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: `Error updating product: ${errorMessage}`,
      });
    }
  };
}
