{
  "compilerOptions": {
    "module": "ESNext", // Use ESNext module system (ES modules)
    "target": "ESNext", // Compile to the latest ECMAScript version
    "moduleResolution": "node", // Resolve modules using Node.js style
    "esModuleInterop": true, // Enables interop between ES modules and CommonJS
    "allowSyntheticDefaultImports": true, // Allows default imports from modules with no default export
    "strict": true, // Enables strict type-checking
    "skipLibCheck": true, // Skip type-checking declaration files (faster builds)
    "outDir": "./dist", // Specifies output directory for compiled files
    "baseUrl": "./",
    "paths": {
      "*": ["node_modules/*"]
    },
    "allowImportingTsExtensions": true, // Allows importing TypeScript files without specifying the extension
    "noEmit": true // Do not emit outputs
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules", "dist"]
}
