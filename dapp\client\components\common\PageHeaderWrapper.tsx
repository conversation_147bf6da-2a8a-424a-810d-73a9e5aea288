import React from "react";

type PageHeaderWrapperProps = {
  title: string;
  description: string;
  children?: React.ReactNode;
};

/**
 * A standardized page header component with title and description
 * Used across admin pages for consistent UI
 */
export const PageHeaderWrapper = ({
  title,
  description,
  children,
}: PageHeaderWrapperProps) => {
  return (
    <div className="container mx-auto py-6">
      <div className="flex flex-col items-start justify-between gap-4 border-b pb-5 sm:flex-row sm:items-center sm:gap-0">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{title}</h1>
          <p className="text-muted-foreground">{description}</p>
        </div>
        {children && <div>{children}</div>}
      </div>
    </div>
  );
};
