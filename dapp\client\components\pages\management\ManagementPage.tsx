"use client";

import { useEffect, useState } from "react";

import { Settings } from "lucide-react";

import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";

import { Brand, BrandManager } from "./BrandManager";
import { Category, CategoryManager } from "./CategoryManager";

// Local storage keys
const BRANDS_STORAGE_KEY = "product-brands";
const CATEGORIES_STORAGE_KEY = "product-categories";

/**
 * Page for managing product categories and brands
 */
export const ManagementPage = () => {
  const [brands, setBrands] = useState<Brand[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoaded, setIsLoaded] = useState(false);

  // Load data from localStorage on component mount
  useEffect(() => {
    try {
      // Load brands
      const storedBrands = localStorage.getItem(BRANDS_STORAGE_KEY);
      if (storedBrands) {
        setBrands(JSON.parse(storedBrands));
      }

      // Load categories
      const storedCategories = localStorage.getItem(CATEGORIES_STORAGE_KEY);
      if (storedCategories) {
        setCategories(JSON.parse(storedCategories));
      }
    } catch (error) {
      console.error("Error loading data from localStorage:", error);
    } finally {
      setIsLoaded(true);
    }
  }, []);

  // Save brands to localStorage when they change
  const handleBrandsChange = (newBrands: Brand[]) => {
    setBrands(newBrands);
    localStorage.setItem(BRANDS_STORAGE_KEY, JSON.stringify(newBrands));
  };

  // Save categories to localStorage when they change
  const handleCategoriesChange = (newCategories: Category[]) => {
    setCategories(newCategories);
    localStorage.setItem(CATEGORIES_STORAGE_KEY, JSON.stringify(newCategories));
  };

  // Add some sample data if none exists
  const addSampleData = () => {
    // Sample brands
    if (brands.length === 0) {
      const sampleBrands: Brand[] = [
        {
          id: "brand-1",
          name: "Apple",
          website: "https://apple.com",
          slug: "apple",
        },
        {
          id: "brand-2",
          name: "Samsung",
          website: "https://samsung.com",
          slug: "samsung",
        },
        {
          id: "brand-3",
          name: "Sony",
          website: "https://sony.com",
          slug: "sony",
        },
      ];
      handleBrandsChange(sampleBrands);
    }

    // Sample categories
    if (categories.length === 0) {
      const sampleCategories: Category[] = [
        {
          id: "category-1",
          name: "Electronics",
          description: "Electronic devices and accessories",
          slug: "electronics",
        },
        {
          id: "category-2",
          name: "Clothing",
          description: "Apparel and fashion items",
          slug: "clothing",
        },
        {
          id: "category-3",
          name: "Home & Kitchen",
          description: "Items for your home",
          slug: "home-kitchen",
        },
      ];
      handleCategoriesChange(sampleCategories);
    }
  };

  // Add sample data on first load if no data exists
  useEffect(() => {
    if (isLoaded && brands.length === 0 && categories.length === 0) {
      addSampleData();
    }
  }, [isLoaded, brands.length, categories.length]);

  // Get the tab from URL query parameter
  const [activeTab, setActiveTab] = useState<string>("categories");

  // Check for tab parameter in URL on component mount
  useEffect(() => {
    if (typeof window !== "undefined") {
      const params = new URLSearchParams(window.location.search);
      const tabParam = params.get("tab");
      if (tabParam === "brands" || tabParam === "categories") {
        setActiveTab(tabParam);
      }
    }
  }, []);

  // Update URL when tab changes
  const handleTabChange = (value: string) => {
    setActiveTab(value);
    if (typeof window !== "undefined") {
      const url = new URL(window.location.href);
      url.searchParams.set("tab", value);
      window.history.pushState({}, "", url);
    }
  };

  return (
    <div className="container mx-auto py-8">
      <div className="mb-8 flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Product Management</h1>
          <p className="text-muted-foreground">
            Manage your product categories and brands
          </p>
        </div>
        <div className="flex items-center">
          <Settings className="mr-2 h-5 w-5" />
          <span>Settings</span>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={handleTabChange}>
        <TabsList className="mb-6 grid w-full grid-cols-2">
          <TabsTrigger value="categories">Categories</TabsTrigger>
          <TabsTrigger value="brands">Brands</TabsTrigger>
        </TabsList>

        <TabsContent value="categories">
          <CategoryManager
            initialCategories={categories}
            onCategoriesChange={handleCategoriesChange}
          />
        </TabsContent>

        <TabsContent value="brands">
          <BrandManager
            initialBrands={brands}
            onBrandsChange={handleBrandsChange}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
};
