"use client";

import { useEffect, useState } from "react";

import {
  <PERSON>R<PERSON>,
  <PERSON><PERSON><PERSON>3,
  Download,
  Filter,
  Hammer,
  Layers,
  LayoutGrid,
  Package,
  Palette,
  Plus,
  Search,
  Sparkles,
  Tag,
  TrendingUp,
  Upload,
} from "lucide-react";
import { useSearchParams } from "next/navigation";

import { PageHeaderWrapper } from "@/components/common/PageHeaderWrapper";
import { BrandManagerEnhanced } from "@/components/pages/management/BrandManagerEnhanced";
import { CategoryManagerEnhanced } from "@/components/pages/management/CategoryManagerEnhanced";
import { ColorManagerEnhanced } from "@/components/pages/management/ColorManagerEnhanced";
import { MaterialManagerEnhanced } from "@/components/pages/management/MaterialManagerEnhanced";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";

export default function CatalogSettings() {
  const [activeTab, setActiveTab] = useState("overview");
  const [searchQuery, setSearchQuery] = useState("");
  const searchParams = useSearchParams();

  // Handle URL parameters for direct tab navigation
  useEffect(() => {
    const tab = searchParams.get("tab");
    if (
      tab &&
      ["overview", "categories", "brands", "materials", "colors"].includes(tab)
    ) {
      setActiveTab(tab);
    }
  }, [searchParams]);

  // Unified stats for all catalog metadata
  const overallStats = [
    {
      label: "Total Categories",
      value: "18",
      change: "+2",
      trend: "up",
      icon: LayoutGrid,
    },
    {
      label: "Total Brands",
      value: "24",
      change: "+3",
      trend: "up",
      icon: Tag,
    },
    {
      label: "Total Materials",
      value: "32",
      change: "+4",
      trend: "up",
      icon: Hammer,
    },
    {
      label: "Total Colors",
      value: "28",
      change: "+2",
      trend: "up",
      icon: Palette,
    },
  ];

  const quickActions = [
    { label: "Import Data", icon: Upload, action: () => console.log("Import") },
    {
      label: "Export All",
      icon: Download,
      action: () => console.log("Export"),
    },
    {
      label: "Catalog Analytics",
      icon: BarChart3,
      action: () => console.log("Analytics"),
    },
  ];

  // Cross-reference data showing relationships
  const catalogRelationships = [
    {
      category: "Electronics",
      brands: ["Apple", "Samsung", "Sony"],
      materials: ["Aluminum", "Glass", "Plastic"],
      colors: ["Black", "White", "Silver"],
      products: 324,
    },
    {
      category: "Clothing",
      brands: ["Nike", "Adidas", "H&M"],
      materials: ["Cotton", "Polyester", "Wool"],
      colors: ["Red", "Blue", "Green"],
      products: 289,
    },
    {
      category: "Home & Garden",
      brands: ["IKEA", "Home Depot", "Wayfair"],
      materials: ["Wood", "Metal", "Fabric"],
      colors: ["Brown", "White", "Gray"],
      products: 156,
    },
  ];

  return (
    <>
      <PageHeaderWrapper
        title="Catalog Settings"
        description="Manage categories, brands, and materials for your product catalog in one unified interface"
      >
        <div className="flex gap-2">
          {quickActions.map((action, index) => (
            <Button
              key={index}
              variant="outline"
              size="sm"
              onClick={action.action}
              className="hidden sm:flex"
            >
              <action.icon className="mr-2 h-4 w-4" />
              {action.label}
            </Button>
          ))}
          <Button size="sm">
            <Plus className="mr-2 h-4 w-4" />
            Quick Add
          </Button>
        </div>
      </PageHeaderWrapper>

      <div className="container mx-auto mt-6 space-y-6">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="categories">Categories</TabsTrigger>
            <TabsTrigger value="brands">Brands</TabsTrigger>
            <TabsTrigger value="materials">Materials</TabsTrigger>
            <TabsTrigger value="colors">Colors</TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            {/* Overall Stats */}
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              {overallStats.map((stat, index) => (
                <Card key={index}>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium text-gray-600">
                      {stat.label}
                    </CardTitle>
                    <stat.icon className="h-4 w-4 text-blue-600" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-gray-900">
                      {stat.value}
                    </div>
                    <div className="mt-1 flex items-center text-xs text-gray-500">
                      {stat.trend === "up" && (
                        <TrendingUp className="mr-1 h-3 w-3 text-green-500" />
                      )}
                      <span>{stat.change}</span>
                      {stat.trend === "up" && (
                        <span className="ml-1">this month</span>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Catalog Relationships */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Layers className="h-5 w-5 text-blue-600" />
                  Catalog Relationships
                </CardTitle>
                <p className="text-sm text-gray-600">
                  See how categories, brands, and materials work together in
                  your catalog
                </p>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {catalogRelationships.map((item, index) => (
                    <div
                      key={index}
                      className="rounded-lg border bg-gray-50 p-4"
                    >
                      <div className="mb-3 flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <LayoutGrid className="h-5 w-5 text-blue-600" />
                          <span className="font-semibold text-gray-900">
                            {item.category}
                          </span>
                          <Badge variant="secondary">
                            {item.products} products
                          </Badge>
                        </div>
                      </div>

                      <div className="grid gap-4 md:grid-cols-2">
                        <div>
                          <div className="mb-2 flex items-center gap-2">
                            <Tag className="h-4 w-4 text-green-600" />
                            <span className="text-sm font-medium text-gray-700">
                              Brands
                            </span>
                          </div>
                          <div className="flex flex-wrap gap-1">
                            {item.brands.map((brand, idx) => (
                              <Badge
                                key={idx}
                                variant="outline"
                                className="text-xs"
                              >
                                {brand}
                              </Badge>
                            ))}
                          </div>
                        </div>

                        <div>
                          <div className="mb-2 flex items-center gap-2">
                            <Hammer className="h-4 w-4 text-purple-600" />
                            <span className="text-sm font-medium text-gray-700">
                              Materials
                            </span>
                          </div>
                          <div className="flex flex-wrap gap-1">
                            {item.materials.map((material, idx) => (
                              <Badge
                                key={idx}
                                variant="outline"
                                className="text-xs"
                              >
                                {material}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      </div>

                      <div className="mt-4">
                        <div className="mb-2 flex items-center gap-2">
                          <Palette className="h-4 w-4 text-pink-600" />
                          <span className="text-sm font-medium text-gray-700">
                            Colors
                          </span>
                        </div>
                        <div className="flex flex-wrap gap-1">
                          {item.colors.map((color, idx) => (
                            <Badge
                              key={idx}
                              variant="outline"
                              className="text-xs"
                            >
                              {color}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Quick Actions Grid */}
            <div className="grid gap-4 md:grid-cols-4">
              <Card
                className="cursor-pointer transition-shadow hover:shadow-md"
                onClick={() => setActiveTab("categories")}
              >
                <CardContent className="p-6 text-center">
                  <LayoutGrid className="mx-auto mb-3 h-8 w-8 text-blue-600" />
                  <h3 className="mb-2 font-semibold text-gray-900">
                    Manage Categories
                  </h3>
                  <p className="mb-3 text-sm text-gray-600">
                    Organize your product catalog
                  </p>
                  <div className="flex items-center justify-center text-sm text-blue-600">
                    <span>Go to Categories</span>
                    <ArrowRight className="ml-1 h-4 w-4" />
                  </div>
                </CardContent>
              </Card>

              <Card
                className="cursor-pointer transition-shadow hover:shadow-md"
                onClick={() => setActiveTab("brands")}
              >
                <CardContent className="p-6 text-center">
                  <Tag className="mx-auto mb-3 h-8 w-8 text-green-600" />
                  <h3 className="mb-2 font-semibold text-gray-900">
                    Manage Brands
                  </h3>
                  <p className="mb-3 text-sm text-gray-600">
                    Add and organize product brands
                  </p>
                  <div className="flex items-center justify-center text-sm text-green-600">
                    <span>Go to Brands</span>
                    <ArrowRight className="ml-1 h-4 w-4" />
                  </div>
                </CardContent>
              </Card>

              <Card
                className="cursor-pointer transition-shadow hover:shadow-md"
                onClick={() => setActiveTab("materials")}
              >
                <CardContent className="p-6 text-center">
                  <Hammer className="mx-auto mb-3 h-8 w-8 text-purple-600" />
                  <h3 className="mb-2 font-semibold text-gray-900">
                    Manage Materials
                  </h3>
                  <p className="mb-3 text-sm text-gray-600">
                    Define product materials
                  </p>
                  <div className="flex items-center justify-center text-sm text-purple-600">
                    <span>Go to Materials</span>
                    <ArrowRight className="ml-1 h-4 w-4" />
                  </div>
                </CardContent>
              </Card>

              <Card
                className="cursor-pointer transition-shadow hover:shadow-md"
                onClick={() => setActiveTab("colors")}
              >
                <CardContent className="p-6 text-center">
                  <Palette className="mx-auto mb-3 h-8 w-8 text-pink-600" />
                  <h3 className="mb-2 font-semibold text-gray-900">
                    Manage Colors
                  </h3>
                  <p className="mb-3 text-sm text-gray-600">
                    Define product color options
                  </p>
                  <div className="flex items-center justify-center text-sm text-pink-600">
                    <span>Go to Colors</span>
                    <ArrowRight className="ml-1 h-4 w-4" />
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Categories Tab */}
          <TabsContent value="categories" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <LayoutGrid className="h-5 w-5 text-blue-600" />
                  Category Management
                </CardTitle>
                <p className="text-sm text-gray-600">
                  Organize your products with categories and subcategories for
                  better navigation
                </p>
              </CardHeader>
              <CardContent>
                <CategoryManagerEnhanced />
              </CardContent>
            </Card>
          </TabsContent>

          {/* Brands Tab */}
          <TabsContent value="brands" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Tag className="h-5 w-5 text-green-600" />
                  Brand Management
                </CardTitle>
                <p className="text-sm text-gray-600">
                  Create, organize, and manage product brands for your catalog
                </p>
              </CardHeader>
              <CardContent>
                <BrandManagerEnhanced />
              </CardContent>
            </Card>
          </TabsContent>

          {/* Materials Tab */}
          <TabsContent value="materials" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Hammer className="h-5 w-5 text-purple-600" />
                  Material Management
                </CardTitle>
                <p className="text-sm text-gray-600">
                  Define and manage materials used in your products for better
                  specifications
                </p>
              </CardHeader>
              <CardContent>
                <MaterialManagerEnhanced />
              </CardContent>
            </Card>
          </TabsContent>

          {/* Colors Tab */}
          <TabsContent value="colors" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Palette className="h-5 w-5 text-pink-600" />
                  Color Management
                </CardTitle>
                <p className="text-sm text-gray-600">
                  Create and manage color options for your products to enhance
                  customer choice and visual appeal
                </p>
              </CardHeader>
              <CardContent>
                <ColorManagerEnhanced />
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </>
  );
}
