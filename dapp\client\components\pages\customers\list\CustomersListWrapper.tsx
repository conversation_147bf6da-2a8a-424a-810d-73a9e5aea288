"use client";

import React from "react";

import { Card, CardContent } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";

import { CustomersFilter } from "./CustomersFilter";
import { CustomersTable } from "./CustomersTable";
import { CustomersPagination } from "./CustomersPagination";

export const CustomersListWrapper = () => {
  return (
    <div className="space-y-6">
      <Card>
        <CardContent className="p-6">
          <CustomersFilter />

          <Separator className="my-3 mb-6" />

          <CustomersTable />
        </CardContent>
      </Card>

      <CustomersPagination />
    </div>
  );
};
