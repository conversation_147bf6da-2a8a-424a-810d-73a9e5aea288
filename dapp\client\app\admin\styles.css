/* Fix for HTML and body height to ensure background color extends fully */
html,
body {
  height: 100%;
  min-height: 100%;
  margin: 0;
  padding: 0;
  overflow-x: hidden;
  background-color: hsl(var(--background));
}

/* Ensure the main container takes full height */
#__next,
main {
  min-height: 100%;
}

/* Ensure sidebar stays on top */
aside {
  z-index: 40;
}

/* Ensure content area scrolls properly */
.overflow-y-auto {
  -webkit-overflow-scrolling: touch;
}
