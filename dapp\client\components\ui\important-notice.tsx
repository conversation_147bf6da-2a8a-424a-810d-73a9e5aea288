import { AlertCircle } from "lucide-react";

type ImportantNoticeProps = {
  title?: string;
  description: string;
  requiredFields: string[];
  tip?: string;
  variant?: "amber" | "blue" | "red" | "green";
};

export const ImportantNotice = ({
  title = "Required Information Notice",
  description,
  requiredFields,
  tip,
  variant = "amber",
}: ImportantNoticeProps) => {
  // Define color schemes for different variants
  const colorSchemes = {
    amber: {
      border: "border-amber-200",
      background: "bg-amber-50",
      icon: "text-amber-600",
      title: "text-amber-900",
      description: "text-amber-800",
      tip: "text-amber-700",
    },
    blue: {
      border: "border-blue-200",
      background: "bg-blue-50",
      icon: "text-blue-600",
      title: "text-blue-900",
      description: "text-blue-800",
      tip: "text-blue-700",
    },
    red: {
      border: "border-red-200",
      background: "bg-red-50",
      icon: "text-red-600",
      title: "text-red-900",
      description: "text-red-800",
      tip: "text-red-700",
    },
    green: {
      border: "border-green-200",
      background: "bg-green-50",
      icon: "text-green-600",
      title: "text-green-900",
      description: "text-green-800",
      tip: "text-green-700",
    },
  };

  const colors = colorSchemes[variant];

  return (
    <div className={`rounded-lg border ${colors.border} ${colors.background} p-4`}>
      <div className="flex items-start gap-3">
        <AlertCircle className={`mt-0.5 h-5 w-5 ${colors.icon}`} />
        <div>
          <h3 className={`font-semibold ${colors.title}`}>{title}</h3>
          <p className={`mt-1 text-sm ${colors.description}`}>
            {description}{" "}
            {requiredFields.length > 0 && (
              <>
                This includes{" "}
                {requiredFields.map((field, index) => (
                  <span key={field}>
                    <strong>{field}</strong>
                    {index < requiredFields.length - 2 && ", "}
                    {index === requiredFields.length - 2 && ", and "}
                    {index === requiredFields.length - 1 && "."}
                  </span>
                ))}
              </>
            )}
          </p>
          {tip && (
            <div className={`mt-2 text-xs ${colors.tip}`}>
              💡 <strong>Tip:</strong> {tip}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
