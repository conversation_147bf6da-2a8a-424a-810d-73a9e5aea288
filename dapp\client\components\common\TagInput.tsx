"use client";

import { ChangeEvent, KeyboardEvent, useEffect, useRef, useState } from "react";

import { X } from "lucide-react";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";

type TagInputProps = {
  tags: string[];
  setTags: (tags: string[]) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  maxTags?: number;
};

export const TagInput = ({
  tags,
  setTags,
  placeholder = "Add tags...",
  className,
  disabled = false,
  maxTags = 5,
}: TagInputProps) => {
  const [inputValue, setInputValue] = useState<string>("");
  const inputRef = useRef<HTMLInputElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  const handleInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
  };

  const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
    if ((e.key === "Enter" || e.key === ",") && inputValue.trim()) {
      e.preventDefault();
      addTag(inputValue);
    } else if (e.key === "Backspace" && !inputValue && tags.length > 0) {
      removeTag(tags.length - 1);
    }
  };

  const handleBlur = () => {
    if (inputValue.trim()) {
      addTag(inputValue);
    }
  };

  const addTag = (value: string) => {
    const processedValue = value.replace(/,/g, "").trim();

    if (
      processedValue &&
      !tags.includes(processedValue) &&
      tags.length < maxTags
    ) {
      setTags([...tags, processedValue]);
      setInputValue("");
    }
  };

  const removeTag = (indexToRemove: number) => {
    setTags(tags.filter((_, index) => index !== indexToRemove));
  };

  const focusInput = () => {
    if (!disabled) {
      inputRef.current?.focus();
    }
  };

  useEffect(() => {
    if (containerRef.current?.contains(document.activeElement)) {
      inputRef.current?.focus();
    }
  }, [tags]);

  return (
    <div
      ref={containerRef}
      onClick={focusInput}
      className={cn(
        "flex min-h-10 flex-wrap items-center justify-between gap-4 rounded-md border border-input bg-background px-3 py-2",
        disabled && "cursor-not-allowed opacity-50",
        className
      )}
    >
      <div className="flex flex-wrap gap-2">
        {tags.map((tag, index) => (
          <Badge
            key={index}
            variant="secondary"
            className="flex h-8 flex-wrap items-center gap-3 px-2 py-0 text-sm"
          >
            <span className="capitalize">{tag}</span>

            <Button
              type="button"
              onClick={(e) => {
                e.stopPropagation();
                if (!disabled) removeTag(index);
              }}
              className={cn(
                "size-auto items-center justify-center rounded-full p-0 hover:opacity-70",
                disabled && "cursor-not-allowed"
              )}
              disabled={disabled}
              aria-label={`Remove ${tag} tag`}
            >
              <X size={12} />
            </Button>
          </Badge>
        ))}
      </div>

      <Input
        ref={inputRef}
        type="text"
        value={inputValue}
        onChange={handleInputChange}
        onKeyDown={handleKeyDown}
        onBlur={handleBlur}
        disabled={disabled || tags.length >= maxTags}
        className="w-auto place-self-end bg-transparent outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed"
        placeholder={
          tags.length === 0 ? placeholder : "Add tag and press Enter"
        }
      />
    </div>
  );
};
