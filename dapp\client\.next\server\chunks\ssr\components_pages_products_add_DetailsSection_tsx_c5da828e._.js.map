{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/pages/products/add/DetailsSection.tsx"], "sourcesContent": ["import { useEffect, useState } from \"react\";\r\n\r\nimport {\r\n  Boxes,\r\n  Calendar,\r\n  ExternalLink,\r\n  Grid3X3,\r\n  Info,\r\n  Layers,\r\n  Package,\r\n  Palette,\r\n  Ruler,\r\n  Sparkles,\r\n  Tag,\r\n  Weight,\r\n} from \"lucide-react\";\r\nimport {\r\n  FieldErrors,\r\n  UseFormRegister,\r\n  UseFormSetValue,\r\n  UseFormWatch,\r\n} from \"react-hook-form\";\r\n\r\nimport { Category } from \"@/components/pages/management/CategoryManager\";\r\nimport { Color } from \"@/components/pages/management/ColorManagerEnhanced\";\r\nimport { Material } from \"@/components/pages/management/MaterialManager\";\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\r\nimport { CollapsibleSection } from \"@/components/ui/collapsible-section\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Label } from \"@/components/ui/label\";\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from \"@/components/ui/select\";\r\nimport { Switch } from \"@/components/ui/switch\";\r\nimport {\r\n  ProductFormData,\r\n  dimensionUnitValues,\r\n  weightUnitValues,\r\n} from \"@/schemas/productSchema\";\r\nimport { DimensionUnit } from \"@/types/common\";\r\n\r\nimport { FormField } from \"../../../common/FormField\";\r\nimport { TagInput } from \"../../../common/TagInput\";\r\nimport { ImportantNotice } from \"../../../ui/important-notice\";\r\n\r\ntype DetailsSectionProps = {\r\n  register: UseFormRegister<ProductFormData>;\r\n  errors: FieldErrors<ProductFormData>;\r\n  setValue: UseFormSetValue<ProductFormData>;\r\n  watch: UseFormWatch<ProductFormData>;\r\n  tags: string[];\r\n  setTags: (tags: string[]) => void;\r\n};\r\n\r\nexport const DetailsSection = ({\r\n  register,\r\n  errors,\r\n  setValue,\r\n  watch,\r\n  tags,\r\n  setTags,\r\n}: DetailsSectionProps) => {\r\n  const watchedDimensions = watch(\"dimensions\");\r\n  const watchedWeight = watch(\"weight\");\r\n\r\n  const [hasDimensions, setHasDimensions] = useState(false);\r\n  const [hasWeight, setHasWeight] = useState(false);\r\n  const [categories, setCategories] = useState<Category[]>([]);\r\n  const [materials, setMaterials] = useState<Material[]>([]);\r\n  const [colors, setColors] = useState<Color[]>([]);\r\n\r\n  // Load categories, materials, and colors from localStorage\r\n  useEffect(() => {\r\n    try {\r\n      // Load categories\r\n      const storedCategories = localStorage.getItem(\"product-categories\");\r\n      if (storedCategories) {\r\n        setCategories(JSON.parse(storedCategories));\r\n      }\r\n\r\n      // Load materials\r\n      const storedMaterials = localStorage.getItem(\"product-materials\");\r\n      if (storedMaterials) {\r\n        setMaterials(JSON.parse(storedMaterials));\r\n      }\r\n\r\n      // Load colors\r\n      const storedColors = localStorage.getItem(\"product-colors\");\r\n      if (storedColors) {\r\n        setColors(JSON.parse(storedColors));\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error loading data from localStorage:\", error);\r\n    }\r\n  }, []);\r\n\r\n  const handleNestedSelectChange = (\r\n    path: keyof ProductFormData,\r\n    field: string,\r\n    value: string | number\r\n  ) => {\r\n    const currentNestedObject = watch(path) || {};\r\n    setValue(path, {\r\n      ...currentNestedObject,\r\n      [field]: value,\r\n    } as DimensionUnit);\r\n  };\r\n\r\n  const resetDimensions = () => {\r\n    setValue(\"dimensions\", undefined);\r\n  };\r\n\r\n  const resetWeight = () => {\r\n    setValue(\"weight\", undefined);\r\n  };\r\n\r\n  const handleTagsChange = (newTags: string[]) => {\r\n    setTags(newTags);\r\n    setValue(\"tags\", newTags);\r\n  };\r\n\r\n  return (\r\n    <div className=\"space-y-8\">\r\n      {/* Section Header */}\r\n      <div className=\"flex items-center gap-3 border-b border-gray-200 pb-4\">\r\n        <div className=\"rounded-full bg-orange-100 p-2\">\r\n          <Package className=\"h-5 w-5 text-orange-600\" />\r\n        </div>\r\n        <div>\r\n          <h2 className=\"text-2xl font-bold text-gray-900\">Product Details</h2>\r\n          <p className=\"text-gray-600\">\r\n            Configure product specifications and attributes\r\n          </p>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Important Notice */}\r\n      <ImportantNotice\r\n        description=\"You must complete all required fields in this section before proceeding to the next step.\"\r\n        requiredFields={[\"Category\", \"Material\", \"Product Tags\", \"Color\"]}\r\n        tip=\"Category helps organize products for customers, Material information is essential for business intelligence, Tags improve search visibility, and Color enhances product presentation and customer choice.\"\r\n        variant=\"amber\"\r\n      />\r\n\r\n      {/* Category Card */}\r\n      <Card className=\"border-l-4 border-l-blue-500\">\r\n        <CardHeader className=\"pb-3\">\r\n          <CardTitle className=\"flex items-center gap-2 text-lg\">\r\n            <Layers className=\"h-5 w-5 text-blue-600\" />\r\n            Category\r\n            <Badge variant=\"destructive\" className=\"text-xs\">\r\n              Required\r\n            </Badge>\r\n          </CardTitle>\r\n          <p className=\"text-sm text-gray-600\">\r\n            Choose the main category for this product\r\n          </p>\r\n        </CardHeader>\r\n        <CardContent>\r\n          <FormField\r\n            id=\"category\"\r\n            label=\"\"\r\n            error={errors.category?.message}\r\n            optional={false}\r\n          >\r\n            <Select\r\n              onValueChange={(value) => setValue(\"category\", value)}\r\n              value={watch(\"category\")}\r\n            >\r\n              <SelectTrigger className=\"border-2 p-4 text-lg focus:border-blue-500\">\r\n                <SelectValue placeholder=\"Select a category...\" />\r\n              </SelectTrigger>\r\n              <SelectContent>\r\n                {categories.length === 0 ? (\r\n                  <SelectItem value=\"no-categories\" disabled>\r\n                    No categories available\r\n                  </SelectItem>\r\n                ) : (\r\n                  categories.map((category) => (\r\n                    <SelectItem key={category.id} value={category.name}>\r\n                      <div className=\"flex items-center gap-2\">\r\n                        <Layers className=\"h-4 w-4\" />\r\n                        <span className=\"font-medium\">{category.name}</span>\r\n                      </div>\r\n                    </SelectItem>\r\n                  ))\r\n                )}\r\n                <SelectItem value=\"other\">\r\n                  <div className=\"flex items-center gap-2\">\r\n                    <Sparkles className=\"h-4 w-4\" />\r\n                    <span className=\"font-medium\">Other (Custom)</span>\r\n                  </div>\r\n                </SelectItem>\r\n              </SelectContent>\r\n            </Select>\r\n            {watch(\"category\") === \"other\" && (\r\n              <Input\r\n                className=\"mt-3 border-2 focus:border-blue-500\"\r\n                id=\"custom-category\"\r\n                {...register(\"category\")}\r\n                placeholder=\"Enter custom category name\"\r\n              />\r\n            )}\r\n          </FormField>\r\n\r\n          <div className=\"mt-3 rounded-lg bg-blue-50 p-3\">\r\n            <div className=\"flex items-start gap-2\">\r\n              <ExternalLink className=\"mt-0.5 h-4 w-4 text-blue-600\" />\r\n              <div className=\"text-sm text-blue-800\">\r\n                <strong>Tip:</strong> Need to add new categories?{\" \"}\r\n                <a\r\n                  href=\"/admin/products/categories\"\r\n                  target=\"_blank\"\r\n                  className=\"font-medium underline hover:no-underline\"\r\n                >\r\n                  Manage categories here\r\n                </a>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n\r\n      {/* Material Card */}\r\n      <Card className=\"border-l-4 border-l-green-500\">\r\n        <CardHeader className=\"pb-3\">\r\n          <CardTitle className=\"flex items-center gap-2 text-lg\">\r\n            <Palette className=\"h-5 w-5 text-green-600\" />\r\n            Material\r\n            <Badge variant=\"destructive\" className=\"text-xs\">\r\n              Required\r\n            </Badge>\r\n          </CardTitle>\r\n          <p className=\"text-sm text-gray-600\">\r\n            Specify the material composition of this product\r\n          </p>\r\n        </CardHeader>\r\n        <CardContent>\r\n          <FormField\r\n            id=\"material\"\r\n            label=\"\"\r\n            error={errors.material?.message}\r\n            optional={false}\r\n          >\r\n            <Select\r\n              onValueChange={(value) => setValue(\"material\", value)}\r\n              value={watch(\"material\")}\r\n            >\r\n              <SelectTrigger className=\"border-2 p-4 text-lg focus:border-green-500\">\r\n                <SelectValue placeholder=\"Select a material...\" />\r\n              </SelectTrigger>\r\n              <SelectContent>\r\n                {materials.length === 0 ? (\r\n                  <SelectItem value=\"no-materials\" disabled>\r\n                    No materials available\r\n                  </SelectItem>\r\n                ) : (\r\n                  materials.map((material) => (\r\n                    <SelectItem key={material.id} value={material.name}>\r\n                      <div className=\"flex items-center gap-2\">\r\n                        <Palette className=\"h-4 w-4\" />\r\n                        <span className=\"font-medium\">{material.name}</span>\r\n                      </div>\r\n                    </SelectItem>\r\n                  ))\r\n                )}\r\n                <SelectItem value=\"other\">\r\n                  <div className=\"flex items-center gap-2\">\r\n                    <Sparkles className=\"h-4 w-4\" />\r\n                    <span className=\"font-medium\">Other (Custom)</span>\r\n                  </div>\r\n                </SelectItem>\r\n              </SelectContent>\r\n            </Select>\r\n            {watch(\"material\") === \"other\" && (\r\n              <Input\r\n                className=\"mt-3 border-2 focus:border-green-500\"\r\n                id=\"custom-material\"\r\n                {...register(\"material\")}\r\n                placeholder=\"Enter custom material name\"\r\n              />\r\n            )}\r\n          </FormField>\r\n\r\n          <div className=\"mt-3 rounded-lg bg-green-50 p-3\">\r\n            <div className=\"flex items-start gap-2\">\r\n              <ExternalLink className=\"mt-0.5 h-4 w-4 text-green-600\" />\r\n              <div className=\"text-sm text-green-800\">\r\n                <strong>Tip:</strong> Need to add new materials?{\" \"}\r\n                <a\r\n                  href=\"/admin/products/materials\"\r\n                  target=\"_blank\"\r\n                  className=\"font-medium underline hover:no-underline\"\r\n                >\r\n                  Manage materials here\r\n                </a>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n\r\n      {/* Color Card */}\r\n      <Card className=\"border-l-4 border-l-pink-500\">\r\n        <CardHeader className=\"pb-3\">\r\n          <CardTitle className=\"flex items-center gap-2 text-lg\">\r\n            <Palette className=\"h-5 w-5 text-pink-600\" />\r\n            Color\r\n            <Badge variant=\"destructive\" className=\"text-xs\">\r\n              Required\r\n            </Badge>\r\n          </CardTitle>\r\n          <p className=\"text-sm text-gray-600\">\r\n            Choose the primary color for this product\r\n          </p>\r\n        </CardHeader>\r\n        <CardContent>\r\n          <FormField\r\n            id=\"color\"\r\n            label=\"\"\r\n            error={errors.color?.message}\r\n            optional={false}\r\n          >\r\n            <Select\r\n              onValueChange={(value) => setValue(\"color\", value)}\r\n              value={watch(\"color\")}\r\n            >\r\n              <SelectTrigger className=\"border-2 p-4 text-lg focus:border-pink-500\">\r\n                <SelectValue placeholder=\"Select a color...\" />\r\n              </SelectTrigger>\r\n              <SelectContent>\r\n                {colors.length === 0 ? (\r\n                  <SelectItem value=\"no-colors\" disabled>\r\n                    <div className=\"flex items-center gap-2 text-gray-500\">\r\n                      <Palette className=\"h-4 w-4\" />\r\n                      <span>No colors available</span>\r\n                    </div>\r\n                  </SelectItem>\r\n                ) : (\r\n                  colors\r\n                    .filter((color) => color.isActive)\r\n                    .map((color) => (\r\n                      <SelectItem key={color.id} value={color.name}>\r\n                        <div className=\"flex items-center gap-2\">\r\n                          <div\r\n                            className=\"h-4 w-4 rounded border border-gray-300\"\r\n                            style={{ backgroundColor: color.hexCode }}\r\n                          />\r\n                          <span className=\"font-medium\">{color.name}</span>\r\n                          <span className=\"text-xs text-gray-500\">\r\n                            {color.hexCode}\r\n                          </span>\r\n                        </div>\r\n                      </SelectItem>\r\n                    ))\r\n                )}\r\n                <SelectItem value=\"other\">\r\n                  <div className=\"flex items-center gap-2\">\r\n                    <Sparkles className=\"h-4 w-4\" />\r\n                    <span className=\"font-medium\">Other (Custom)</span>\r\n                  </div>\r\n                </SelectItem>\r\n              </SelectContent>\r\n            </Select>\r\n          </FormField>\r\n\r\n          <div className=\"mt-3 rounded-lg bg-pink-50 p-3\">\r\n            <div className=\"flex items-start gap-2\">\r\n              <ExternalLink className=\"mt-0.5 h-4 w-4 text-pink-600\" />\r\n              <div className=\"text-sm text-pink-800\">\r\n                <strong>Tip:</strong> Need to add new colors?{\" \"}\r\n                <a\r\n                  href=\"/admin/products/catalog-settings?tab=colors\"\r\n                  target=\"_blank\"\r\n                  className=\"font-medium underline hover:no-underline\"\r\n                >\r\n                  Manage colors here\r\n                </a>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n\r\n      {/* Product Tags Card */}\r\n      <Card className=\"border-l-4 border-l-indigo-500\">\r\n        <CardHeader className=\"pb-3\">\r\n          <CardTitle className=\"flex items-center gap-2 text-lg\">\r\n            <Tag className=\"h-5 w-5 text-indigo-600\" />\r\n            Product Tags\r\n            <Badge variant=\"destructive\" className=\"text-xs\">\r\n              Required\r\n            </Badge>\r\n          </CardTitle>\r\n          <p className=\"text-sm text-gray-600\">\r\n            Add searchable tags to help customers find your product\r\n          </p>\r\n        </CardHeader>\r\n        <CardContent>\r\n          <FormField\r\n            id=\"tags\"\r\n            label=\"\"\r\n            error={errors.tags?.message as string}\r\n            optional={false}\r\n          >\r\n            <TagInput\r\n              tags={tags}\r\n              setTags={handleTagsChange}\r\n              placeholder=\"Type tag and press Enter...\"\r\n            />\r\n          </FormField>\r\n\r\n          <div className=\"mt-3 rounded-lg border border-indigo-200 bg-indigo-50 p-3\">\r\n            <div className=\"flex items-start gap-2\">\r\n              <Tag className=\"mt-0.5 h-4 w-4 text-indigo-600\" />\r\n              <div className=\"text-sm text-indigo-800\">\r\n                <strong>SEO Tip:</strong> Use relevant keywords as tags to\r\n                improve search visibility. Click the × to remove tags.\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Tag Examples */}\r\n          {tags.length === 0 && (\r\n            <div className=\"mt-3 rounded-lg border border-gray-200 bg-gray-50 p-3\">\r\n              <div className=\"flex items-start gap-2\">\r\n                <Sparkles className=\"mt-0.5 h-4 w-4 text-gray-600\" />\r\n                <div className=\"text-sm text-gray-700\">\r\n                  <strong>Examples:</strong> wireless, bluetooth, waterproof,\r\n                  premium, eco-friendly, bestseller\r\n                </div>\r\n              </div>\r\n            </div>\r\n          )}\r\n        </CardContent>\r\n      </Card>\r\n\r\n      {/* Dimensions Card */}\r\n      <Card className=\"border-l-4 border-l-purple-500\">\r\n        <CardHeader className=\"pb-3\">\r\n          <CardTitle className=\"flex items-center gap-2 text-lg\">\r\n            <Ruler className=\"h-5 w-5 text-purple-600\" />\r\n            Dimensions\r\n            <Badge variant=\"secondary\" className=\"text-xs\">\r\n              Optional\r\n            </Badge>\r\n          </CardTitle>\r\n          <p className=\"text-sm text-gray-600\">\r\n            Add physical dimensions for shipping calculations\r\n          </p>\r\n        </CardHeader>\r\n        <CardContent>\r\n          <div className=\"space-y-4\">\r\n            {/* Dimensions Toggle */}\r\n            <div className=\"flex items-center space-x-3\">\r\n              <Switch\r\n                id=\"dimensions-toggle\"\r\n                checked={hasDimensions}\r\n                onCheckedChange={(checked) => {\r\n                  setHasDimensions(checked);\r\n                  if (!checked) resetDimensions();\r\n                }}\r\n              />\r\n              <Label\r\n                htmlFor=\"dimensions-toggle\"\r\n                className=\"text-base font-medium\"\r\n              >\r\n                Add product dimensions\r\n              </Label>\r\n            </div>\r\n\r\n            {/* Dimensions Section */}\r\n            {hasDimensions && (\r\n              <div className=\"space-y-4 rounded-lg border border-purple-200 bg-purple-50 p-4\">\r\n                <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-4\">\r\n                  <FormField\r\n                    id=\"dimensions.width\"\r\n                    label=\"Width\"\r\n                    error={errors.dimensions?.width?.message}\r\n                    className=\"col-span-1\"\r\n                  >\r\n                    <Input\r\n                      id=\"dimensions.width\"\r\n                      type=\"number\"\r\n                      step=\"0.01\"\r\n                      {...register(\"dimensions.width\", { valueAsNumber: true })}\r\n                      placeholder=\"0.00\"\r\n                      className=\"border-2 focus:border-purple-500\"\r\n                    />\r\n                  </FormField>\r\n\r\n                  <FormField\r\n                    id=\"dimensions.height\"\r\n                    label=\"Height\"\r\n                    error={errors.dimensions?.height?.message}\r\n                    className=\"col-span-1\"\r\n                  >\r\n                    <Input\r\n                      id=\"dimensions.height\"\r\n                      type=\"number\"\r\n                      step=\"0.01\"\r\n                      {...register(\"dimensions.height\", {\r\n                        valueAsNumber: true,\r\n                      })}\r\n                      placeholder=\"0.00\"\r\n                      className=\"border-2 focus:border-purple-500\"\r\n                    />\r\n                  </FormField>\r\n\r\n                  <FormField\r\n                    id=\"dimensions.depth\"\r\n                    label=\"Depth\"\r\n                    error={errors.dimensions?.depth?.message}\r\n                    className=\"col-span-1\"\r\n                  >\r\n                    <Input\r\n                      id=\"dimensions.depth\"\r\n                      type=\"number\"\r\n                      step=\"0.01\"\r\n                      {...register(\"dimensions.depth\", { valueAsNumber: true })}\r\n                      placeholder=\"0.00\"\r\n                      className=\"border-2 focus:border-purple-500\"\r\n                    />\r\n                  </FormField>\r\n\r\n                  <FormField\r\n                    id=\"dimensions.unit\"\r\n                    label=\"Unit\"\r\n                    error={errors.dimensions?.unit?.message}\r\n                    className=\"col-span-1\"\r\n                  >\r\n                    <Select\r\n                      onValueChange={(value) =>\r\n                        handleNestedSelectChange(\"dimensions\", \"unit\", value)\r\n                      }\r\n                      value={watchedDimensions?.unit}\r\n                    >\r\n                      <SelectTrigger className=\"border-2 focus:border-purple-500\">\r\n                        <SelectValue placeholder=\"Select unit\" />\r\n                      </SelectTrigger>\r\n                      <SelectContent>\r\n                        {dimensionUnitValues.map((unit) => (\r\n                          <SelectItem key={unit} value={unit}>\r\n                            <div className=\"flex items-center gap-2\">\r\n                              <Ruler className=\"h-4 w-4\" />\r\n                              <span className=\"font-medium\">{unit}</span>\r\n                            </div>\r\n                          </SelectItem>\r\n                        ))}\r\n                      </SelectContent>\r\n                    </Select>\r\n                  </FormField>\r\n                </div>\r\n\r\n                <div className=\"rounded-lg border border-purple-300 bg-white p-3\">\r\n                  <div className=\"flex items-start gap-2\">\r\n                    <Info className=\"mt-0.5 h-4 w-4 text-purple-600\" />\r\n                    <div className=\"text-sm text-purple-800\">\r\n                      <strong>Shipping Tip:</strong> Accurate dimensions help\r\n                      calculate shipping costs and prevent delivery issues.\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n\r\n      {/* Weight Card */}\r\n      <Card className=\"border-l-4 border-l-teal-500\">\r\n        <CardHeader className=\"pb-3\">\r\n          <CardTitle className=\"flex items-center gap-2 text-lg\">\r\n            <Weight className=\"h-5 w-5 text-teal-600\" />\r\n            Weight\r\n            <Badge variant=\"secondary\" className=\"text-xs\">\r\n              Optional\r\n            </Badge>\r\n          </CardTitle>\r\n          <p className=\"text-sm text-gray-600\">\r\n            Add product weight for shipping calculations\r\n          </p>\r\n        </CardHeader>\r\n        <CardContent>\r\n          <div className=\"space-y-4\">\r\n            {/* Weight Toggle */}\r\n            <div className=\"flex items-center space-x-3\">\r\n              <Switch\r\n                id=\"weight-toggle\"\r\n                checked={hasWeight}\r\n                onCheckedChange={(checked) => {\r\n                  setHasWeight(checked);\r\n                  if (!checked) resetWeight();\r\n                }}\r\n              />\r\n              <Label htmlFor=\"weight-toggle\" className=\"text-base font-medium\">\r\n                Add product weight\r\n              </Label>\r\n            </div>\r\n\r\n            {/* Weight Section */}\r\n            {hasWeight && (\r\n              <div className=\"space-y-4 rounded-lg border border-teal-200 bg-teal-50 p-4\">\r\n                <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-2\">\r\n                  <FormField\r\n                    id=\"weight.value\"\r\n                    label=\"Weight Value\"\r\n                    error={errors.weight?.value?.message}\r\n                  >\r\n                    <Input\r\n                      id=\"weight.value\"\r\n                      type=\"number\"\r\n                      step=\"0.01\"\r\n                      {...register(\"weight.value\", { valueAsNumber: true })}\r\n                      placeholder=\"0.00\"\r\n                      className=\"border-2 focus:border-teal-500\"\r\n                    />\r\n                  </FormField>\r\n\r\n                  <FormField\r\n                    id=\"weight.unit\"\r\n                    label=\"Weight Unit\"\r\n                    error={errors.weight?.unit?.message}\r\n                  >\r\n                    <Select\r\n                      onValueChange={(value) =>\r\n                        handleNestedSelectChange(\"weight\", \"unit\", value)\r\n                      }\r\n                      value={watchedWeight?.unit}\r\n                    >\r\n                      <SelectTrigger className=\"border-2 focus:border-teal-500\">\r\n                        <SelectValue placeholder=\"Select unit\" />\r\n                      </SelectTrigger>\r\n                      <SelectContent>\r\n                        {weightUnitValues.map((unit) => (\r\n                          <SelectItem key={unit} value={unit}>\r\n                            <div className=\"flex items-center gap-2\">\r\n                              <Weight className=\"h-4 w-4\" />\r\n                              <span className=\"font-medium\">{unit}</span>\r\n                            </div>\r\n                          </SelectItem>\r\n                        ))}\r\n                      </SelectContent>\r\n                    </Select>\r\n                  </FormField>\r\n                </div>\r\n\r\n                <div className=\"rounded-lg border border-teal-300 bg-white p-3\">\r\n                  <div className=\"flex items-start gap-2\">\r\n                    <Info className=\"mt-0.5 h-4 w-4 text-teal-600\" />\r\n                    <div className=\"text-sm text-teal-800\">\r\n                      <strong>Shipping Tip:</strong> Accurate weight helps\r\n                      calculate shipping costs and prevents carrier surcharges.\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n\r\n      {/* Year Made - Collapsible */}\r\n      <CollapsibleSection\r\n        title=\"Year Made\"\r\n        description=\"Specify the year this product was manufactured\"\r\n        icon={<Calendar className=\"h-5 w-5 text-amber-600\" />}\r\n        borderColor=\"border-l-amber-500\"\r\n        defaultOpen={false}\r\n        isOptional={true}\r\n      >\r\n        <FormField\r\n          id=\"yearMade\"\r\n          label=\"\"\r\n          error={errors.yearMade?.message}\r\n          optional={true}\r\n        >\r\n          <Input\r\n            id=\"yearMade\"\r\n            type=\"number\"\r\n            min=\"1900\"\r\n            max={new Date().getFullYear() + 1}\r\n            {...register(\"yearMade\", { valueAsNumber: true })}\r\n            placeholder={new Date().getFullYear().toString()}\r\n            className=\"border-2 p-4 text-lg focus:border-amber-500\"\r\n          />\r\n        </FormField>\r\n\r\n        <div className=\"mt-3 rounded-lg border border-amber-200 bg-amber-50 p-3\">\r\n          <div className=\"flex items-start gap-2\">\r\n            <Calendar className=\"mt-0.5 h-4 w-4 text-amber-600\" />\r\n            <div className=\"text-sm text-amber-800\">\r\n              <strong>Note:</strong> Manufacturing year helps customers\r\n              understand product age and can affect warranty coverage.\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </CollapsibleSection>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAwBA;AACA;AACA;AACA;AACA;AACA;AAOA;AACA;AAOA;AACA;AACA;;;;;;;;;;;;;;;AAWO,MAAM,iBAAiB,CAAC,EAC7B,QAAQ,EACR,MAAM,EACN,QAAQ,EACR,KAAK,EACL,IAAI,EACJ,OAAO,EACa;IACpB,MAAM,oBAAoB,MAAM;IAChC,MAAM,gBAAgB,MAAM;IAE5B,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IAC3D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAEhD,2DAA2D;IAC3D,CAAA,GAAA,oUAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI;YACF,kBAAkB;YAClB,MAAM,mBAAmB,aAAa,OAAO,CAAC;YAC9C,IAAI,kBAAkB;gBACpB,cAAc,KAAK,KAAK,CAAC;YAC3B;YAEA,iBAAiB;YACjB,MAAM,kBAAkB,aAAa,OAAO,CAAC;YAC7C,IAAI,iBAAiB;gBACnB,aAAa,KAAK,KAAK,CAAC;YAC1B;YAEA,cAAc;YACd,MAAM,eAAe,aAAa,OAAO,CAAC;YAC1C,IAAI,cAAc;gBAChB,UAAU,KAAK,KAAK,CAAC;YACvB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;QACzD;IACF,GAAG,EAAE;IAEL,MAAM,2BAA2B,CAC/B,MACA,OACA;QAEA,MAAM,sBAAsB,MAAM,SAAS,CAAC;QAC5C,SAAS,MAAM;YACb,GAAG,mBAAmB;YACtB,CAAC,MAAM,EAAE;QACX;IACF;IAEA,MAAM,kBAAkB;QACtB,SAAS,cAAc;IACzB;IAEA,MAAM,cAAc;QAClB,SAAS,UAAU;IACrB;IAEA,MAAM,mBAAmB,CAAC;QACxB,QAAQ;QACR,SAAS,QAAQ;IACnB;IAEA,qBACE,6WAAC;QAAI,WAAU;;0BAEb,6WAAC;gBAAI,WAAU;;kCACb,6WAAC;wBAAI,WAAU;kCACb,cAAA,6WAAC,4RAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;;;;;;kCAErB,6WAAC;;0CACC,6WAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,6WAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;;;;;;;0BAOjC,6WAAC,wIAAA,CAAA,kBAAe;gBACd,aAAY;gBACZ,gBAAgB;oBAAC;oBAAY;oBAAY;oBAAgB;iBAAQ;gBACjE,KAAI;gBACJ,SAAQ;;;;;;0BAIV,6WAAC,yHAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6WAAC,yHAAA,CAAA,aAAU;wBAAC,WAAU;;0CACpB,6WAAC,yHAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,6WAAC,0RAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAA0B;kDAE5C,6WAAC,0HAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAc,WAAU;kDAAU;;;;;;;;;;;;0CAInD,6WAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAIvC,6WAAC,yHAAA,CAAA,cAAW;;0CACV,6WAAC,kIAAA,CAAA,YAAS;gCACR,IAAG;gCACH,OAAM;gCACN,OAAO,OAAO,QAAQ,EAAE;gCACxB,UAAU;;kDAEV,6WAAC,2HAAA,CAAA,SAAM;wCACL,eAAe,CAAC,QAAU,SAAS,YAAY;wCAC/C,OAAO,MAAM;;0DAEb,6WAAC,2HAAA,CAAA,gBAAa;gDAAC,WAAU;0DACvB,cAAA,6WAAC,2HAAA,CAAA,cAAW;oDAAC,aAAY;;;;;;;;;;;0DAE3B,6WAAC,2HAAA,CAAA,gBAAa;;oDACX,WAAW,MAAM,KAAK,kBACrB,6WAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;wDAAgB,QAAQ;kEAAC;;;;;+DAI3C,WAAW,GAAG,CAAC,CAAC,yBACd,6WAAC,2HAAA,CAAA,aAAU;4DAAmB,OAAO,SAAS,IAAI;sEAChD,cAAA,6WAAC;gEAAI,WAAU;;kFACb,6WAAC,0RAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;kFAClB,6WAAC;wEAAK,WAAU;kFAAe,SAAS,IAAI;;;;;;;;;;;;2DAH/B,SAAS,EAAE;;;;;kEAQhC,6WAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAChB,cAAA,6WAAC;4DAAI,WAAU;;8EACb,6WAAC,8RAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;8EACpB,6WAAC;oEAAK,WAAU;8EAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oCAKrC,MAAM,gBAAgB,yBACrB,6WAAC,0HAAA,CAAA,QAAK;wCACJ,WAAU;wCACV,IAAG;wCACF,GAAG,SAAS,WAAW;wCACxB,aAAY;;;;;;;;;;;;0CAKlB,6WAAC;gCAAI,WAAU;0CACb,cAAA,6WAAC;oCAAI,WAAU;;sDACb,6WAAC,0SAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;sDACxB,6WAAC;4CAAI,WAAU;;8DACb,6WAAC;8DAAO;;;;;;gDAAa;gDAA6B;8DAClD,6WAAC;oDACC,MAAK;oDACL,QAAO;oDACP,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUX,6WAAC,yHAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6WAAC,yHAAA,CAAA,aAAU;wBAAC,WAAU;;0CACpB,6WAAC,yHAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,6WAAC,4RAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAA2B;kDAE9C,6WAAC,0HAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAc,WAAU;kDAAU;;;;;;;;;;;;0CAInD,6WAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAIvC,6WAAC,yHAAA,CAAA,cAAW;;0CACV,6WAAC,kIAAA,CAAA,YAAS;gCACR,IAAG;gCACH,OAAM;gCACN,OAAO,OAAO,QAAQ,EAAE;gCACxB,UAAU;;kDAEV,6WAAC,2HAAA,CAAA,SAAM;wCACL,eAAe,CAAC,QAAU,SAAS,YAAY;wCAC/C,OAAO,MAAM;;0DAEb,6WAAC,2HAAA,CAAA,gBAAa;gDAAC,WAAU;0DACvB,cAAA,6WAAC,2HAAA,CAAA,cAAW;oDAAC,aAAY;;;;;;;;;;;0DAE3B,6WAAC,2HAAA,CAAA,gBAAa;;oDACX,UAAU,MAAM,KAAK,kBACpB,6WAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;wDAAe,QAAQ;kEAAC;;;;;+DAI1C,UAAU,GAAG,CAAC,CAAC,yBACb,6WAAC,2HAAA,CAAA,aAAU;4DAAmB,OAAO,SAAS,IAAI;sEAChD,cAAA,6WAAC;gEAAI,WAAU;;kFACb,6WAAC,4RAAA,CAAA,UAAO;wEAAC,WAAU;;;;;;kFACnB,6WAAC;wEAAK,WAAU;kFAAe,SAAS,IAAI;;;;;;;;;;;;2DAH/B,SAAS,EAAE;;;;;kEAQhC,6WAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAChB,cAAA,6WAAC;4DAAI,WAAU;;8EACb,6WAAC,8RAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;8EACpB,6WAAC;oEAAK,WAAU;8EAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oCAKrC,MAAM,gBAAgB,yBACrB,6WAAC,0HAAA,CAAA,QAAK;wCACJ,WAAU;wCACV,IAAG;wCACF,GAAG,SAAS,WAAW;wCACxB,aAAY;;;;;;;;;;;;0CAKlB,6WAAC;gCAAI,WAAU;0CACb,cAAA,6WAAC;oCAAI,WAAU;;sDACb,6WAAC,0SAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;sDACxB,6WAAC;4CAAI,WAAU;;8DACb,6WAAC;8DAAO;;;;;;gDAAa;gDAA4B;8DACjD,6WAAC;oDACC,MAAK;oDACL,QAAO;oDACP,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUX,6WAAC,yHAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6WAAC,yHAAA,CAAA,aAAU;wBAAC,WAAU;;0CACpB,6WAAC,yHAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,6WAAC,4RAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAA0B;kDAE7C,6WAAC,0HAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAc,WAAU;kDAAU;;;;;;;;;;;;0CAInD,6WAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAIvC,6WAAC,yHAAA,CAAA,cAAW;;0CACV,6WAAC,kIAAA,CAAA,YAAS;gCACR,IAAG;gCACH,OAAM;gCACN,OAAO,OAAO,KAAK,EAAE;gCACrB,UAAU;0CAEV,cAAA,6WAAC,2HAAA,CAAA,SAAM;oCACL,eAAe,CAAC,QAAU,SAAS,SAAS;oCAC5C,OAAO,MAAM;;sDAEb,6WAAC,2HAAA,CAAA,gBAAa;4CAAC,WAAU;sDACvB,cAAA,6WAAC,2HAAA,CAAA,cAAW;gDAAC,aAAY;;;;;;;;;;;sDAE3B,6WAAC,2HAAA,CAAA,gBAAa;;gDACX,OAAO,MAAM,KAAK,kBACjB,6WAAC,2HAAA,CAAA,aAAU;oDAAC,OAAM;oDAAY,QAAQ;8DACpC,cAAA,6WAAC;wDAAI,WAAU;;0EACb,6WAAC,4RAAA,CAAA,UAAO;gEAAC,WAAU;;;;;;0EACnB,6WAAC;0EAAK;;;;;;;;;;;;;;;;2DAIV,OACG,MAAM,CAAC,CAAC,QAAU,MAAM,QAAQ,EAChC,GAAG,CAAC,CAAC,sBACJ,6WAAC,2HAAA,CAAA,aAAU;wDAAgB,OAAO,MAAM,IAAI;kEAC1C,cAAA,6WAAC;4DAAI,WAAU;;8EACb,6WAAC;oEACC,WAAU;oEACV,OAAO;wEAAE,iBAAiB,MAAM,OAAO;oEAAC;;;;;;8EAE1C,6WAAC;oEAAK,WAAU;8EAAe,MAAM,IAAI;;;;;;8EACzC,6WAAC;oEAAK,WAAU;8EACb,MAAM,OAAO;;;;;;;;;;;;uDARH,MAAM,EAAE;;;;;8DAc/B,6WAAC,2HAAA,CAAA,aAAU;oDAAC,OAAM;8DAChB,cAAA,6WAAC;wDAAI,WAAU;;0EACb,6WAAC,8RAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,6WAAC;gEAAK,WAAU;0EAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOxC,6WAAC;gCAAI,WAAU;0CACb,cAAA,6WAAC;oCAAI,WAAU;;sDACb,6WAAC,0SAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;sDACxB,6WAAC;4CAAI,WAAU;;8DACb,6WAAC;8DAAO;;;;;;gDAAa;gDAAyB;8DAC9C,6WAAC;oDACC,MAAK;oDACL,QAAO;oDACP,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUX,6WAAC,yHAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6WAAC,yHAAA,CAAA,aAAU;wBAAC,WAAU;;0CACpB,6WAAC,yHAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,6WAAC,oRAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;oCAA4B;kDAE3C,6WAAC,0HAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAc,WAAU;kDAAU;;;;;;;;;;;;0CAInD,6WAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAIvC,6WAAC,yHAAA,CAAA,cAAW;;0CACV,6WAAC,kIAAA,CAAA,YAAS;gCACR,IAAG;gCACH,OAAM;gCACN,OAAO,OAAO,IAAI,EAAE;gCACpB,UAAU;0CAEV,cAAA,6WAAC,iIAAA,CAAA,WAAQ;oCACP,MAAM;oCACN,SAAS;oCACT,aAAY;;;;;;;;;;;0CAIhB,6WAAC;gCAAI,WAAU;0CACb,cAAA,6WAAC;oCAAI,WAAU;;sDACb,6WAAC,oRAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;sDACf,6WAAC;4CAAI,WAAU;;8DACb,6WAAC;8DAAO;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;4BAO9B,KAAK,MAAM,KAAK,mBACf,6WAAC;gCAAI,WAAU;0CACb,cAAA,6WAAC;oCAAI,WAAU;;sDACb,6WAAC,8RAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,6WAAC;4CAAI,WAAU;;8DACb,6WAAC;8DAAO;;;;;;gDAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUtC,6WAAC,yHAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6WAAC,yHAAA,CAAA,aAAU;wBAAC,WAAU;;0CACpB,6WAAC,yHAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,6WAAC,wRAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;oCAA4B;kDAE7C,6WAAC,0HAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAY,WAAU;kDAAU;;;;;;;;;;;;0CAIjD,6WAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAIvC,6WAAC,yHAAA,CAAA,cAAW;kCACV,cAAA,6WAAC;4BAAI,WAAU;;8CAEb,6WAAC;oCAAI,WAAU;;sDACb,6WAAC,2HAAA,CAAA,SAAM;4CACL,IAAG;4CACH,SAAS;4CACT,iBAAiB,CAAC;gDAChB,iBAAiB;gDACjB,IAAI,CAAC,SAAS;4CAChB;;;;;;sDAEF,6WAAC,0HAAA,CAAA,QAAK;4CACJ,SAAQ;4CACR,WAAU;sDACX;;;;;;;;;;;;gCAMF,+BACC,6WAAC;oCAAI,WAAU;;sDACb,6WAAC;4CAAI,WAAU;;8DACb,6WAAC,kIAAA,CAAA,YAAS;oDACR,IAAG;oDACH,OAAM;oDACN,OAAO,OAAO,UAAU,EAAE,OAAO;oDACjC,WAAU;8DAEV,cAAA,6WAAC,0HAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,MAAK;wDACL,MAAK;wDACJ,GAAG,SAAS,oBAAoB;4DAAE,eAAe;wDAAK,EAAE;wDACzD,aAAY;wDACZ,WAAU;;;;;;;;;;;8DAId,6WAAC,kIAAA,CAAA,YAAS;oDACR,IAAG;oDACH,OAAM;oDACN,OAAO,OAAO,UAAU,EAAE,QAAQ;oDAClC,WAAU;8DAEV,cAAA,6WAAC,0HAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,MAAK;wDACL,MAAK;wDACJ,GAAG,SAAS,qBAAqB;4DAChC,eAAe;wDACjB,EAAE;wDACF,aAAY;wDACZ,WAAU;;;;;;;;;;;8DAId,6WAAC,kIAAA,CAAA,YAAS;oDACR,IAAG;oDACH,OAAM;oDACN,OAAO,OAAO,UAAU,EAAE,OAAO;oDACjC,WAAU;8DAEV,cAAA,6WAAC,0HAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,MAAK;wDACL,MAAK;wDACJ,GAAG,SAAS,oBAAoB;4DAAE,eAAe;wDAAK,EAAE;wDACzD,aAAY;wDACZ,WAAU;;;;;;;;;;;8DAId,6WAAC,kIAAA,CAAA,YAAS;oDACR,IAAG;oDACH,OAAM;oDACN,OAAO,OAAO,UAAU,EAAE,MAAM;oDAChC,WAAU;8DAEV,cAAA,6WAAC,2HAAA,CAAA,SAAM;wDACL,eAAe,CAAC,QACd,yBAAyB,cAAc,QAAQ;wDAEjD,OAAO,mBAAmB;;0EAE1B,6WAAC,2HAAA,CAAA,gBAAa;gEAAC,WAAU;0EACvB,cAAA,6WAAC,2HAAA,CAAA,cAAW;oEAAC,aAAY;;;;;;;;;;;0EAE3B,6WAAC,2HAAA,CAAA,gBAAa;0EACX,wHAAA,CAAA,sBAAmB,CAAC,GAAG,CAAC,CAAC,qBACxB,6WAAC,2HAAA,CAAA,aAAU;wEAAY,OAAO;kFAC5B,cAAA,6WAAC;4EAAI,WAAU;;8FACb,6WAAC,wRAAA,CAAA,QAAK;oFAAC,WAAU;;;;;;8FACjB,6WAAC;oFAAK,WAAU;8FAAe;;;;;;;;;;;;uEAHlB;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAY3B,6WAAC;4CAAI,WAAU;sDACb,cAAA,6WAAC;gDAAI,WAAU;;kEACb,6WAAC,sRAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,6WAAC;wDAAI,WAAU;;0EACb,6WAAC;0EAAO;;;;;;4DAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAY9C,6WAAC,yHAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6WAAC,yHAAA,CAAA,aAAU;wBAAC,WAAU;;0CACpB,6WAAC,yHAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,6WAAC,0RAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAA0B;kDAE5C,6WAAC,0HAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAY,WAAU;kDAAU;;;;;;;;;;;;0CAIjD,6WAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAIvC,6WAAC,yHAAA,CAAA,cAAW;kCACV,cAAA,6WAAC;4BAAI,WAAU;;8CAEb,6WAAC;oCAAI,WAAU;;sDACb,6WAAC,2HAAA,CAAA,SAAM;4CACL,IAAG;4CACH,SAAS;4CACT,iBAAiB,CAAC;gDAChB,aAAa;gDACb,IAAI,CAAC,SAAS;4CAChB;;;;;;sDAEF,6WAAC,0HAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAgB,WAAU;sDAAwB;;;;;;;;;;;;gCAMlE,2BACC,6WAAC;oCAAI,WAAU;;sDACb,6WAAC;4CAAI,WAAU;;8DACb,6WAAC,kIAAA,CAAA,YAAS;oDACR,IAAG;oDACH,OAAM;oDACN,OAAO,OAAO,MAAM,EAAE,OAAO;8DAE7B,cAAA,6WAAC,0HAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,MAAK;wDACL,MAAK;wDACJ,GAAG,SAAS,gBAAgB;4DAAE,eAAe;wDAAK,EAAE;wDACrD,aAAY;wDACZ,WAAU;;;;;;;;;;;8DAId,6WAAC,kIAAA,CAAA,YAAS;oDACR,IAAG;oDACH,OAAM;oDACN,OAAO,OAAO,MAAM,EAAE,MAAM;8DAE5B,cAAA,6WAAC,2HAAA,CAAA,SAAM;wDACL,eAAe,CAAC,QACd,yBAAyB,UAAU,QAAQ;wDAE7C,OAAO,eAAe;;0EAEtB,6WAAC,2HAAA,CAAA,gBAAa;gEAAC,WAAU;0EACvB,cAAA,6WAAC,2HAAA,CAAA,cAAW;oEAAC,aAAY;;;;;;;;;;;0EAE3B,6WAAC,2HAAA,CAAA,gBAAa;0EACX,wHAAA,CAAA,mBAAgB,CAAC,GAAG,CAAC,CAAC,qBACrB,6WAAC,2HAAA,CAAA,aAAU;wEAAY,OAAO;kFAC5B,cAAA,6WAAC;4EAAI,WAAU;;8FACb,6WAAC,0RAAA,CAAA,SAAM;oFAAC,WAAU;;;;;;8FAClB,6WAAC;oFAAK,WAAU;8FAAe;;;;;;;;;;;;uEAHlB;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAY3B,6WAAC;4CAAI,WAAU;sDACb,cAAA,6WAAC;gDAAI,WAAU;;kEACb,6WAAC,sRAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,6WAAC;wDAAI,WAAU;;0EACb,6WAAC;0EAAO;;;;;;4DAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAY9C,6WAAC,2IAAA,CAAA,qBAAkB;gBACjB,OAAM;gBACN,aAAY;gBACZ,oBAAM,6WAAC,8RAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;gBAC1B,aAAY;gBACZ,aAAa;gBACb,YAAY;;kCAEZ,6WAAC,kIAAA,CAAA,YAAS;wBACR,IAAG;wBACH,OAAM;wBACN,OAAO,OAAO,QAAQ,EAAE;wBACxB,UAAU;kCAEV,cAAA,6WAAC,0HAAA,CAAA,QAAK;4BACJ,IAAG;4BACH,MAAK;4BACL,KAAI;4BACJ,KAAK,IAAI,OAAO,WAAW,KAAK;4BAC/B,GAAG,SAAS,YAAY;gCAAE,eAAe;4BAAK,EAAE;4BACjD,aAAa,IAAI,OAAO,WAAW,GAAG,QAAQ;4BAC9C,WAAU;;;;;;;;;;;kCAId,6WAAC;wBAAI,WAAU;kCACb,cAAA,6WAAC;4BAAI,WAAU;;8CACb,6WAAC,8RAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,6WAAC;oCAAI,WAAU;;sDACb,6WAAC;sDAAO;;;;;;wCAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQpC", "debugId": null}}]}