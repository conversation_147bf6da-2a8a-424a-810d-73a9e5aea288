"use client";

import React, { useState } from "react";

import {
  <PERSON><PERSON>eft,
  <PERSON><PERSON>,
  Edit,
  Eye,
  MoreHorizontal,
  Save,
  Trash2,
  X,
} from "lucide-react";
import Link from "next/link";

import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

type ProductDetailsActionsProps = {
  productId: string;
  isEditing?: boolean;
  onEditToggle?: () => void;
  onSave?: () => void;
  onCancel?: () => void;
};

export const ProductDetailsActions = ({
  productId,
  isEditing = false,
  onEditToggle,
  onSave,
  onCancel,
}: ProductDetailsActionsProps) => {
  const handleDuplicate = () => {
    console.log("Duplicate product:", productId);
    // TODO: Implement duplicate functionality
  };

  const handleDelete = () => {
    console.log("Delete product:", productId);
    // TODO: Implement delete functionality
  };

  const handleViewInStore = () => {
    console.log("View in store:", productId);
    // TODO: Implement view in store functionality
  };

  if (isEditing) {
    return (
      <div className="flex gap-2">
        <Button size="sm" onClick={onSave}>
          <Save className="mr-2 h-4 w-4" />
          Save Changes
        </Button>
        <Button variant="outline" size="sm" onClick={onCancel}>
          <X className="mr-2 h-4 w-4" />
          Cancel
        </Button>
      </div>
    );
  }

  return (
    <div className="flex gap-2">
      <Button variant="outline" size="sm" asChild>
        <Link href="/products/list">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Products
        </Link>
      </Button>

      <Button variant="outline" size="sm" onClick={onEditToggle}>
        <Edit className="mr-2 h-4 w-4" />
        Edit Product
      </Button>

      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" size="sm">
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem onClick={handleViewInStore}>
            <Eye className="mr-2 h-4 w-4" />
            View in Store
          </DropdownMenuItem>
          <DropdownMenuItem onClick={handleDuplicate}>
            <Copy className="mr-2 h-4 w-4" />
            Duplicate Product
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem className="text-red-600" onClick={handleDelete}>
            <Trash2 className="mr-2 h-4 w-4" />
            Delete Product
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};
