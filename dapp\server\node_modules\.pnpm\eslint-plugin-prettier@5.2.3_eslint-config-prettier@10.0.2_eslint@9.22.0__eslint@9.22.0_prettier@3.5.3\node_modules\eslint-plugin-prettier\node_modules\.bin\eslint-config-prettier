#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/Desktop/Learning/Codes/dapp/server/node_modules/.pnpm/eslint-config-prettier@10.0.2_eslint@9.22.0/node_modules/eslint-config-prettier/build/bin/node_modules:/mnt/c/Users/<USER>/Desktop/Learning/Codes/dapp/server/node_modules/.pnpm/eslint-config-prettier@10.0.2_eslint@9.22.0/node_modules/eslint-config-prettier/build/node_modules:/mnt/c/Users/<USER>/Desktop/Learning/Codes/dapp/server/node_modules/.pnpm/eslint-config-prettier@10.0.2_eslint@9.22.0/node_modules/eslint-config-prettier/node_modules:/mnt/c/Users/<USER>/Desktop/Learning/Codes/dapp/server/node_modules/.pnpm/eslint-config-prettier@10.0.2_eslint@9.22.0/node_modules:/mnt/c/Users/<USER>/Desktop/Learning/Codes/dapp/server/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/Desktop/Learning/Codes/dapp/server/node_modules/.pnpm/eslint-config-prettier@10.0.2_eslint@9.22.0/node_modules/eslint-config-prettier/build/bin/node_modules:/mnt/c/Users/<USER>/Desktop/Learning/Codes/dapp/server/node_modules/.pnpm/eslint-config-prettier@10.0.2_eslint@9.22.0/node_modules/eslint-config-prettier/build/node_modules:/mnt/c/Users/<USER>/Desktop/Learning/Codes/dapp/server/node_modules/.pnpm/eslint-config-prettier@10.0.2_eslint@9.22.0/node_modules/eslint-config-prettier/node_modules:/mnt/c/Users/<USER>/Desktop/Learning/Codes/dapp/server/node_modules/.pnpm/eslint-config-prettier@10.0.2_eslint@9.22.0/node_modules:/mnt/c/Users/<USER>/Desktop/Learning/Codes/dapp/server/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../eslint-config-prettier@10.0.2_eslint@9.22.0/node_modules/eslint-config-prettier/build/bin/cli.js" "$@"
else
  exec node  "$basedir/../../../../../eslint-config-prettier@10.0.2_eslint@9.22.0/node_modules/eslint-config-prettier/build/bin/cli.js" "$@"
fi
