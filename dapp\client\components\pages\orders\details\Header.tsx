import { Calendar } from "lucide-react";

import { PageTitle } from "@/components/common/PageTitle";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

export const Header = () => {
  return (
    <section className="flex flex-col gap-6">
      <PageTitle title="Order details" />

      <div className="flex flex-col justify-between rounded-lg border border-gray-200 bg-white p-6 sm:flex-row">
        <div className="mb-4 flex items-start sm:mb-0">
          <div className="mr-2 mt-1">
            <Calendar className="size-5 text-gray-500" />
          </div>

          <div>
            <p className="font-medium">Wed, Aug 13, 2020, 4:34PM</p>
            <p className="text-sm text-gray-500">#ID 3453012</p>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Select>
            <SelectTrigger className="h-10 w-[160px] border-gray-200 bg-white">
              <SelectValue placeholder="Change status" />
            </SelectTrigger>

            <SelectContent>
              <SelectItem value="pending">Pending</SelectItem>
              <SelectItem value="paid">Paid</SelectItem>
              <SelectItem value="cancelled">Cancelled</SelectItem>
            </SelectContent>
          </Select>

          <Button className="h-10 bg-blue-600 px-5 hover:bg-blue-700">
            Save
          </Button>
        </div>
      </div>
    </section>
  );
};
