import { Nfc } from "lucide-react";
import Image from "next/image";

import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";

import { InfoRow } from "./InfoRow";
import { PaymentHeader } from "./PaymentHeader";

export const PaymentAndNotesSection = () => {
  return (
    <section className="flex flex-1 flex-col gap-4">
      <Card className="rounded-lg border border-gray-200 bg-white p-5">
        <PaymentHeader icon={<Nfc className="size-5" />} title="Payment info" />

        <div className="flex flex-col gap-2">
          <div className="flex items-center gap-3">
            <Image
              src="/icons/mastercard_payment_icon.svg"
              alt="Visa"
              className="object-cover"
              width={32}
              height={32}
            />

            <span className="font-medium">Master Card **** **** 4768</span>
          </div>

          <InfoRow value="Business name: Master Card, inc." />

          <InfoRow value="Phone: +1 (800) 555-154-52" />
        </div>
      </Card>

      <Card className="rounded-lg border border-gray-200 bg-white p-5">
        <PaymentHeader title="Notes" />

        <div className="flex w-full flex-col items-end space-y-4">
          <Textarea
            placeholder="Type here"
            className="h-32 w-full resize-none border-gray-200"
          />

          <Button className="bg-blue-600 hover:bg-blue-700">Save note</Button>
        </div>
      </Card>
    </section>
  );
};
